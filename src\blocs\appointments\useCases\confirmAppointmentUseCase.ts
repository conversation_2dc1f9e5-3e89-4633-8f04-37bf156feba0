import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { IGuardiaAppointment, ITimestamp, countries } from "@umahealth/entities"
import { AppointmentRepository } from "@umahealth/repositories"

export const confirmAppointmentUseCase = async (assignationId: string, country: countries) => {
  const appointment : IGuardiaAppointment = await AppointmentRepository.getByAssignationId("bag", country, assignationId)
  if (!appointment) throw new NotFoundException(`[ confirmAppointment ] => Appointment with id: ${assignationId} not found`)
  const score = (appointment.score || 0) + 5 // Si confirmó, le sumamos 5 a su score
  const timestamps : ITimestamp<Timestamp> = {
    ...appointment.timestamps,
    dt_confirmed: Timestamp.now()
  }
  const dataToUpdate = {
    confirmed: true,
    timestamps,
    score
  }
  const updatedAppointment = await AppointmentRepository.update("bag", country, assignationId, dataToUpdate)
  return updatedAppointment
}
