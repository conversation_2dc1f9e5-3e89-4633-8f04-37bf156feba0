import { IDocumentList, PatientRepository } from "@umahealth/repositories"
import { call } from "@umahealth/entities"
import { InternalServerErrorException } from "@nestjs/common"

export async function endCall(uid: string): Promise<IDocumentList<Partial<call>>> {
  try {
    const callObj: call = {
      activeUid: "",
      assignation_id: "",
      assignationPath: "",
      calling: false,
      cuit: "",
      dependant: false,
      room: "",
      token: "",
      type: ""
    }
    const cleanCall = await PatientRepository.updateCall(uid, callObj)
    return cleanCall
  } catch (err) {
    throw new InternalServerErrorException(`[ Patient | endCall ] => ${err.message}`)
  }
}
