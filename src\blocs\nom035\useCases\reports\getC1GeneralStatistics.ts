import { FieldSet } from "airtable"
import { IFormFilters, INom035Form } from "src/portal-app/nom035/statistics/statistics.entities"
import { getUsersWithFormByDate } from "../../utils/functions"

export const getC1GeneralStatistics = (usersArray: FieldSet[], c1Responses: FieldSet[], filters: IFormFilters) => {
  const users = getUsersWithFormByDate(usersArray, c1Responses, INom035Form.c1, filters?.date)
  const treshold_amount = Math.floor(usersArray.length * 0.1)

  const definition = "Un acontecimiento traumático severo (ATS) es una experiencia excepcionalmente estresante y perjudicial que puede tener un impacto significativo en la salud mental y emocional de una persona. Esto puede incluir eventos como desastres naturales, abusos graves, ataques violentos, accidentes traumáticos o experiencias traumáticas en situaciones de combate, que pueden dejar secuelas emocionales y psicológicas profundas en quienes los experimentan."

  const resultado = {
    noATS: 0,
    requireAssessment: 0,
    noRequireAssessment: 0
  }

  const answeredForms = users.length
  const pendingForms = usersArray.length - answeredForms

  if (answeredForms === 0 || (answeredForms < treshold_amount && !filters?.area && !filters?.branch)){
    return { enoughResponses: false, C1Categories: resultado, answeredForms, pendingForms, date: filters?.date, definition: "", participation: 0 }
  }

  const c1ResponsesMap = new Map<string, FieldSet>()
  c1Responses.forEach(record => {
    c1ResponsesMap.set(record.id as string, record)
  })

  users.map(user => {
    const c1FormCompleted = user.c1 as string[] || []
    const lastId = c1FormCompleted[c1FormCompleted.length - 1]
    const record = c1ResponsesMap.get(lastId)

    if (record) {
      if (record["cat1 acontecimiento traumatico"] === "NO") {
        resultado.noATS++
      } else if (
        record["cat2 recuerdos persistentes"] === "SI" ||
        record["cat3 evitar circunstancias"] === "SI" ||
        record["cat4 afectacion"] === "SI"
      ) {
        resultado.requireAssessment++
      } else {
        resultado.noRequireAssessment++
      }
    }
  })

  return {
    enoughResponses: true,
    C1Categories: resultado,
    answeredForms,
    pendingForms,
    date: filters?.date,
    definition,
    participation: Math.round(answeredForms / usersArray.length * 100),
  }

}
