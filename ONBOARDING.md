# 🚀 Onboarding - Megalith Backend

Bienvenido al equipo de desarrollo de **Megalith**, el backend monolítico de UMA Health construido con NestJS y arquitectura Domain Driven Design (DDD).

## 📋 Tabla de Contenidos

1. [Visión General del Proyecto](#visión-general-del-proyecto)
2. [Arquitectura y Patrones](#arquitectura-y-patrones)
3. [Configuración del Entorno](#configuración-del-entorno)
4. [Estructura del Proyecto](#estructura-del-proyecto)
5. [Flujo de Desarrollo](#flujo-de-desarrollo)
6. [Mejores Prácticas](#mejores-prácticas)
7. [Ejemplos Prácticos](#ejemplos-prácticos)
8. [Recursos y Documentación](#recursos-y-documentación)

## 🎯 Visión General del Proyecto

### ¿Qué es Megalith?

Megalith es la aplicación backend monolítica de UMA Health que sirve como núcleo para múltiples aplicaciones de salud digital. Está construida siguiendo principios de **Domain Driven Design (DDD)** y utiliza **NestJS 15** con **Server Side Rendering**.

### Aplicaciones que Soporta

- **Doctor App**: APIs para aplicaciones orientadas a médicos
- **Patient App**: APIs para aplicaciones orientadas a pacientes  
- **Portal App**: APIs para el portal administrativo
- **Public APIs**: Endpoints públicos sin autenticación

### Stack Tecnológico

```typescript
// Tecnologías principales
- Framework: NestJS 15 (Server Side)
- Base de datos: PostgreSQL + Firestore
- Cache: Redis
- Autenticación: Firebase Auth
- Documentación: Swagger/OpenAPI
- Testing: Jest
- Logs: Pino
- Cloud: Google Cloud Platform
```

## 🏗️ Arquitectura y Patrones

### Arquitectura DDD Implementada

Megalith sigue una arquitectura **Domain Driven Design** con las siguientes capas:

```
📁 src/
├── 📁 blocs/           # Lógica de Dominio (Domain Layer)
├── 📁 doctor-app/      # Capa de Aplicación - Médicos
├── 📁 patient-app/     # Capa de Aplicación - Pacientes
├── 📁 portal-app/      # Capa de Aplicación - Portal
├── 📁 public/          # APIs Públicas
├── 📁 providers/       # Integraciones Externas
└── 📁 utils/           # Utilidades Compartidas
```

### Estructura de Blocs (Domain Layer)

Los **blocs** representan los dominios de negocio y contienen:

- **Casos de Uso (Use Cases)**: Lógica de negocio específica
- **Entidades**: Modelos de dominio
- **Repositorios**: Abstracción de acceso a datos
- **Servicios de Dominio**: Lógica compleja del dominio

```typescript
// Ejemplo de estructura de un bloc
📁 blocs/appointments/
├── appointments.bloc.ts        # Fachada del dominio
├── 📁 useCases/
│   ├── createAppointmentUseCase.ts
│   ├── cancelAppointmentUseCase.ts
│   └── updateAppointmentUseCase.ts
├── 📁 entities/
│   └── appointment.entities.ts
└── 📁 interfaces/
    └── appointment.interfaces.ts
```

### Patrón de Casos de Uso

Cada operación de negocio se implementa como un caso de uso independiente:

```typescript
// Ejemplo de caso de uso
export async function createAppointmentUseCase(
  appointmentData: ICreateAppointmentRequest
): Promise<IAppointment> {
  // 1. Validaciones de negocio
  // 2. Lógica del dominio
  // 3. Persistencia
  // 4. Eventos de dominio
}
```

## ⚙️ Configuración del Entorno

### Requisitos Previos

```bash
# Versiones requeridas
Node.js: v16 o superior
npm: v8 o superior
PostgreSQL: v13 o superior
Redis: v6 o superior
Google Cloud SDK (gcloud)
```

### Instalación Paso a Paso

#### 1. Clonar y Configurar el Proyecto

```bash
# Clonar el repositorio
git clone [URL_DEL_REPO]
cd BE-megalith-main

# Instalar dependencias
npm install
```

#### 2. Configurar Base de Datos

```bash
# Conectar a la base de datos usando bastión
gcloud compute ssh sql-bastion-host \
  --tunnel-through-iap \
  --zone=us-central1-b \
  --ssh-flag="-fN -L 5432:localhost:5432" \
  --project=uma-development-ar

# Verificar conexión
psql -h localhost -U [USUARIO] -d [BASE_DATOS]
```

#### 3. Configurar Redis

```bash
# Instalar Redis (Ubuntu/Debian)
sudo apt install redis-server

# Iniciar Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Verificar
redis-cli ping
# Respuesta esperada: PONG
```

#### 4. Configurar Variables de Entorno

Crear el archivo `secret_creds.json` en la raíz del proyecto:

```json
{
  "DATABASE_URL": "postgresql://user:password@localhost:5432/database",
  "REDIS_URL": "redis://localhost:6379",
  "FIREBASE_PROJECT_ID": "your-firebase-project",
  "GOOGLE_CLOUD_PROJECT": "your-gcp-project",
  // ... otros secretos necesarios
}
```

⚠️ **Importante**: Este archivo contiene información sensible y no debe committearse.

#### 5. Ejecutar la Aplicación

```bash
# Desarrollo con recarga automática
npm run start:dev

# Producción local
npm run start
```

La aplicación estará disponible en: `http://localhost:8081`

### Verificación de la Instalación

```bash
# Verificar que la API responde
curl http://localhost:8081/api

# Verificar documentación Swagger
# Abrir: http://localhost:8081/api
```

## 📁 Estructura del Proyecto

### Organización por Capas

```
src/
├── app.module.ts              # Módulo principal
├── main.ts                    # Punto de entrada
│
├── 📁 blocs/                  # DOMAIN LAYER
│   ├── appointments/          # Dominio de citas
│   ├── patients/              # Dominio de pacientes
│   ├── medical-records/       # Dominio de historias clínicas
│   ├── payments/              # Dominio de pagos
│   └── ...                    # Otros dominios
│
├── 📁 doctor-app/             # APPLICATION LAYER - Médicos
│   ├── appointments/          # Controladores de citas
│   ├── chat/                  # Controladores de chat
│   ├── prescriptions/         # Controladores de recetas
│   └── ...
│
├── 📁 patient-app/            # APPLICATION LAYER - Pacientes
│   ├── appointments/
│   ├── profile/
│   ├── payments/
│   └── ...
│
├── 📁 portal-app/             # APPLICATION LAYER - Portal
│   ├── organizations/
│   ├── reports/
│   ├── users/
│   └── ...
│
├── 📁 providers/              # INFRASTRUCTURE LAYER
│   ├── audibaires/            # Integración Audibaires
│   ├── osde/                  # Integración OSDE
│   └── preserfar/             # Integración Preserfar
│
└── 📁 utils/                  # SHARED KERNEL
    ├── auth/                  # Utilidades de autenticación
    ├── validation/            # Validaciones
    ├── logger/                # Logging
    └── ...
```

### Convenciones de Nomenclatura

```typescript
// Archivos y carpetas
kebab-case: appointment-controller.ts
camelCase: appointmentService.ts

// Clases
PascalCase: AppointmentController, PatientService

// Interfaces
PascalCase con I: IAppointment, IPatientData

// Constantes
UPPER_SNAKE_CASE: MAX_APPOINTMENTS_PER_DAY

// Variables y funciones
camelCase: createAppointment, patientData
```

## 🔄 Flujo de Desarrollo

### Git Flow

```bash
# 1. Crear rama de feature desde main
git checkout main
git pull origin main
git checkout -b feature/nueva-funcionalidad

# 2. Desarrollo y commits
git add .
git commit -m "feat: implementar nueva funcionalidad"

# 3. PR a beta (desarrollo)
git push origin feature/nueva-funcionalidad
# Crear PR: feature/nueva-funcionalidad → beta

# 4. PR a next (staging) 
# Crear PR: feature/nueva-funcionalidad → next

# 5. PR a main (producción)
# Crear PR: next → main
```

### Changesets para Versionado

```bash
# Generar changeset
npx changeset

# Seleccionar tipo de cambio:
# - patch: Bug fixes (1.0.0 → 1.0.1)
# - minor: Nuevas features (1.0.0 → 1.1.0)  
# - major: Breaking changes (1.0.0 → 2.0.0)

# Incluir en commit
git add .changeset/
git commit -m "feat: nueva funcionalidad con changeset"
```

### Scripts de Desarrollo

```bash
# Desarrollo
npm run start:dev          # Servidor con recarga automática
npm run build              # Compilar aplicación
npm run lint               # Revisar código
npm run lint:fix           # Corregir problemas de linting

# Testing
npm run test               # Ejecutar tests
npm run test:watch         # Tests en modo watch
npm run test:cov           # Tests con cobertura
npm run test:e2e           # Tests end-to-end

# Base de datos
npm run sql-proxy          # Proxy para base de datos local
```

## ✅ Mejores Prácticas

### 1. Arquitectura DDD

```typescript
// ✅ CORRECTO: Lógica en casos de uso
export async function createAppointmentUseCase(data: ICreateAppointmentData) {
  // Validaciones de dominio
  if (!isValidAppointmentTime(data.scheduledAt)) {
    throw new BadRequestException('Horario inválido')
  }
  
  // Lógica de negocio
  const appointment = await AppointmentRepository.create(data)
  
  // Eventos de dominio
  await EventEmitter.emit('appointment.created', appointment)
  
  return appointment
}

// ❌ INCORRECTO: Lógica en controladores
@Post()
async createAppointment(@Body() data: any) {
  // NO poner lógica de negocio aquí
  const appointment = await this.repository.save(data)
  return appointment
}
```

### 2. Server Side con NestJS 15

```typescript
// ✅ CORRECTO: Server Side Rendering
@Controller('appointments')
export class AppointmentsController {
  @Get(':id')
  async getAppointment(@Param('id') id: string) {
    // Lógica del servidor, no API REST
    const appointment = await this.appointmentsBloc.getById(id)
    return appointment
  }
}

// ❌ INCORRECTO: Exponer APIs REST para lógica interna
@Get('api/appointments/:id')  // Evitar rutas /api/ para lógica interna
```

### 3. Manejo de Errores

```typescript
// ✅ CORRECTO: Errores específicos del dominio
export class AppointmentNotAvailableError extends BadRequestException {
  constructor(appointmentId: string) {
    super(`Appointment ${appointmentId} is not available`)
  }
}

// Uso en casos de uso
if (!appointment.isAvailable()) {
  throw new AppointmentNotAvailableError(appointment.id)
}
```

### 4. Validación con Joi

```typescript
// ✅ CORRECTO: Schemas de validación
export const createAppointmentSchema = Joi.object({
  patientId: Joi.string().required(),
  providerId: Joi.string().required(),
  scheduledAt: Joi.date().iso().required(),
  specialty: Joi.string().valid(...specialtiesArray).required()
})

// Uso en controladores
@Post()
@UsePipes(new JoiValidationPipe(createAppointmentSchema))
async createAppointment(@Body() data: ICreateAppointmentData) {
  return await this.appointmentsBloc.create(data)
}
```

### 5. Testing

```typescript
// ✅ CORRECTO: Tests de casos de uso
describe('CreateAppointmentUseCase', () => {
  it('should create appointment when data is valid', async () => {
    // Arrange
    const appointmentData = {
      patientId: 'patient-123',
      providerId: 'provider-456',
      scheduledAt: new Date('2024-12-01T10:00:00Z')
    }
    
    // Act
    const result = await createAppointmentUseCase(appointmentData)
    
    // Assert
    expect(result).toBeDefined()
    expect(result.patientId).toBe(appointmentData.patientId)
  })
  
  it('should throw error when appointment time is invalid', async () => {
    // Arrange
    const invalidData = {
      patientId: 'patient-123',
      providerId: 'provider-456',
      scheduledAt: new Date('2020-01-01T10:00:00Z') // Fecha pasada
    }
    
    // Act & Assert
    await expect(createAppointmentUseCase(invalidData))
      .rejects
      .toThrow('Horario inválido')
  })
})
```

## 🧩 Ejemplos Prácticos

### Ejemplo 1: Implementar un Nuevo Caso de Uso

Supongamos que necesitas implementar la funcionalidad "Cancelar Cita Médica":

#### 1. Crear el Caso de Uso

```typescript
// src/blocs/appointments/useCases/cancelAppointmentUseCase.ts
import { BadRequestException, NotFoundException } from "@nestjs/common"
import { AppointmentRepository } from "@umahealth/repositories"
import { IAppointment, appointmentStates } from "@umahealth/entities"

export async function cancelAppointmentUseCase(
  appointmentId: string,
  cancelReason: string,
  cancelledBy: 'patient' | 'doctor' | 'system'
): Promise<IAppointment> {
  // 1. Validaciones de entrada
  if (!appointmentId || !cancelReason) {
    throw new BadRequestException('ID de cita y razón son requeridos')
  }

  // 2. Obtener la cita
  const appointment = await AppointmentRepository.getById(appointmentId)
  if (!appointment) {
    throw new NotFoundException('Cita no encontrada')
  }

  // 3. Validaciones de negocio
  if (appointment.state === appointmentStates.CANCELLED) {
    throw new BadRequestException('La cita ya está cancelada')
  }

  if (appointment.state === appointmentStates.COMPLETED) {
    throw new BadRequestException('No se puede cancelar una cita completada')
  }

  // 4. Lógica de dominio
  const updatedAppointment = {
    ...appointment,
    state: appointmentStates.CANCELLED,
    cancelReason,
    cancelledBy,
    cancelledAt: new Date()
  }

  // 5. Persistir cambios
  const result = await AppointmentRepository.update(appointmentId, updatedAppointment)

  // 6. Eventos de dominio (notificaciones, etc.)
  await EventEmitter.emit('appointment.cancelled', {
    appointmentId,
    patientId: appointment.patientId,
    providerId: appointment.providerId,
    cancelReason
  })

  return result
}
```

#### 2. Agregar al Bloc

```typescript
// src/blocs/appointments/appointments.bloc.ts
import { cancelAppointmentUseCase } from './useCases'

export class AppointmentsBloc {
  // ... otros métodos

  async cancelAppointment(
    appointmentId: string,
    cancelReason: string,
    cancelledBy: 'patient' | 'doctor' | 'system'
  ) {
    return await cancelAppointmentUseCase(appointmentId, cancelReason, cancelledBy)
  }
}
```

#### 3. Crear Controlador

```typescript
// src/patient-app/appointments/appointments.controller.ts
@Controller('appointments')
export class AppointmentsController {
  constructor(private readonly appointmentsBloc: AppointmentsBloc) {}

  @Patch(':id/cancel')
  @UsePipes(new JoiValidationPipe(cancelAppointmentSchema))
  async cancelAppointment(
    @Param('id') appointmentId: string,
    @Body() body: ICancelAppointmentBody,
    @Request() req: any
  ) {
    return await this.appointmentsBloc.cancelAppointment(
      appointmentId,
      body.reason,
      'patient'
    )
  }
}
```

#### 4. Definir Interfaces y Validaciones

```typescript
// src/patient-app/appointments/appointments.entities.ts
export interface ICancelAppointmentBody {
  reason: string
}

export const cancelAppointmentSchema = Joi.object({
  reason: Joi.string().min(10).max(500).required()
})
```

### Ejemplo 2: Integración con Proveedor Externo

```typescript
// src/providers/new-provider/new-provider.service.ts
@Injectable()
export class NewProviderService {
  private readonly logger = new Logger(NewProviderService.name)

  async syncPatientData(patientId: string): Promise<IPatientData> {
    try {
      // 1. Obtener datos del paciente local
      const localPatient = await PatientRepository.getById(patientId)

      // 2. Llamar API externa
      const externalData = await this.callExternalAPI(localPatient.dni)

      // 3. Mapear datos
      const mappedData = this.mapExternalToInternal(externalData)

      // 4. Validar y actualizar
      await PatientRepository.update(patientId, mappedData)

      return mappedData
    } catch (error) {
      this.logger.error(`Error syncing patient ${patientId}:`, error)
      throw new InternalServerErrorException('Error en sincronización')
    }
  }

  private async callExternalAPI(dni: string) {
    // Implementación de llamada a API externa
  }

  private mapExternalToInternal(externalData: any): IPatientData {
    // Mapeo de datos externos a formato interno
  }
}
```

## 🔧 Herramientas de Desarrollo Recomendadas

### Extensiones de VSCode

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "ms-vscode.vscode-jest",
    "humao.rest-client"
  ]
}
```

### Configuración de Prettier

```json
// .prettierrc
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2
}
```

### Configuración de ESLint

```json
// .eslintrc.js
module.exports = {
  extends: [
    '@nestjs',
    'prettier'
  ],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn'
  }
}
```

## 🚨 Troubleshooting Común

### Problemas de Conexión

```bash
# Error: ECONNREFUSED 5432
# Solución: Verificar túnel SSH a base de datos
gcloud compute ssh sql-bastion-host --tunnel-through-iap --zone=us-central1-b

# Error: ECONNREFUSED 6379
# Solución: Instalar y iniciar Redis
sudo apt install redis-server
sudo systemctl start redis-server

# Error: Variables de entorno no cargadas
# Solución: Verificar secret_creds.json y reiniciar servidor
npm run start
```

### Problemas de Desarrollo

```bash
# Error: Module not found
# Solución: Verificar imports y paths
npm run build

# Error: Tests failing
# Solución: Limpiar cache y reinstalar
npm run test:cov
rm -rf node_modules package-lock.json
npm install

# Error: Linting issues
# Solución: Auto-fix
npm run lint:fix
```

## 🎯 Próximos Pasos

1. **Explorar la documentación de APIs**: https://docs.umasalud.com/
2. **Revisar casos de uso existentes** en `/src/blocs/`
3. **Configurar tu IDE** con las extensiones recomendadas
4. **Ejecutar los tests** para familiarizarte con el código
5. **Implementar tu primera funcionalidad** siguiendo los patrones establecidos

### Tu Primera Tarea Sugerida

1. Explora el bloc de `appointments`
2. Ejecuta los tests relacionados: `npm run test -- appointments`
3. Implementa un pequeño caso de uso siguiendo el patrón mostrado
4. Crea un PR siguiendo el flujo de Git establecido

## 📚 Recursos Adicionales

- [Documentación de NestJS](https://docs.nestjs.com/)
- [Domain Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)
- [Documentación interna de APIs](https://docs.umasalud.com/)
- [Google Sheets - Configuración de ambientes](https://docs.google.com/spreadsheets/d/1sKUwtPbiwQoZG5OIB-rkXQh-ZKGFroB0kYvItQEj5Ic/edit?gid=0#gid=0)
- [Guía de TypeScript](https://www.typescriptlang.org/docs/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)

---

¡Bienvenido al equipo! 🎉 Si tienes dudas, no dudes en preguntar al equipo.
