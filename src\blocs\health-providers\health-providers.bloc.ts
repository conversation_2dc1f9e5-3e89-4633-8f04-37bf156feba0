import { Timestamp } from "@google-cloud/firestore"
import { Injectable } from "@nestjs/common"
import { IProvider, availableServices } from "@umahealth/entities"
import { getAllProviders, getByDniAndCuit, getByUid, getProvidersByCuit, getProvidersByEmail, getProvidersByFullname, getProvidersByUid, getProvidersByWs,  registerProvider, requestValidate, reverify, setAvailableServicesUseCase, updateProvider, verify } from "./useCases"
import getAvailablePermissions from "./useCases/getAvailablePermissions"
import { createProviderAuth } from "./useCases/createProviderAuth"
import { ICreateProviderAuth } from "src/portal-app/healthProvider/healthProvider.entities"

@Injectable()
export class HealthProvidersBloc {
  async createProvider(data: IProvider<Timestamp>){
    return await registerProvider(data)
  }

  async updateProvider(providerId: string, data: Partial<IProvider<Timestamp>>){
    return await updateProvider(providerId, data)
  }

  async requestValidation(providerId: string){
    return await requestValidate(providerId)
  }

  async getProviderByCuitAndDni(cuit: string, dni: string) {
    return await getByDniAndCuit(cuit, dni)
  }

  async verifyHelthProvider(providerId: string, verified: boolean) {
    return await verify(providerId, verified)
  }

  async reverify(providerId: string, reverified: boolean) {
    return await reverify(providerId, reverified)
  }

  async getProviderByUid(providerUid: string) {
    return await getByUid(providerUid)
  }

  async updateProviderByUid(providerUid: string, data: IProvider) {
    return await updateProvider(providerUid, data)
  }

  async setPermissions(uid: string, serviceName: availableServices, active: boolean) {
    return await setAvailableServicesUseCase(uid, serviceName, active)
  }

  async getProvidersByFullname(name: string){
    return await getProvidersByFullname(name)
  }

  async getProvidersByUid(uid: string){
    return await getProvidersByUid(uid)
  }

  async getProvidersByCuit(cuit: string){
    return await getProvidersByCuit(cuit)
  }

  async getProvidersByEmail(email: string){
    return await getProvidersByEmail(email)
  }

  async getProvidersByWs(ws: string){
    return await getProvidersByWs(ws)
  }

  async getAllProviders(page?: number, size?: number){
    return await getAllProviders(page, size)
  }

  async getPermissions(uid: string){
    return await getAvailablePermissions(uid)
  }

  async createProviderAuth(data: ICreateProviderAuth) {
    return await createProviderAuth(data)
  }
}
