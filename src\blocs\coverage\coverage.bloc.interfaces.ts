import { IHealthInsurance } from "@umahealth/entities"
import { IPatientIoma } from "./apiValidations/interfaces"
import { IPfaDependant } from "src/patient-app/dependant/dependant.entities"
import { Timestamp } from "@google-cloud/firestore"

export type IPatientExternal = IPatientIoma | IPfaDependant[]
export interface ICoverageIncomplete{
  id: string,
  missingFields: Array<keyof Pick<IHealthInsurance,"affiliate_id">>
}

export interface IEmergenciasCredentials {
  coverageUrl: string
  grant_type:string
  password:string
  tokenUrl: string
  username: string
}

export interface IEmergenciasSalesforceCredentials {
  client_id: string
  client_secret: string
  coverageUrl: string
  password: string
  tokenUrl: string
  username: string
}

export interface IIomaCredentials {
  Username: string,
  Password: string
}

export interface IValidateCoverageByApi {
  valid: boolean
  updatedData?: Partial<IHealthInsurance<Timestamp>>,
}

export interface IValidation {
  valid: boolean,
  passValidation: boolean,
  updatedData?: Partial<IHealthInsurance<Timestamp>>,
}
