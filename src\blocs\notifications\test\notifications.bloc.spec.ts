import { Test } from "@nestjs/testing";
import { NotificationsBloc } from "../notifications.bloc";
import { getUserSpecialtyNotification, createSpecialtyNotificationEventBatch, updateSpecialtyNotificationEventBatch } from "../useCases";
import { mockSpecialtyNotificationEvent } from "./notifications.mock";
import { NotificationChannel, TSpecialties, ISpecialtyNotificationEvent } from "@umahealth/entities";
import { Timestamp } from "@google-cloud/firestore";

jest.mock("../useCases", () => ({
    getUserSpecialtyNotification: jest.fn(),
    createSpecialtyNotificationEventBatch: jest.fn(),
    updateSpecialtyNotificationEventBatch: jest.fn(),
}));

describe("NotificationsBloc", () => {
    let notificationsBloc: NotificationsBloc;

    beforeEach(async () => {
        const moduleRef = await Test.createTestingModule({
            providers: [NotificationsBloc],
        }).compile();

        notificationsBloc = moduleRef.get<NotificationsBloc>(NotificationsBloc);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("should be defined", () => {
        expect(notificationsBloc).toBeDefined();
    });

    describe("getUserSpecialtyNotification", () => {
        const uid = "uid";
        it("should return all active notifications for the user uid provided", async () => {
            (getUserSpecialtyNotification as jest.Mock).mockResolvedValue(["clinicamedica"]);

            const result = await notificationsBloc.getUserSpecialtyNotification(uid);
            expect(result).toEqual(["clinicamedica"]);
            expect(getUserSpecialtyNotification).toHaveBeenCalledWith(uid, undefined);
        });

        it("should return notifications for a specific specialty", async () => {
            const specialty: TSpecialties = "clinicamedica";
            (getUserSpecialtyNotification as jest.Mock).mockResolvedValue(["clinicamedica"]);

            const result = await notificationsBloc.getUserSpecialtyNotification(uid, specialty);
            expect(result).toEqual(["clinicamedica"]);
            expect(getUserSpecialtyNotification).toHaveBeenCalledWith(uid, specialty);
        });

        it("should handle errors", async () => {
            (getUserSpecialtyNotification as jest.Mock).mockRejectedValue(new Error("getUserSpecialtyNotification - Error getting user notification"));

            await expect(notificationsBloc.getUserSpecialtyNotification(uid)).rejects.toThrowError("getUserSpecialtyNotification - Error getting user notification");
            expect(getUserSpecialtyNotification).toHaveBeenCalledTimes(1);
        });
    });

    describe("createSpecialtyNotificationEventBatch", () => {
        it("should create a specialty notification event batch", async () => {
            const mockNotificationData: ISpecialtyNotificationEvent<Timestamp> = {
                active: true,
                channels: { email: true, push: false, whatsapp: false },
                coverage: "full",
                email: "<EMAIL>",
                specialty: "clinicamedica",
                uid: "testUid",
                ws: "testWs",
                timestamps: {
                    dt_create: Timestamp.now(),
                    dt_start: Timestamp.now(),
                },
            };

            (createSpecialtyNotificationEventBatch as jest.Mock).mockResolvedValue({ id: "batchId" });

            const result = await notificationsBloc.createSpecialtyNotificationEventBatch(mockNotificationData);
            expect(result).toEqual({ id: "batchId" });
            expect(createSpecialtyNotificationEventBatch).toHaveBeenCalledWith(mockNotificationData);
        });

        it("should handle errors", async () => {
            const mockNotificationData: ISpecialtyNotificationEvent<Timestamp> = {} as ISpecialtyNotificationEvent<Timestamp>;
            (createSpecialtyNotificationEventBatch as jest.Mock).mockRejectedValue(new Error("Error creating notification event batch"));

            await expect(notificationsBloc.createSpecialtyNotificationEventBatch(mockNotificationData)).rejects.toThrowError("Error creating notification event batch");
            expect(createSpecialtyNotificationEventBatch).toHaveBeenCalledTimes(1);
        });
    });

    describe("updateSpecialtyNotificationEventBatch", () => {
        it("should update a specialty notification event batch", async () => {
            const documentId = "testDocId";
            const updateData: Partial<ISpecialtyNotificationEvent<Timestamp>> = {
                active: false,
                channels: { email: false, push: true, whatsapp: false },
            };

            (updateSpecialtyNotificationEventBatch as jest.Mock).mockResolvedValue({ updated: true });

            const result = await notificationsBloc.updateSpecialtyNotificationEventBatch(documentId, updateData);
            expect(result).toEqual({ updated: true });
            expect(updateSpecialtyNotificationEventBatch).toHaveBeenCalledWith(documentId, updateData);
        });

        it("should handle errors", async () => {
            const documentId = "testDocId";
            const updateData: Partial<ISpecialtyNotificationEvent<Timestamp>> = {};
            (updateSpecialtyNotificationEventBatch as jest.Mock).mockRejectedValue(new Error("Error updating notification event batch"));

            await expect(notificationsBloc.updateSpecialtyNotificationEventBatch(documentId, updateData)).rejects.toThrowError("Error updating notification event batch");
            expect(updateSpecialtyNotificationEventBatch).toHaveBeenCalledTimes(1);
        });
    });
});