import { Timestamp } from "@google-cloud/firestore"
import { PatientRepository } from "@umahealth/repositories"

export async function createPatientHistory(providerUid: string, cuit: string, providerFullname: string, providerEspecialidad: string, patientUid: string, patientDni: string, text: string, dt_create: Timestamp, historyId: string) {

  const data = {
    doctor: {
      uid: providerUid,
      cuit: cuit,
      fullname: providerFullname,
      specialty: providerEspecialidad
    },
    patient: {
      uid: patientUid,
      dni: patientDni
    },
    text,
    timestamps: {
      dt_create: dt_create
    }
  }

  return await PatientRepository.createHistory(patientUid, historyId, data)
}
