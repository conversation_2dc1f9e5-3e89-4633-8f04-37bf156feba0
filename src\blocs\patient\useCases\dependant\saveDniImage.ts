import { IDocumentReference, countries } from "@umahealth/entities"
import { DependantRepository } from "@umahealth/repositories"
import { uploadLocalFileStorage } from "src/utils/files"
import { Timestamp } from "@google-cloud/firestore"

export const saveDependantDniImage = async (uid: string, dependantUid: string, country: countries, filename: string, documentSide: "front" | "back") => {
  // Upload to local Storage
  const storagePath = `${dependantUid}/dni/${documentSide}/${new Date().toISOString()}`
  const localPath = `./uploads/${filename}`
  const fileUrl = await uploadLocalFileStorage(localPath, storagePath)
  // Crear document reference
  const documentReference: IDocumentReference<Timestamp> = {
    country: country,
    content: {
      attachment: {
        url: fileUrl,
        storagePath: storagePath
      }
    },
    referenceId: `${dependantUid}_${documentSide}_${fileUrl}`,
    uid: dependantUid
  }

  // Subir documentReference
  const documentReferenceBatch = await DependantRepository.createDocumentReference(dependantUid, documentReference)
  // Actualizar dependant
  let dependantDocument = null
  if(documentSide === "front") {
    dependantDocument = await DependantRepository.doublePathUpdate(uid, dependantUid, { path_dni: storagePath })
  } else {
    dependantDocument = await DependantRepository.doublePathUpdate(uid, dependantUid, { path_dni_back: storagePath })
  }
  return {path: storagePath, docs: [documentReferenceBatch, ...dependantDocument]}
}
