import { PatientRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { emergenciasSalesforceApi } from "../../apiValidations/patients/EMERGENCIAS"
import { Logger, NotFoundException } from "@nestjs/common"
import { Timestamp } from "@google-cloud/firestore"
import { getFatsaValidation } from "../../apiValidations/patients/FATSA"
import { validateOspeconPatient } from "../../apiValidations/patients/OSPECON"
import { validateOsmeconPatient } from "../../apiValidations/patients/OSMECON"
import { runIomaValidation } from "src/blocs/patient/useCases/revalidateIOMAPatient"
import { IValidateCoverageByApi } from "../../coverage.bloc.interfaces"
import { emerApiDefault } from "../../apiValidations/dependants/EMERGENCIAS"
import { validateUdemPatient } from "../../apiValidations/patients/UDEM"
import { applyPFAPatientUpdates } from "src/blocs/patient/useCases/revalidatePFAPatient"
import { ValidatePFAPatient, ValidatePFAPatientByDni } from "../../apiValidations/patients/PFA"
import { validateOspreraPatient } from "../../apiValidations/patients/OSPRERA"

export const ValidateCoverageByApiUseCase = async(corporate: IHealthInsurance<Timestamp>, uid: string): Promise<IValidateCoverageByApi> => {

  const patient = await PatientRepository.getByUid(uid)

  if(!patient) throw new NotFoundException(`[ Coverages | patient | validateCoverageByAPI ] => User not found with uid: ${uid}`)
  let response: boolean
  let updatedData: Partial<IHealthInsurance<Timestamp>>

  switch(corporate.id.toUpperCase()) {
  case "EMERGENCIAS":{
    const patientCoverages = await emergenciasSalesforceApi(patient.dni, corporate)
    response = !!patientCoverages?.[corporate.id]
    if (patientCoverages[corporate.id]) {
      updatedData = {
        affiliate_id: patientCoverages[corporate.id].affiliateNumber || corporate.affiliate_id,
        plan: patientCoverages[corporate.id].plan || corporate.plan,
        taxTreatment: patientCoverages[corporate.id].taxTreatment
      }
    }
    break
  }
  case "IOMA":{
    response = await runIomaValidation(corporate, patient, false)
    break
  }
  case "IOMA-APP":{
    response = await runIomaValidation(corporate, patient, false)
    break
  }
  case "FATSA":{
    response = await getFatsaValidation(patient)
    break
  }
  case "OSPECON": {
    response = await validateOspeconPatient(patient.dni)
    break
  }
  case "OSMECON": {
    response = await validateOsmeconPatient(patient.dni)
    break
  }
  case "POLICIA FEDERAL": {
    const validationResult = await ValidatePFAPatient(patient.dni, corporate.affiliate_id)
    response = validationResult.result

    // Si falla la validacion, intentamos con el dni y corregimos el affiliate_id, dado que en muchos casos hay errores en el affiliate_id de base de datos
    if(!response){
      const validationResultByDni = await ValidatePFAPatientByDni(patient.dni)
      response = validationResultByDni.result
      Logger.log(`[ Patient | validateCoverageByAPI | POLICIA FEDERAL ] Validation result by dni: ${JSON.stringify(validationResultByDni)}`)

      if(validationResultByDni.correctAffiliateNumber){
        // Corregimos el affiliate_id en base de datos
        corporate.affiliate_id = validationResultByDni.correctAffiliateNumber
        Logger.log(`[ Patient | validateCoverageByAPI | POLICIA FEDERAL ] Affiliate_id updated to: ${corporate.affiliate_id}`)
      }
    }

    // Solo aplicamos las actualizaciones PFA si la validación fue exitosa
    if(response) {
      await applyPFAPatientUpdates(corporate, patient, false)
    }
    break
  }
  case "UDEM": {
    Logger.log(`[${ValidateCoverageByApiUseCase.name}] -> Validating UDEM patient with email ${patient.email}...`)
    response = await validateUdemPatient(patient.dni)
    break
  }
  case "OSPRERA": {
    Logger.log(`[${ValidateCoverageByApiUseCase.name}] -> Validating OSPRERA patient with email ${patient.email}...`)
    response = await validateOspreraPatient(patient.dni)
    break
  }

  default: {
    const patientCoverages = await emerApiDefault(patient.dni, corporate)
    if(patientCoverages[corporate.id]){
      response = true
      updatedData = {
        affiliate_id: patientCoverages[corporate.id].affiliateNumber || corporate.affiliate_id,
        plan: patientCoverages[corporate.id].plan || corporate.plan,
        taxTreatment: patientCoverages[corporate.id].taxTreatment
      }
    }else{
      response = false
    }
    break
  }
  }

  return {
    valid: response,
    updatedData,
  }
}

