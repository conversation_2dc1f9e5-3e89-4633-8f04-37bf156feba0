import { Timestamp } from "@google-cloud/firestore"
import { BadRequestException } from "@nestjs/common"
import { IPatient, gender } from "@umahealth/entities"
import axios from "axios"

export async function getFatsaValidation(patient: IPatient<Timestamp>) {
  const cuil = getCuilCuit(patient.dni, patient.sex)
  const response = await axios.get(`https://wwwfatsa.sanidad.org.ar/Valida_Atencion/${cuil}/0`)
  if (response.data === "NO Atiende") return false
  return true
}

function getCuilCuit(dni: string, gender: gender): string {
  // AB = prefijo
  // C = verificador
  let AB, C

  // Verifico que el dni tenga exactamente ocho numeros y que la cadena no contenga letras.
  if (dni.length != 8) {
    if (dni.length == 7) {
      dni = "0".concat(dni)
    } else {
      throw new BadRequestException("[ Coverages | apiValidations | FATSA ] => El numero de dni ingresado debe tener entre 7 y 8 digitos")
    }
  }

  // Defino el valor del prefijo.
  if (gender === "M") {
    AB = "20"
  } else if (gender === "F") {
    AB = "27"
  } else {
    AB = "30"
  }


  // Los numeros que le tengo que multiplicar a la cadena prefijo+dni
  const multiplicadores = [3, 2, 7, 6, 5, 4, 3, 2]

  // Realizo las dos primeras multiplicaciones por separado.
  let calculo = parseInt(AB.charAt(0)) * 5 + parseInt(AB.charAt(1)) * 4

  // Recorro el array y el numero de dni para realizar las multiplicaciones.
  for (let i = 0; i < dni.length; i++) {
    calculo += parseInt(dni.charAt(i)) * multiplicadores[i]
  }

  // Calculo el resto.
  const resto = calculo % 11

  // Llevo a cabo la evaluacion de las tres condiciones para determinar el valor de C
  // y conocer el valor definitivo de AB.
  if ((gender === "F" || gender === "M") && resto == 1) {
    if (gender === "M") {
      C = "9"
    } else {
      C = "4"
    }
    AB = "23"
  } else if (resto === 0) {
    C = "0"
  } else {
    C = 11 - resto
  }

  // Generate cuit
  const cuil_cuit = `${AB}${dni}${C}`
  return cuil_cuit
}
