import { ParametersRepository } from "@umahealth/repositories"
import { IProviderIncomePrices } from "@umahealth/entities"

/**
 * Crea un nuevo documento de ingresos y lo prepara para ser añadido al batch de operaciones de Firestore.
 *
 * @param {IProviderIncomePrices} incomeData - Documento que puede ser normalPrice, turbopricecomun o turbopricedelturbo.
 * @param {Date} startTurbo - Fecha y hora de inicio del periodo del ingreso.
 * @param {Date} endTurbo - Fecha y hora de fin del periodo del ingreso.
 * @returns {Promise<IDocumentList<IProviderIncomePrices>>} - Promesa que resuelve al documento listo para ser añadido a saveBatch.
 */
export async function createTurboDocument(
  incomeData: IProviderIncomePrices,
  startTurbo: Date,
  endTurbo: Date) {

  const adjustedStart = new Date(startTurbo)
  adjustedStart.setHours(adjustedStart.getHours() + 3) // Ajustar por zona horaria o necesidad específica

  const adjustedEnd = endTurbo ? new Date(endTurbo) : null
  if (adjustedEnd) {
    adjustedEnd.setHours(adjustedEnd.getHours() + 3) // Ajustar por zona horaria
  }

  return await ParametersRepository.createNewIncomesInGuardia({
    ...incomeData,
    timestamps: {
      dt_period_start: adjustedStart,
      dt_period_finish: adjustedEnd
    }
  })
}

