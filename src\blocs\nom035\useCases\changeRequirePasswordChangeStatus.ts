import { NotFoundException } from "@nestjs/common"
import { authClient, getUserByUid } from "@umahealth/auth"


export const changeRequirePasswordChangeStatus = async (uid: string) => {

  const user = await getUserByUid(uid)

  if(!user) {
    throw new NotFoundException(`[ Nom035 | RequireChangePasswordStatus ] User not found with uid: ${uid}`)
  }

  await authClient.setCustomUserClaims(uid, {
    "requirePasswordChange": false
  })

  return { message: "Custom claim updated", status: 200 }
}
