import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, Logger } from "@nestjs/common"
import { countries, IAppointment, IOnlineAppointment } from "@umahealth/entities"
import { AppointmentRepository, IDocumentList } from "@umahealth/repositories"

export const closeOnlineUseCase = async (
  assignationId: string,
  country: countries,
  appointment: Partial<IAppointment<Timestamp>>
): Promise<IDocumentList<Partial<IAppointment<Timestamp>>>> => {
  const fullAppointment = await AppointmentRepository.getByAssignationId<IOnlineAppointment<Timestamp>>("online", country, assignationId)

  appointment.state = "DONE"
  appointment.timestamps = {
    ...fullAppointment.timestamps,
    dt_close: Timestamp.now()
  }

  const closedOnlineAppointment = await AppointmentRepository.update(
    "online",
    country,
    assignationId,
    appointment
  )

  if (!closedOnlineAppointment) {
    Logger.error(`[ Appointments | ${closeOnlineUseCase.name} ] => Error closing online appointment ${assignationId}`)
    throw new InternalServerErrorException(
      `[ Appointments | ${closeOnlineUseCase.name} ] => Error closing onsite appointment. Id: ${assignationId}`
    )
  }

  return closedOnlineAppointment
}
