import { Logger } from "@nestjs/common"
import { Nom035Form } from "@umahealth/sql-models"
import { ICreateNomForm } from "../../interfaces"

export const createNomForm = async (formData: ICreateNomForm) => {
  const {email, corporate, form, questionsAndAnswers} = formData
  Logger.log(`[${createNomForm.name}] -> Creating nom form, email: ${email}`)
  await Nom035Form.create({ email, corporate, form, questionsAndAnswers  })
}
