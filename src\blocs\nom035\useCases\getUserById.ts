import { INom035User } from "@umahealth/entities"
import { getAirtableFormStatusForOneUser } from "src/utils/airtable"
import { INom035UserWithFormStatus } from "../interfaces"
import nom035DTO from "../models/nom035_userPositionDTO"


export const getUserById = async (userId: string): Promise<INom035UserWithFormStatus> => {
  const user = await nom035DTO.getUserById(userId) as unknown as INom035UserWithFormStatus
  if(user){
    const formStatus = await getAirtableFormStatusForOneUser(user.email)
    if(formStatus){
      user.dataValues.formStatus = {
        c1: formStatus.c1Responses ? true : false,
        c2: formStatus.c2Responses ? true : false,
        c3: formStatus.c3Responses ? true : false
      }
    }
  }


  return user
}
