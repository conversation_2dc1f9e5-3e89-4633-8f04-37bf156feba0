import { Injectable } from "@nestjs/common"
import { aiDiagnosisUseCase, complainPredictUseCase, diagnosticPredictUseCase, medicalQAUseCase, sendChatbotMessage, dengueSendChatbotMessage } from "./useCases"

@Injectable()
export class AiModelsBloc {
  async complainPredict(complainText: string) {
    return await complainPredictUseCase(complainText)
  }

  async diagnosticPredict(motivoDeConsulta: string) {
    return await diagnosticPredictUseCase(motivoDeConsulta)
  }

  async aiDiagnosis(attMotive: string) {
    return await aiDiagnosisUseCase(attMotive)
  }

  async medicalQA(attMotive: string) {
    return await medicalQAUseCase(attMotive)
  }

  async sendChatbotMessage(text: string, conversation_id: string) {
    return await sendChatbotMessage(text, conversation_id)
  }

  async dengueSendChatbotMessage(text: string, conversation_id: string) {
    return await dengueSendChatbotMessage(text, conversation_id)
  }
}

export default new AiModelsBloc()
