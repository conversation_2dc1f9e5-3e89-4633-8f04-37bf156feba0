import { Logger } from "@nestjs/common"
import { IEventData, IGenerateNotificationParams, INotificationConfig } from "../../notifications.bloc.interface"
import { getNotificationsConfig } from "./notificationsConfig"

export const CorporatesNotificationsEnum = {
  IOMA: "IOMA",
  FARMATODO: "FARMATODO",
  UMA: "UMA",
} as const

export type CorporatesNotificationsType = keyof typeof CorporatesNotificationsEnum

export const corporateNotifications: Record<CorporatesNotificationsType, INotificationConfig> = {
  [ CorporatesNotificationsEnum.IOMA ]: {
    enabledTypes: [ "email", "whatsapp" ],
    generateNotifications,
  },
  [ CorporatesNotificationsEnum.UMA ]: {
    enabledTypes: [ "email", "sms", "whatsapp" ],
    generateNotifications,
  },
  [ CorporatesNotificationsEnum.FARMATODO ]: {
    enabledTypes: [ "email" ],
    generateNotifications,
  }
}

export function generateNotifications({
  country,
  corporate,
  dateEqualAssignation,
  dateFourHoursBefore,
  dateOneDayBefore,
  dateOneHourBefore,
  dateTenMinutesBefore,
  attentionTime,
  type,
  patientFullname,
  providerFullname,
  attentionDate,
  dynamicLink,
  dynamicLinkQueue,
  homeUrl,
}: IGenerateNotificationParams): IEventData[] {
  const dateMap: Record<string, string> = {
    dateEqualAssignation,
    dateFourHoursBefore,
    dateOneDayBefore,
    dateOneHourBefore,
    dateTenMinutesBefore
  }

  const eventData: IEventData[] = []

  const config = getNotificationsConfig({
    attentionDate,
    attentionTime,
    corporate,
    dynamicLink,
    dynamicLinkQueue,
    homeUrl,
    patientFullname,
    providerFullname,
    type
  })

  if (!config) {
    Logger.warn(`[ ${generateNotifications.name} ] => Notification ${type} config for ${corporate} is not defined`)
    return []
  }

  Object.keys(dateMap).forEach((dateKey) => {
    if (
      (country === "AR" && dateKey === "dateTenMinutesBefore") ||
      (country === "MX" && dateKey === "dateEqualAssignation")
    ) {
      return
    }
    if (config[dateKey]) {
      eventData.push({
        date: dateMap[dateKey],
        params: config[dateKey]?.params,
        template: config[dateKey]?.template,
        type
      })
    }
  })

  return eventData
}


