import { Timestamp } from "@google-cloud/firestore"
import { ProviderRepository } from "@umahealth/repositories"

export async function updateHistoryInProvider(text: string, providerUid: string, patientUid: string, historyId: string, dt_updated: Timestamp) {
  const updatedHistory = {
    text: text,
    "timestamps.dt_updated": dt_updated
  }
  return await ProviderRepository.updateHistory(providerUid, patientUid, historyId, updatedHistory)

}
