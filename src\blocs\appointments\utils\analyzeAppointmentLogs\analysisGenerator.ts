import { AppointmentConnectionStatus, IConnectionPeriod, ITechnicalIssue } from "../../appointments.entities"

/**
 * Formatea una duración en minutos y segundos a texto
 */
function formatDuration(duration: { minutes: number, seconds: number }): string {
  if (duration.minutes === 0 && duration.seconds === 0) return "0 segundos"

  let minutesPart = ""
  if (duration.minutes > 0) {
    minutesPart = `${duration.minutes} ${duration.minutes === 1 ? "minuto" : "minutos"}`
  }

  let secondsPart = ""
  if (duration.seconds > 0) {
    secondsPart = `${duration.seconds} ${duration.seconds === 1 ? "segundo" : "segundos"}`
  }

  return [minutesPart, secondsPart].filter(Boolean).join(" y ")
}

/**
 * Genera detalles de conexión para un participante específico
 */
function generateConnectionDetails(history: IConnectionPeriod[], actor: "paciente" | "médico"): string[] {
  const details: string[] = []

  if (history.length > 0) {
    details.push(`El ${actor} se conectó a la consulta`)

    if (history.length > 1) {
      const disconnectionCount = history.length - 1
      details.push(`El ${actor} durante la consulta se desconectó ${disconnectionCount} ${disconnectionCount === 1 ? "vez" : "veces"}`)
    }

    const isCurrentlyConnected = history[history.length - 1].disconnected_at === null
    if (isCurrentlyConnected) {
      details.push(`El ${actor} no finalizó su conexión`)
    }
  }

  return details
}

/**
 * Genera el análisis completo de la consulta
 */
export function generateAnalysis(
  patientHistory: IConnectionPeriod[],
  providerHistory: IConnectionPeriod[],
  technicalIssues: ITechnicalIssue[],
  uidsProvided: { patientUidProvided: boolean, providerUidProvided: boolean },
  overlappingTime: { minutes: number, seconds: number }
): {
  appointment_status: AppointmentConnectionStatus
  summary: string
  details: string[]
} {
  const details: string[] = []

  if (!uidsProvided.patientUidProvided) {
    details.push("No se proporcionó el UID del paciente, por lo que no se puede determinar si el paciente se conectó o no")
  }
  if (!uidsProvided.providerUidProvided) {
    details.push("No se proporcionó el UID del médico, por lo que no se puede determinar si el médico se conectó o no")
  }

  const isPatientConnected = patientHistory.some(p => p.disconnected_at === null)
  const isProviderConnected = providerHistory.some(p => p.disconnected_at === null)
  const wasPatientEverConnected = patientHistory.length > 0
  const wasProviderEverConnected = providerHistory.length > 0

  let status: AppointmentConnectionStatus
  let summary: string

  if (!uidsProvided.patientUidProvided || !uidsProvided.providerUidProvided) {
    status = "failed"
    if (uidsProvided.patientUidProvided && wasPatientEverConnected) {
      summary = "El paciente se conectó pero no hay información del médico"
    } else if (uidsProvided.providerUidProvided && wasProviderEverConnected) {
      summary = "El médico se conectó pero no hay información del paciente"
    } else {
      summary = "No hay suficiente información para determinar el estado de la consulta"
    }
  } else if (isPatientConnected && isProviderConnected) {
    status = "in_progress"
    summary = "La consulta está en curso con ambos participantes conectados"
  } else if (!wasPatientEverConnected && !wasProviderEverConnected) {
    status = "failed"
    summary = "Ningún participante se conectó a la consulta"
  } else if (wasPatientEverConnected && wasProviderEverConnected) {
    status = "successful"
    summary = "Ambos participantes se conectaron"
  } else {
    status = "failed"
    summary = !wasPatientEverConnected
      ? "El paciente nunca se conectó a la consulta"
      : "El médico nunca se conectó a la consulta"
  }


  if (uidsProvided.patientUidProvided && wasPatientEverConnected) {
    details.push(...generateConnectionDetails(patientHistory, "paciente"))
  }
  if (uidsProvided.providerUidProvided && wasProviderEverConnected) {
    details.push(...generateConnectionDetails(providerHistory, "médico"))
  }

  if (technicalIssues.length > 0) {
    const issues = technicalIssues.length === 1 ?
      "Se detectó 1 problema técnico durante la consulta" :
      `Se detectaron ${technicalIssues.length} problemas técnicos durante la consulta`

    details.push(issues)
    technicalIssues.forEach(issue => {
      details.push(`${issue.actor === "patient" ? "Paciente" : "Médico"}: ${issue.description}`)
    })
  }

  if (overlappingTime.minutes > 0 || overlappingTime.seconds > 0) {
    details.push(`Paciente y médico estuvieron conectados simultáneamente durante ${formatDuration(overlappingTime)}`)
  } else if (uidsProvided.patientUidProvided && uidsProvided.providerUidProvided) {
    details.push("No hubo tiempo de conexión simultánea")
  }

  return {
    appointment_status: status,
    summary,
    details
  }
}
