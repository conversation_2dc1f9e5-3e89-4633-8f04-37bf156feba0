import { Injectable } from "@nestjs/common"
import { countries, gender } from "@umahealth/entities"
import { createEntryUseCase } from "./useCases"


@Injectable()
export class MarketingBloc {
  async createMarketingListEntry(corporateNorm: string, country: countries, dob: string, email: string, firstName: string, lastName: string, listId: string, sex: gender) {
    return await createEntryUseCase(corporateNorm, country, dob, email, firstName, lastName, listId, sex)
  }
}