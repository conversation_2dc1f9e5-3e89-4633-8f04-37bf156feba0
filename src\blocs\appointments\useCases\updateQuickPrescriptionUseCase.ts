import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { countries, IChatAttAppointment} from "@umahealth/entities"
import { AppointmentRepository, IDocumentList } from "@umahealth/repositories"

export const updateQuickPrescriptionAppointment = async (
  assignationId: string,
  country: countries,
  appointment: Partial<IChatAttAppointment<Timestamp>>
): Promise<IDocumentList<Partial<IChatAttAppointment<Timestamp>>>> => {
  const updatedChatAttAppointment = await AppointmentRepository.update("quickPrescription",country,assignationId,appointment)

  if(!updatedChatAttAppointment) throw new InternalServerErrorException(`[ Appointments | updateQuickPrescriptionAppointment ] => Error updating prescription attention appointment. Id: ${assignationId}`)

  return updatedChatAttAppointment
}