import { Timestamp } from "@google-cloud/firestore"
import { chat } from "@umahealth/entities"
import { PatientRepository } from "@umahealth/repositories"

export async function createChat(uid: string) {

  const callObj: chat<Timestamp> = {
    activeUid: null,
    assignationPath: null,
    assignation_id: null,
    chatting: false,
    cuit: null,
    dependant: null,
    requested: false,
    type: "chat",
    unreadMessagesDoctor: 0,
    dtLastMessageDoctor: null,
    unreadMessagesPatient: 0,
    dtLastMessagePatient: null
  }
  return await PatientRepository.createChatAtt(uid, callObj)
}
