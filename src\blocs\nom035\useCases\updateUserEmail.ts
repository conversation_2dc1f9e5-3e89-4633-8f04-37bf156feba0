import { Logger } from "@nestjs/common"
import nom035DTO from "../models/nom035_userPositionDTO"
import {updateUserInAirtable} from "src/utils/airtable"
import {updateEmail} from "@umahealth/auth"
import {updateDocument} from "@umahealth/firestore"
import {sendUpdateCacheMessage} from "./cache/sendUpdateCacheMessage"

export const updateUserEmail = async (uid: string, corporateId: string, email: string, data: {correo: string}) => {
  try {
    Logger.log(`[${updateUserEmail.name}] -> Updating email`)

    await updateEmail(uid, data.correo)
    await nom035DTO.updateUser(uid, {email: data.correo})
    await nom035DTO.updateUserForm(email, data.correo)
    await updateUserInAirtable("nomina", email, data)
    await updateDocument(`user/${uid}`, {email: data.correo})

    await sendUpdateCacheMessage(corporateId, "users")

    Logger.log(`[${updateUserEmail.name}] -> Email actualizado exitosamente`)

    return {
      message: "Email actualizado exitosamente",
    }

  } catch (error) {
    Logger.error(`[${updateUserEmail.name}] -> Error al actualizar email: ${error.message}`)
    throw new Error(`Error al actualizar email del usuario: ${error.message}`)
  }
}
