import { IRequest } from "@umahealth/entities"
import { updateDocument } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { IUpdateOldActiveServices } from "../../patient.bloc.interface"


export const updateOldActiveServicesUseCase = async (data: IUpdateOldActiveServices, request: IRequest<Timestamp>, assignationPath: string, type: string) => {
  const { activeUid, assignationId, cuit, startDate, isDependant, room, token} = data
  const oldActiveServicesPath = `user/${data.uid}`
  const oldCallData = {
    _start_date: token,
    call: {
      activeUid, room, token, cuit,
      date: startDate,
      assignation_id: assignationId,
      assignationPath,
      category: request.att_category,
      dependant: isDependant,
      type: type,
    }
  }
  await updateDocument(oldActiveServicesPath, oldCallData)
}