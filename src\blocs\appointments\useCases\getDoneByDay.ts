import { countries } from "@umahealth/entities"
import { AppointmentRepository, IFilter } from "@umahealth/repositories"
import { isAfter } from "date-fns"

export async function getDoneGuardiaAppointmentsByDay(uid: string, country: countries, day: Date, limit: number): Promise<{
  onlineToday: boolean,
  hasExceededLimit: boolean,
  perHour: number[] | null
}> {
  const filters: IFilter[] = [{
    field: "uid",
    comparator: "==",
    value: uid
  }]

  const allAppointments = await AppointmentRepository.getDoneAppointmentsByFilters("bag", country, filters)
  if (allAppointments.length === 0) {
    return {
      onlineToday: false,
      hasExceededLimit: false,
      perHour: null
    }
  }
  const appointments = allAppointments.filter(appt => appt.timestamps?.dt_close && isAfter(appt.timestamps.dt_close.toDate(), day))
  let exceededLimit = false
  const amountPerHourList = []
  const amountPerHour: Record<string, number> = {
    "0": 0,
    "1": 0,
    "2": 0,
    "3": 0,
    "4": 0,
    "5": 0,
    "6": 0,
    "7": 0,
    "8": 0,
    "9": 0,
    "10": 0,
    "11": 0,
    "12": 0,
    "13": 0,
    "14": 0,
    "15": 0,
    "16": 0,
    "17": 0,
    "18": 0,
    "19": 0,
    "20": 0,
    "21": 0,
    "22": 0,
    "23": 0
  }

  for (const appt of appointments) {
    const hour = appt.timestamps.dt_close.toDate().getHours()
    amountPerHour[hour]++
  }

  for (const countPerHour of Object.values(amountPerHour)) {
    if (countPerHour > limit) {
      exceededLimit = true
    }
    amountPerHourList.push(countPerHour)
  }

  return {
    onlineToday: true,
    hasExceededLimit: exceededLimit,
    perHour: amountPerHourList
  }
}
