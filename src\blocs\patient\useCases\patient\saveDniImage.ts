import { InternalServerErrorException } from "@nestjs/common"
import { IDocumentReference, countries } from "@umahealth/entities"
import { PatientRepository } from "@umahealth/repositories"
import { uploadLocalFileStorage } from "src/utils/files"
import { Timestamp } from "@google-cloud/firestore"

export const saveDniImage = async (uid: string, country: countries, filename: string, documentSide: "front" | "back") => {
  try {
    // Upload to local Storage
    const storagePath = `${uid}/dni/${documentSide}/${filename}`
    const localPath = `./uploads/${filename}`
    const fileUrl = await uploadLocalFileStorage(localPath, storagePath)
    // Crear document reference
    const documentReference: IDocumentReference<Timestamp> = {
      country: country,
      content: {
        attachment: {
          url: fileUrl,
          storagePath: storagePath
        }
      },
      referenceId: `${uid}_${documentSide}_${fileUrl}`,
      uid: uid
    }

    // Subir documentReference
    const documentReferenceBatch = await PatientRepository.createDocumentReference(uid, documentReference)
    // Actualizar patient
    let patientDocument = null
    if (documentSide === "front") {
      patientDocument = await PatientRepository.update(uid, { path_dni: storagePath })
    } else {
      patientDocument = await PatientRepository.update(uid, { path_dni_back: storagePath })
    }
    return [documentReferenceBatch, patientDocument]
  } catch (error) {
    throw new InternalServerErrorException(`[ patient | saveDniImage ] => Something failed in the bloc. ${JSON.stringify(error)}`)
  }
}
