import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { getUserByUid } from "@umahealth/auth"
import { IPortalUser } from "@umahealth/entities"
import { getFollowUps } from "src/utils/airtable"


export const getFollowUpsMethod = async (uid: string, coach: IPortalUser<Timestamp>,  tag?:string, date?:string, status?:string) => {
  const user = await getUserByUid(uid)
  if(!user){
    throw new NotFoundException(`[ Nom035 | Follow-ups ] User with uid: ${uid} not found`)
  }
  return await getFollowUps(user.email, coach, tag, date, status)

}
