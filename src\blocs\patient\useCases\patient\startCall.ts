import { appointmentServices, call, callToken, countries, dependantUid, TSpecialties } from "@umahealth/entities"
import { AppointmentRepository, PatientRepository } from "@umahealth/repositories"

export async function startCall(assignation_id: string, call: callToken, country: countries, dependantUid: dependantUid, service: appointmentServices, specialty: TSpecialties | false, uid: string) {
  const assignationPath = AppointmentRepository.getPath(service, country)

  const callObj: call = {
    activeUid: uid,
    assignationPath: `${assignationPath}/${assignation_id}`,
    assignation_id: assignation_id,
    calling: true,
    cuit: service,
    dependant: dependantUid,
    room: call.session,
    token: call.token,
    type: specialty ? specialty : "bag"
  }
  return await PatientRepository.createCall(uid, callObj)
}
