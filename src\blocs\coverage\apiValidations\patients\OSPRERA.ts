import axios, { AxiosResponse } from "axios"
import { Logger } from "@nestjs/common"

export interface OspreraValidateSuccess {
  FechaConsulta: Date;
  Id: string;
  BocaDeExpendio: string;
  UsuarioApodo: string;
  CUITTitular: string;
  NOMBRETitular: string;
  Documento: string;
  TipoDocumento: string;
  TipoDocumentoDescripcion: string;
  PlanBeneficiario: string;
  NroPlan: number;
  Secuencia: string;
  Sexo: string;
  Uatre: string;
  EsVirtual: string;
  Nombre: string;
  EstadoCobertura: string;
  EnCondiciones: string;
  OrigenBeneficiario: string;
  Localidad: string;
  Provincia: string;
  FechaDeAlta: string;
  FechaNacimiento: Date;
  Edad: string;
  FechaCarencia1: null;
  FechaCarencia2: null;
  AplicaCoseguro: string;
  CoberturaToString: string;
  CarenciaToString: string;
  GetEstado: string;
  TipoConsulta: string;
  MesConsulta: null;
  Correlativo: string;
  Haydatos: boolean;
  Parentesco: string;
  Discapacidad: null;
  id_beneficiario: string;
  TipoAfiliado: string;
  Rural: string;
  EsASIMM: boolean;
  EsNoRural: boolean;
  TraspasoVigente: boolean;
  ConsultaExcepcional: boolean;
  RangoTraspaso: string;
  TipoBeneficiarioDetalle: string;
  FechaVencimientoDiscapacidad: null;
  FechaVencimientoPMI: null;
  BocaExpendioDescripcion: string;
  FechaNacimientoToString: string;
  EdadToString: string;
  UatreToString: string;
}

export interface OspreraValidateError {
  Message: string;
}

export interface GetOspreraTokenResponse {
  JWToken: string;
  Exito: boolean;
  Mensaje: string;
  payload: null;
}



export const validateOspreraPatient = async (dni: string): Promise<boolean> => {
  const ospreraToken = await getOspreraToken()
  const { urlValidate } = JSON.parse(process.env.OSPRERA_AFFILIATE_VALIDATION_AUTH)

  const url = `${urlValidate}?Periodo=&idProvincias=1&NumeroDocumento=${dni}`

  const headers = {
    Accept: "application/json",
    Authorization: `Bearer ${ospreraToken}`,
  }

  try {
    const response = await axios.get<AxiosResponse<OspreraValidateSuccess[] | OspreraValidateError>>(`${url}`, {
      headers,
    })

    if (Array.isArray(response.data) && response.data.length > 0) {
      const patient = response.data[0]
      if (patient.EstadoCobertura === "No") return false
      if (patient.EstadoCobertura === "Si") return true
    }
    return false
  } catch (error) {
    Logger.error(`[${validateOspreraPatient.name}] -> Error while fetching OSPRERA padron with dni ${dni}`)
    return false
  }
}

const getOspreraToken = async (): Promise<string> => {
  const { urlAuth, user, password } = JSON.parse(process.env.OSPRERA_AFFILIATE_VALIDATION_AUTH)

  try {
    const response = await axios.post<GetOspreraTokenResponse>(urlAuth, {
      userName: user,
      password: password,
      dominio_id: 2,
    })
    return response.data.JWToken
  } catch (error) {
    Logger.error("[getOspreraToken] -> Error while generating OSPRERA token")
    throw new Error("Error while fetching OSPRERA token")
  }
}
