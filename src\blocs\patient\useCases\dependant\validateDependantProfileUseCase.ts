import { NotFoundException } from "@nestjs/common"
import { IPatient, IValidatePatient } from "@umahealth/entities"
import { DependantRepository } from "@umahealth/repositories"
import * as moment from "moment"
import { ValidateDni } from "src/utils/patient/validateDni"

export const ValidateDependantProfileUseCase = async (uid: string): Promise<IValidatePatient> => {
  const missingField: Array <keyof Pick<IPatient, "dni"|"dob">> = []
  const dependant = await DependantRepository.getByUidFromDependant(uid)
  if(!dependant) throw new NotFoundException(`Dependant not found with uid: ${uid}`)

  // Validate DNI
  switch(dependant.country){
  case("AR"):{
    if(dependant?.dni === "" || !ValidateDni(dependant?.dni) || dependant?.dni === null){
      missingField.push("dni")
    }
    break
  }
  default: {
    if(dependant?.dni === "" || dependant?.dni === undefined || dependant?.dni === null){
      missingField.push("dni")
    }
  }
  }

  // Validate date of birth -  format and years
  if(dependant?.dob !== undefined && !dependant?.dob?.match(/^(\d{4}-\d{2}-\d{2})/)){
    if( moment(dependant?.dob).toDate() > moment().toDate() || moment(dependant?.dob).toDate() < moment("1900-01-01").toDate()){
      missingField.push("dob")
    }
  }else{
    missingField.push("dob")
  }

  const profile: IValidatePatient = {
    profile: missingField
  }

  return profile
}
