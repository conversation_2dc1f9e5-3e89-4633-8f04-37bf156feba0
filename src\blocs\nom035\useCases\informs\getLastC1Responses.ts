import { NotFoundException } from "@nestjs/common"
import { getUserByUid } from "@umahealth/auth"
import { FieldSet } from "airtable"
import { IQuestion, extractC1Responses } from "src/utils/airtable/utils/functions"

export const getLastC1Responses = async (c1Questions: FieldSet[], c1Responses: FieldSet[], uid: string, email?: string) => {
  if(!email) {
    const user = await getUserByUid(uid)
    if(!user){
      throw new NotFoundException(`[ Nom035 | getLastC1Responses ] User with uid: ${uid} not found`)
    }
    email = user.email
  }

  const c1Forms = c1Responses.filter(response => (response["correo (from nomina)"]as string[])[0] === email)
  const form_found = c1Forms[c1Forms.length - 1]

  if(!c1Forms ||!c1Forms.length || !form_found){
    return {formCompleted: false, questions: [] as IQuestion[], requireValidation: null as boolean, ATS: null as boolean, date: null as string }
  }

  return extractC1Responses(c1Questions, form_found)
}
