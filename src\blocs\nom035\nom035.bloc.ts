import { Inject, Injectable, InternalServerErrorException, Logger } from "@nestjs/common"
import { ICreateNom035User, ICreateNomForm, IExtraFilters, INom035UserFilters, INom035_csvBody, IUpdateCorporate } from "./interfaces"
import { ICreateNomCorporate, INomAdminUsers, IPortalUser } from "@umahealth/entities"
import { createUser, createCorporate, changeUserStatus, updateUser, createUsersByCSV, getUserById, getUsers, getTotalUsers, getCorporateById, updateCorporate, changeRequirePasswordChangeStatus, getAssignedEmployees, getC3ResponsesToCoach, getC1ResponsesToCoach, getIndiceResponsesToCoach, employeeC3Answers, employeeC3Details, employeeC3SpeedometerAndRiskyFactors, getLastC1Responses, getInformsUsers, getUserInformation, getFollowUpsMethod, postFollowUp, getUserMedicalRecord, postUserMedicalRecord, getFollowUpsByCorporateId, getUsersFromAT, getC1Responses, getC3Responses, getC3Questions, getIndexResponses, getIndexQuestions, getC1GeneralStatistics, getC3CategoriesStatisticsToSpeedometerGraph, getC3GeneralCategoriesStatistics, getC3GeneralDomainsStatistics, getIndiceGeneralStatisticsToSpeedometerGraph, getIndiceGeneralStatistics, getIndiceSubdimensionsStatistics, getCorporates, updateCorporateCache, updateUsersCache, updateIndexResponsesCache, updateC1ResponsesCache, updateC3ResponsesCache, updateCacheCron, getC1Questions, updateIndexQuestionsCache, updateC1QuestionsCache, updateC3QuestionsCache, createUsersAuthAccount, getUsernamesByProximity, getPendingFormUrls, updateUsersCorporateUid, getEmployeesStatistics, getFormResponses } from "./useCases"
import { IEditUserBody } from "src/portal-app/nom035/user/user.entities"
import { getEmployeesC2FormResponses } from "src/utils/airtable"
import { Timestamp } from "@google-cloud/firestore"
import { IAddFollowUpBody, ICoachMedicalRecordBody } from "src/portal-app/nom035/coach/coach.entities"
import { Redis } from "ioredis"
import { fetchAndCacheData } from "src/utils/redis/functions"
import { FieldSet } from "airtable"
import { areaFilter } from "./utils/functions"
import { EventEmitter2 } from "@nestjs/event-emitter"
import { getCorporateAttributes } from "./useCases/corporate/getCorporateAttributes"
import { createNomForm } from "./useCases/form/createForm"
import { uploadCorporateLogo } from "./useCases/uploadCorporateLogo"
import { updateUserEmail } from "./useCases/updateUserEmail"
import { IFormFilters, IFormResponses } from "src/portal-app/nom035/statistics/statistics.entities"
import { createContactForm } from "./useCases/form/createContactForm"
import { IContactForm } from "src/public/nom/entities"

@Injectable()
export class Nom035Bloc{
  constructor(
    @Inject("REDIS_NOM035") private readonly redisNom035: Redis
  ){}

  async syncNomAirtableForm(data: ICreateNomForm ) {
    try {
      await createNomForm(data)
      Logger.log(`[${Nom035Bloc.name} | ${this.syncNomAirtableForm.name}] -> Succesfully sync form from: ${data.email}!`)
    } catch (error) {
      Logger.error(`[${Nom035Bloc.name} | ${this.syncNomAirtableForm.name}] -> Failed while creating form ${data.form} from: ${data.email} in corporate ${data.corporate}, ${error}`)
    }
  }

  async createCorporate(data: Partial<ICreateNomCorporate>, logo: Express.Multer.File) {
    try {
      const defaultUsersIds: INomAdminUsers = {admin1: process.env.NOM_DEFAULT_ADMIN1, admin2: process.env.NOM_DEFAULT_ADMIN2, coach1: process.env.NOM_DEFAULT_COACH1}
      const logoToken = await uploadCorporateLogo(logo, data.corporateName)
      await createCorporate({...data, isFull: Boolean(data?.isFull), authUsers: defaultUsersIds, logo: logoToken} as ICreateNomCorporate)
      Logger.log(`[${Nom035Bloc.name} | ${this.createCorporate.name}] -> Succesfully created nom corporate, ${data.corporateName}`)
      return {status: 201, message: "Succesfully created nom corporate"}
    } catch (error) {
      Logger.error(`[${Nom035Bloc.name} | ${this.createCorporate.name}] -> Error while creating nom corporate, ${error}`)
      throw new InternalServerErrorException(error)
    }
  }

  async createUser(data: Partial<ICreateNom035User>, corporateId:string) {
    return await createUser(data, corporateId)
  }

  async createUsersByCSV(corporateInfo: {id: string, value: string}, usersFromCsv: INom035_csvBody[], eventEmitter: EventEmitter2){
    return await createUsersByCSV(corporateInfo, usersFromCsv, eventEmitter)
  }

  async createUsersAuthAccount(users: INom035_csvBody[]) {
    return await createUsersAuthAccount(users)
  }

  async getTotalUsers(corporateId: string, filters?: INom035UserFilters) {
    return await getTotalUsers(corporateId, filters)
  }

  async getUsers(users: FieldSet[], corporateId: string, filters?: INom035UserFilters){
    return await getUsers(users, corporateId, filters)
  }

  async getUsersFromAT(corporateId: string, active = true, filters?: IFormFilters) {
    const users = await fetchAndCacheData(this.redisNom035, `corporate:${corporateId}:users`, () => getUsersFromAT(corporateId))
    return users.filter(user => {
      const areaMatch = filters?.area ? areaFilter(corporateId, user, filters?.area) : true
      const branchMatch = filters?.branch ? user.sucursal === filters?.branch : true
      const activeMatch = active ? user.activo : true
      return areaMatch && activeMatch && branchMatch
    })
  }

  async getUsernamesByProximity(corporateId: string, extraFilters: Partial<IExtraFilters>) {
    const response = await getUsernamesByProximity(corporateId, extraFilters)
    return response
  }

  async getInformsUsers(users: FieldSet[], corporateId: string, extraFilters?: IExtraFilters){
    return await getInformsUsers(users, corporateId, extraFilters)
  }

  async getInformsUserInformation(uid: string){
    return await getUserInformation(uid)
  }

  async getUserById(userId: string){
    return await getUserById(userId)
  }

  async changeUserStatus(uid: string, users: FieldSet[], corporateId: string){
    return await changeUserStatus(uid, users, corporateId)
  }

  async getCorporates() {
    return await getCorporates()
  }

  // Cache
  async updateCacheCron() {
    return await updateCacheCron()
  }

  async updateCorporateCache(corporate: string) {
    return await updateCorporateCache(this.redisNom035, corporate)
  }

  async updateUsersCache(corporate: string) {
    return await updateUsersCache(this.redisNom035, corporate)
  }

  async updateIndexQuestionsCache() {
    return await updateIndexQuestionsCache(this.redisNom035)
  }

  async updateIndexResponsesCache(corporate: string) {
    return await updateIndexResponsesCache(this.redisNom035, corporate)
  }

  async updateC1QuestionsCache() {
    return await updateC1QuestionsCache(this.redisNom035)
  }

  async updateC1ResponsesCache(corporate: string) {
    return await updateC1ResponsesCache(this.redisNom035, corporate)
  }

  async updateC3QuestionsCache() {
    return await updateC3QuestionsCache(this.redisNom035)
  }

  async updateC3ResponsesCache(corporate: string) {
    return await updateC3ResponsesCache(this.redisNom035, corporate)
  }


  async getCorporateById(corporateId: string) {
    const corporate = await fetchAndCacheData(this.redisNom035, `corporate:${corporateId}:details`, () => getCorporateById(corporateId))
    return corporate
  }

  async getCorporateAttributes(corporateId: string) {
    const corporateAttributes = await getCorporateAttributes(corporateId)
    return corporateAttributes
  }

  async updateCorporate(corporateId: string, updateData: IUpdateCorporate) {
    return await updateCorporate(corporateId, updateData)
  }

  async updateUserEmail(uid: string, corporateId: string, email: string, data: {correo: string}) {
    return await updateUserEmail(uid, corporateId, email, data)
  }

  async updateUser(uid: string, data: Partial<IEditUserBody>, corporate: string){
    return await updateUser(uid, data, corporate)
  }

  async getC1Questions(){
    const c1Questions = await fetchAndCacheData(this.redisNom035, "questions:c1", getC1Questions)
    return c1Questions
  }

  async getC1Responses(corporateId: string){
    const c1Responses = await fetchAndCacheData(this.redisNom035, `corporate:${corporateId}:form:c1:responses`, () => getC1Responses(corporateId))
    return c1Responses
  }

  async getC3Responses(corporateId: string){
    const c3Responses = await fetchAndCacheData(this.redisNom035, `corporate:${corporateId}:form:c3:responses`, () => getC3Responses(corporateId))
    return c3Responses
  }

  async getC3Questions(){
    const c3Questions = await fetchAndCacheData(this.redisNom035, "questions:c3", getC3Questions)
    return c3Questions
  }

  async getIndexResponses(corporateId: string){
    const indexResponses = await fetchAndCacheData(this.redisNom035, `corporate:${corporateId}:form:index:responses`, () => getIndexResponses(corporateId))
    return indexResponses
  }

  async getIndexQuestions(){
    const indexQuestions = await fetchAndCacheData(this.redisNom035, "questions:index", getIndexQuestions)
    return indexQuestions
  }

  async getFormC2ResponsesAmount(corporateId: string, areaFilter?: string){
    return await getEmployeesC2FormResponses(corporateId, areaFilter)
  }

  getTotalFormResponses(users: FieldSet[], formResponses: IFormResponses, filters: IFormFilters) {
    return getFormResponses(users, formResponses, filters)
  }

  getC3CategoriesStatisticsToSpeedometerGraph(users: FieldSet[], c3Responses: FieldSet[], filters?: IFormFilters) {
    return getC3CategoriesStatisticsToSpeedometerGraph(users, c3Responses, filters)
  }

  getIndiceGeneralStatisticsToSpeedometerGraph(users: FieldSet[], indexResponses: FieldSet[], filters?: IFormFilters) {
    return getIndiceGeneralStatisticsToSpeedometerGraph(users, indexResponses, filters)
  }

  getIndiceGeneralStatistics(users: FieldSet[], indexResponses: FieldSet[], filters: IFormFilters){
    return getIndiceGeneralStatistics(users, indexResponses, filters)
  }

  getIndiceSubdimensionsStatistics(users: FieldSet[], indexResponses: FieldSet[], indexQuestions: FieldSet[], dimension: string, filters?: IFormFilters){
    return getIndiceSubdimensionsStatistics(users, indexResponses, indexQuestions, dimension, filters)
  }

  getC1GeneralStatistics(users: FieldSet[], c1Responses: FieldSet[], filters?: IFormFilters) {
    return getC1GeneralStatistics(users, c1Responses, filters)
  }

  getEmployeesStatistics(corporateId: string){
    return getEmployeesStatistics(corporateId)
  }

  async changeRequirePasswordChangeStatus(uid: string){
    return await changeRequirePasswordChangeStatus(uid)
  }

  async getAssignedEmployees(email: string, corporateId: string){
    return await getAssignedEmployees(email, corporateId)
  }

  async getC3ResponsesToCoach(uid: string){
    return await getC3ResponsesToCoach(uid)
  }

  async getC1ResponsesToCoach(uid: string){
    return await getC1ResponsesToCoach(uid)
  }

  async getIndiceResponsesToCoach(uid: string){
    return await getIndiceResponsesToCoach(uid)
  }

  getC3GeneralCategoriesStatistics(users: FieldSet[], c3Responses: FieldSet[], filters?: IFormFilters) {
    return getC3GeneralCategoriesStatistics(users, c3Responses, filters)
  }

  getC3GeneralDomainsStatistics(users: FieldSet[], c3Responses: FieldSet[], c3Questions: FieldSet[], filters?: IFormFilters, category?: string) {
    return getC3GeneralDomainsStatistics(users, c3Responses, c3Questions, filters, category)
  }

  async getEmployeeC3SpeedometerAndRiskyFactors(users: FieldSet[], c3Questions: FieldSet[], c3Responses: FieldSet[], uid: string, email?: string){
    return await employeeC3SpeedometerAndRiskyFactors(users, c3Questions, c3Responses, uid, email)
  }

  async getEmployeeC3Answers(c3Questions: FieldSet[], c3Responses: FieldSet[], uid: string, email?: string){
    return await employeeC3Answers(c3Questions, c3Responses, uid, email)
  }

  async getEmployeeC3Details(c3Questions: FieldSet[], c3Responses: FieldSet[], uid: string, email?: string){
    return await employeeC3Details(c3Questions, c3Responses, uid, email)
  }

  async getLastC1Responses(c1Questions: FieldSet[], c1Responses: FieldSet[], uid:string, email?: string){
    return await getLastC1Responses(c1Questions, c1Responses, uid, email)
  }

  async getFollowUps(uid: string, coach: IPortalUser<Timestamp>,  tag?:string, date?:string, status?:string){
    return await getFollowUpsMethod(uid, coach, tag, date, status)
  }

  async addFollowUp(uid: string, coach: IPortalUser<Timestamp>, data: IAddFollowUpBody){
    return await postFollowUp(uid, coach, data)
  }

  async getMedicalRecord(uid: string, coach: IPortalUser<Timestamp>){
    return await getUserMedicalRecord(uid, coach)
  }

  async postMedicalRecord(uid: string, coach: IPortalUser<Timestamp>, medicalRecordData: ICoachMedicalRecordBody){
    return await postUserMedicalRecord(uid, coach, medicalRecordData)
  }

  async getFollowUpsByCorporateId(corporateId: string) {
    return await getFollowUpsByCorporateId(corporateId)
  }

  async getPendingFormUrls(users: FieldSet[], c1Responses: FieldSet[], c3Responses: FieldSet[]) {
    return await getPendingFormUrls(users, c1Responses, c3Responses)
  }

  async updateUsersCorporateUid(users: FieldSet[]) {
    return await updateUsersCorporateUid(users)
  }

  async createContactForm(data: IContactForm) {
    return await createContactForm(data)
  }
}
