import { DengueChatbotConversation, DengueChatbotMessage } from "@umahealth/sql-models"
import { chatMessages } from "./chatMessagesDengue"
import { getLatestChat } from "./getLatestChatDengue"

export const chat = async (uid: string) => {
  // Verifico si el usuario ya tiene un chat abierto
  const latestChat = await getLatestChat(uid)
  if (latestChat) {
    const messages = await chatMessages(latestChat.id)
    return { chat: latestChat.dataValues, messages }
  }

  // Si no existe creo un chat nuevo
  const newChat = await DengueChatbotConversation.create({
    uid,
    anonymous: false,
    email: null,
  })

  const welcomeMessage = await DengueChatbotMessage.create({
    conversation_id: newChat.id,
    text_message:
    "Hola, soy **DengueBot**, tu asistente de IA. Por favor, comunícate conmigo diariamente para informarme sobre cómo te sientes, hasta haber concluido las 48 horas sin fiebre.  \n&nbsp;  \nPuedo ayudarte con:\n - Información y dudas respecto al dengue.\n- Identificación de signos de alarma que sugieran que tu condición podría estar empeorando.\n- Recomendaciones sobre cuándo es conveniente acudir a la guardia o realizar teleconsultas.  \n&nbsp;  \n**Importante:**  \nEs crucial recordar que DengueBot está aquí para brindarte apoyo y orientación, pero **NO reemplaza la atención médica profesional.** Mi asesoramiento se ofrece únicamente como guía y no debe ser el único recurso para tomar decisiones relacionadas con tu salud.",
    rol: "ai",
  })

  return { chat: newChat.dataValues, messages: [welcomeMessage] }
}
