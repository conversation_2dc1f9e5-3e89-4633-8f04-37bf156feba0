import { BadRequestException } from "@nestjs/common"
import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance, dependantUid } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"

export const DependantGetPrimaryUseCase = async (dependantUid: dependantUid): Promise<IHealthInsurance<Timestamp>> => {
  if (!dependantUid) throw new BadRequestException("[ Coverages | dependant | getPrimary ] => dependantUid must be valid string")
  return await HealthInsuranceRepository.getDepentantPrimary(dependantUid)
}
