import { Logger } from "@nestjs/common"
import * as Airtable from "airtable"
import { IAirtableContactData } from "src/public/nom/entities"
import { getMinifiedItem } from "src/utils/airtable"

export const saveContactInfo = async (data: IAirtableContactData) => {
  try {
    Airtable.configure({ apiKey: process.env.AIRTABLE_NOM_TOKEN })
    const base = Airtable.base("appwPQFBZYTduO9x0")
    const table = base("Lead")
    const newRecords = await table.create([{fields: { ...data }}])

    return getMinifiedItem(newRecords[0])
  } catch (err) {
    Logger.error(`Error guardando info de contacto en Airtable, ${err}`)
    throw new Error(`Error guardando info de contacto en Airtable, ${err}`)
  }
}
