import { FieldSet } from "airtable"
import { IFormFilters, IFormResponses, INom035Form } from "src/portal-app/nom035/statistics/statistics.entities"
import { getUsersWithFormByDate } from "../../utils/functions"

export const getFormResponses = (usersArray: FieldSet[], formResponses: IFormResponses, filters?: IFormFilters) => {
  const {c1: c1Responses, c3: c3Responses, indice: indexResponses} = formResponses

  const c1TotalResponses = getUsersWithFormByDate(usersArray, c1Responses, INom035Form.c1, filters?.date)?.length
  const c3TotalResponses = getUsersWithFormByDate(usersArray, c3Responses, INom035Form.c3, filters?.date)?.length
  const indexTotalResponses = getUsersWithFormByDate(usersArray, indexResponses, INom035Form.index, filters?.date)?.length

  const treshold_amount = Math.floor(usersArray.length * 0.1)

  const getFormStats = (totalResponses: number) => {
    return {
      enoughResponses: (totalResponses >= treshold_amount || filters?.area || filters?.branch) && totalResponses > 0,
      completed: totalResponses,
      incompleted: usersArray.length - totalResponses,
      participation: totalResponses > 0 ? Number(((totalResponses / usersArray.length) * 100).toFixed(2)) : totalResponses
    }}

  return {
    c1: getFormStats(c1TotalResponses),
    c3: getFormStats(c3TotalResponses),
    indice: getFormStats(indexTotalResponses),
  }
}
