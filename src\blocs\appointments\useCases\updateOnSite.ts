import { Timestamp } from "@google-cloud/firestore"
import { ConflictException, Logger, NotFoundException } from "@nestjs/common"
import { action, countries, IOnSiteAppointment, IOnSitePatient, IRequest, OnSiteAppointment, practice } from "@umahealth/entities"
import { AppointmentRepository, IDocumentList, RequestRepository, saveBatch } from "@umahealth/repositories"
import * as moment from "moment"

function removeUndefinedFields<T extends object>(obj: T): Partial<T> {
  const cleanObj: Record<string, unknown> = {}

  for (const [key, value] of Object.entries(obj)) {
    if (value === undefined) {
      continue
    }

    // Check if value is a Timestamp (checking for the presence of seconds and nanoseconds)
    if (value && typeof value === "object" && "seconds" in value && "nanoseconds" in value) {
      cleanObj[key] = value
    }
    // Check if value is an object but not an array
    else if (value && typeof value === "object" && !Array.isArray(value)) {
      cleanObj[key] = removeUndefinedFields(value)
    } else {
      cleanObj[key] = value
    }
  }

  return cleanObj as Partial<T>
}

export const updateOnSiteAppointmentUseCase = async (
  action: action,
  assignationId: string,
  country: countries,
  specialization: string,
  patient: IOnSitePatient,
  practices: Array<practice>,
  uid: string,
  service = "consultorio"
): Promise<IDocumentList<Partial<IOnSiteAppointment<Timestamp>>>> => {
  const [receivedDatetime, receivedDoctor] = assignationId.split("_")

  // TODO: fix timezones in appointments to post or update (3 hours difference in "time" and "dt_assignation" fields)
  const hour: string = moment(receivedDatetime, "YYYYMMDDhhmmss").format("HH:mm")
  const newDtAssignation = moment(receivedDatetime, "YYYYMMDDhhmmss").add(3, "hours").toDate()

  const appointments = await AppointmentRepository.getOnSiteAppointmentsByDatetimeAndDoctorId("consultorio", country, receivedDoctor, newDtAssignation)


  if (appointments.length === 0) {
    throw new NotFoundException(`No appointments found for the assignationId ${assignationId}`)
  }

  if (appointments.length === 2) {
    Logger.log("If appointments.length === 2")
    // revisa nombre y estado previo a asignar turno para no repetir paciente
    if ((appointments[0].patient?.fullname === patient.fullname && appointments[0].state === "ASSIGN") || (appointments[1].patient?.fullname === patient.fullname && appointments[1].state === "ASSIGN")) {
      throw new ConflictException(`The patient ${uid} is already assigned to this appointment`)
    }
    if (appointments[0].state === "ASSIGN" && appointments[1].state === "ASSIGN") {
      throw new ConflictException(`Both appointments are already assigned for the assignationId ${assignationId}`)
    }
    if (appointments[0].state === "FREE" || appointments[1].state === "FREE") {
      Logger.log("appointments[0].state === FREE || appointments[1].state === FREE")
      const appointment = appointments.filter(appointment => appointment.state === "FREE")[0]
      const newAdminActions = typeof (appointment.adminActions) === "object" ? appointment.adminActions.concat(action) : [action]
      const updatedOnSiteAppointment = new OnSiteAppointment({
        ...appointment,
        patient: {
          ...patient,
          uid: uid,
        },
        state: "ASSIGN",
        time: hour,
        practices,
        timestamps: {
          ...appointment.timestamps,
          dt_assignation: Timestamp.fromDate(newDtAssignation)
        },
        service,
        adminActions: newAdminActions,
      })
      return await AppointmentRepository.updateOnSiteAppointment(
        country,
        updatedOnSiteAppointment.assignation_id,
        removeUndefinedFields(updatedOnSiteAppointment)
      )
    }
  }

  const lastAppointment = appointments.at(-1)

  if (lastAppointment.state === "FREE") {
    Logger.log("lastAppointment.state === FREE")
    const newAdminActions = typeof (lastAppointment.adminActions) === "object" ? lastAppointment.adminActions.concat(action) : [action]

    const updatedOnSiteAppointment = new OnSiteAppointment({
      ...lastAppointment,
      assignation_id: assignationId,
      datetime: receivedDatetime,
      especialidad: specialization,
      patient: {
        ...patient,
        uid: uid,
      },
      state: "ASSIGN",
      time: hour,
      timestamps: {
        ...lastAppointment.timestamps,
        dt_assignation: Timestamp.fromDate(newDtAssignation)
      },
      adminActions: newAdminActions,
      overbooked: false,
      practices,
      service,
      slot: 1
    })

    return await AppointmentRepository.updateOnSiteAppointment(
      country,
      assignationId,
      removeUndefinedFields(updatedOnSiteAppointment)
    )
  }

  if (lastAppointment.state === "ASSIGN") {
    if (lastAppointment.patient?.fullname === patient.fullname) {
      throw new ConflictException(`The patient ${uid} is already assigned to this appointment`)
    }
    Logger.log("lastAppointment.state === ASSIGN")

    const newAssignationId = `${assignationId}_1`
    const updatedOnSiteAppointment = new OnSiteAppointment({
      ...lastAppointment,
      assignation_id: newAssignationId,
      datetime: receivedDatetime,
      patient: {
        ...patient,
        uid: uid,
      },
      state: "ASSIGN",
      time: hour,
      timestamps: {
        ...lastAppointment.timestamps,
        dt_assignation: Timestamp.fromDate(newDtAssignation)
      },
      adminActions: lastAppointment.adminActions.concat(action),
      overbooked: true,
      practices,
      ref: `${lastAppointment.ref}_"1"`,
      service,
      slot: lastAppointment.slot + 1
    })

    return await AppointmentRepository.updateOnSiteAppointment(
      country,
      newAssignationId,
      removeUndefinedFields(updatedOnSiteAppointment)
    )
  }
}

export const updateOnsiteAppointmentAndRequestUseCase = async (country: countries, assignationId: string, appointment: IOnSiteAppointment) => {
  const cleanAppointment = removeUndefinedFields(appointment)
  const responseAppointment = await AppointmentRepository.updateOnSiteAppointment(
    country,
    assignationId,
    cleanAppointment
  )
  const responseRequest = await RequestRepository.update(
    assignationId,
    "onsite",
    cleanAppointment as unknown as Partial<IRequest>
  )

  await saveBatch<Partial<IOnSiteAppointment<Timestamp>> | Partial<IRequest<Timestamp>>>([responseRequest, responseAppointment])

  return {
    updated: true,
    assignationId,
    message: "Appointment updated successfully",
  }
}
