import { documentType } from "@umahealth/entities"

export const validationPrompts: Partial<Record<documentType, string>> = {
  DNI: `Utilizando la siguiente imagen del documento de identidad, extrae y presenta la siguiente información en un formato estructurado json:
    Nombre completo (campo llamado nombre)
    Número de documento (campo llamado dni. Elimina los puntos). El número de documento se encuentra etiquetado como "Documento" o "Document" y está ubicado en la parte inferior izquierda del DNI. No debe incluir el "Número de trámite" ni el "Número de CUIL/CUIT", que están en otras partes del documento. El número no debe tener mas de 8 dígitos
    Sexo (campo llamado sexo)`,
  Pasaporte: `Utilizando la siguiente imagen del pasaporte, extrae y presenta la siguiente información en un formato estructurado json:
    Nombre completo (campo llamado nombre)
    Número de pasaporte (campo llamado dni. Elimina los puntos)
    Sexo (campo llamado sexo)`,
  // TODO: prompts para LC, LI, CI
}