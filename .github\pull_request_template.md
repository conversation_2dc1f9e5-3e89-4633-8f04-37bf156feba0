## Tipo de PR
<!-- Idealmente 1, maximo 3. --> 

Marca la opcion que corresponde a esta PR:
- [ ] Fix
- [ ] Feature
- [ ] Actualizacion de estilo (nombres de variables, textos por ejemplo)
- [ ] Cambios relacionados al build (paquetes por ejemplo)
- [ ] Documentacion
- [ ] <PERSON><PERSON><PERSON>
- [ ] <PERSON><PERSON> (desarrolle): 

<br><br>

## Proposito de la PR
<!-- Cual es mi objetivo o tarea? Que estoy solucionando? --> 

### Cual es el comportamiento actual?
<!-- Que hice? (si tome alguna desicion, por que decidi hacerlo asi) -->

### Que cambia?
<!-- Si el cambio hace lo que esperaba, como funciona ahora? Por que funciona? -->
<!-- Si el cambio no funciona, que intente? Que me queda por intentar -->

<br><br>

## Riesgo tecnico
### Introduzco un breaking change
<!-- Si estoy introduciendo un cambio grande, explicar que pasa en caso de error o fallo -->

- [ ] Si, y lo testee en todos los ambientes anteriores
- [ ] Si, y confio en que funciona / lo estoy testeando con esta PR
- [ ] No estoy segurx
- [ ] No

#### En caso de riesgo tecnico, explicar aca que medidas tome para prevenir accidentes



## Otros
<!-- Cualquier informacion extra que creas relevante sobre esta PR -->
