import { Logger } from "@nestjs/common"
import { gender, IDependant } from "@umahealth/entities"
import axios, { AxiosResponse } from "axios"
import { ICorporateBody } from "src/patient-app/coverage/coverage.entities"
import { getDT } from "src/patient-app/dependant/utils/functions"
import { Timestamp } from "@google-cloud/firestore"
import { format, parse } from "date-fns"

/**
 * Represents a response from the UDEM affiliate validation service.
 */
interface UdemAffiliate {
  CONTRATO: string;
  APELLIDO_NOMBRES: string;
  TIPO_DOC: string;
  NRO_DOC: string;
  EMAIL: string | null;
  CUIT: string | null;
  CUIL_AUX: string;

  /**
   * Fecha de desafiliación (formato: DD-MMM-YY, e.g., "06-NOV-03").
   */
  FECHA_DESAFILIACION: string | null;

  /**
   * Fecha de afiliación (formato: DD-MMM-YY, e.g., "29-AUG-06").
   */
  FECHA_AFILIACION: string;

  SEXO: gender;

  /**
   * <PERSON><PERSON> de nacimiento (formato: DD-MMM-YY, e.g., "22-OCT-78").
   */
  FECHA_NACIMIENTO: string;

  TELEFONO: string | null;

  /**
   * Tipo de afiliado ("TITULAR" o "ADHERENTE").
   */
  TIPO: "TITULAR" | "ADHERENTE";
}


/**
 * Represents an error response from the UDEM affiliate validation service.
 */
interface UdemErrorResponse {
  message: string;
}

/**
 * Validates if a patient is affiliated with UDEM based on their DNI.
 *
 * @param {string} dni - The DNI of the patient to validate.
 * @returns {Promise<boolean>} A promise that resolves to `true` if the patient is found, otherwise `false`.
 * @throws {Error} If there is an issue fetching data from the UDEM service.
 */
export const validateUdemPatient = async (dni: string): Promise<boolean> => {
  let response: AxiosResponse<UdemAffiliate[] | UdemErrorResponse>
  const authConfig = process.env.UDEM_AFFILIATE_VALIDATION_AUTH

  if (!authConfig) {
    Logger.error(`[${validateUdemPatient.name}] -> Missing UDEM_AFFILIATE_VALIDATION_AUTH environment variable`)
    throw new Error("Missing UDEM_AFFILIATE_VALIDATION_AUTH environment variable")
  }

  const { url, key } = JSON.parse(authConfig)
  const headers = {
    Accept: "application/json",
    Authorization: key,
  }

  try {
    response = await axios.get<UdemAffiliate[]>(`${url}?dni=${dni}`, { headers })
  } catch (error) {
    Logger.error(`[${validateUdemPatient.name}] -> Error while fetching UDEM padron with id ${dni}`)
    return false
  }

  if (response.status === 200 && Array.isArray(response.data) && response.data.some((user) => user.NRO_DOC === dni)) {
    Logger.log(`[${validateUdemPatient.name}] -> Successfully found user with id ${dni}`)
    return true
  }

  Logger.log(`[${validateUdemPatient.name}] -> User with id ${dni} not found`)
  return false
}

/**
 * Retrieves the health coverage information for a patient affiliated with UDEM.
 *
 * @param {string} dni - The DNI of the patient to validate.
 * @returns {Promise<ICorporateBody>} A promise resolving with the formatted health coverage details.
 * @throws {Error} If the DNI is invalid or the API request fails.
 */
export async function getUdemHealthCoverage(dni: string): Promise<ICorporateBody> {
  if (!dni) {
    throw new Error("DNI is required")
  }

  const authConfig = process.env.UDEM_AFFILIATE_VALIDATION_AUTH
  if (!authConfig) {
    Logger.error("[getUdemHealthCoverage] -> Missing UDEM_AFFILIATE_VALIDATION_AUTH environment variable")
    throw new Error("Missing UDEM_AFFILIATE_VALIDATION_AUTH environment variable")
  }

  const { url, key } = JSON.parse(authConfig)
  const headers = {
    Accept: "application/json",
    Authorization: key,
  }

  try {
    const response: AxiosResponse<UdemAffiliate[] | UdemErrorResponse> = await axios.get<UdemAffiliate[]>(`${url}?dni=${dni}`, { headers })

    if (response.status === 200 && Array.isArray(response.data) && response.data.length > 0) {
      const user = response.data.find((u) => u.NRO_DOC === dni)

      if (!user) {
        Logger.log(`[getUdemHealthCoverage] -> User with DNI ${dni} not found`)
        throw new Error("User not found")
      }

      const corporateBody: ICorporateBody = {
        affiliate_id: user.CONTRATO,
        name: "UDEM",
        plan: "UDEM0", // No especificado en la respuesta de UDEM
        userInput: "UDEM",
        credentialVersion: "0", // No especificado en la respuesta de UDEM
      }

      Logger.log(`[getUdemHealthCoverage] -> Found user with DNI ${dni}`)
      return corporateBody
    }

    Logger.log(`[getUdemHealthCoverage] -> User with DNI ${dni} not found`)
    throw new Error("User not found")
  } catch (error) {
    Logger.error(`[getUdemHealthCoverage] -> Error fetching UDEM padron for DNI ${dni}: ${error.message}`)
    throw new Error("Failed to fetch health coverage data")
  }
}

/**
 * Retrieves all dependants (adherentes) for a given DNI from the UDEM affiliate validation service.
 *
 * @param {string} dni - The DNI of the patient (titular o adherente).
 * @returns {Promise<Omit<IDependant, 'core_id'>[]>} A promise resolving with an array of dependants.
 * @throws {Error} If the DNI is invalid or the API request fails.
 */
export async function getUdemDependants(dni: string): Promise<Omit<IDependant<Timestamp>, "core_id">[]> {
  if (!dni) {
    throw new Error("DNI is required")
  }

  const authConfig = process.env.UDEM_AFFILIATE_VALIDATION_AUTH
  if (!authConfig) {
    Logger.error("[getUdemDependants] -> Missing UDEM_AFFILIATE_VALIDATION_AUTH environment variable")
    throw new Error("Missing UDEM_AFFILIATE_VALIDATION_AUTH environment variable")
  }

  const { url, key } = JSON.parse(authConfig)
  const headers = {
    Accept: "application/json",
    Authorization: key,
  }

  try {
    const response: AxiosResponse<UdemAffiliate[] | UdemErrorResponse> = await axios.get<UdemAffiliate[]>(`${url}?dni=${dni}`, { headers })

    if (response.status === 200 && Array.isArray(response.data) && response.data.length > 0) {
      const dependants = response.data.filter((user) => user.TIPO === "ADHERENTE")
      const user = response.data.filter((user) => user.TIPO === "TITULAR")[0]

      const uma_dependants : Omit<IDependant<Timestamp>, "core_id">[] = dependants.map(dependant => {

        const dobParsed = parse(dependant.FECHA_NACIMIENTO, "dd-MMM-yy", new Date())
        const dobFormated = format(dobParsed, "yyyy-MM-dd")

        return {
          corporate_norm: "UDEM",
          country: "AR",
          dni: dependant.NRO_DOC,
          dob: dobFormated,
          dt: getDT(),
          email: dependant.EMAIL ?? user.EMAIL,
          firstname: dependant.APELLIDO_NOMBRES.split(" ")?.[0] || "",
          fullname: dependant.APELLIDO_NOMBRES,
          group: user.NRO_DOC || "",
          id: "",
          lastname: dependant.APELLIDO_NOMBRES.split(" ")?.[1] || "",
          nationality: "AR",
          sex: dependant.SEXO,
          timestamps: {
            dt_create: Timestamp.now()
          },
          ws: dependant.TELEFONO || "",

        }})

      if (dependants.length === 0) {
        Logger.log(`[getUdemDependants] -> No dependants found for DNI ${dni}`)
        return []
      }

      Logger.log(`[getUdemDependants] -> Found ${dependants.length} dependants for DNI ${dni}`)
      return uma_dependants
    }

    Logger.log(`[getUdemDependants] -> No dependants found for DNI ${dni}`)
    return []
  } catch (error) {
    Logger.error(`[getUdemDependants] -> Error fetching UDEM dependants for DNI ${dni}: ${error.message}`)
    throw new Error("Failed to fetch dependants data")
  }
}
