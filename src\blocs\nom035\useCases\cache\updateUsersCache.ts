import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getUsersFromAT } from "../users/getUsersFromAT"

export const updateUsersCache = async (redis: Redis, corporateId: string) => {
  Logger.log(`[Nom035 | updateUsersCache] Updating users for corporate: ${corporateId}`)

  const users = await getUsersFromAT(corporateId)
  const redisExpiration = 86400 // 24 hours

  await redis.set(`corporate:${corporateId}:users`, JSON.stringify(users), "EX", redisExpiration)
}