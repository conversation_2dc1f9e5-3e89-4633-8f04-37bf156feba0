import { EventTypes, IPatientLog, IProviderLog, IProviderErrorPrescriptionLog } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { calculateDuration } from "./timeCalculations"
import { IConnectionPeriod, IPrescriptionAttempt } from "../../appointments.entities"

export const connectionEvents = {
  connect: [
    "subscriberConnected",
    "sessionConnected",
    "publisherAccessAllowed",
    "sessionStreamCreated",
    "providerSessionStreamCreated",
    "providerPublisherAccessAllowed",
    "providerSessionConnectionCreated"
  ],
  disconnect: [
    "subscriberDestroyed",
    "sessionDisconnected",
    "sessionStreamDestroyed",
    "providerPublisherDestroyed",
    "providerPublisherStreamDestroyed",
    "providerSessionStreamDestroyed",
    "providerSubscriberDestroyed",
    "providerStreamDestroyed",
    "providerSessionConnectionDestroyed"
  ]
}

export const joinRoomEvents = [
  "joinRoomFromToast",
  "joinRoomFromQueue",
  "joinFromLink",
  "joinFromLinkWhatsapp",
  "joinFromLinkEmail",
  "joinFromLinkSms",
  "joinFromLinkNotification"
]

/**
 * Verifica si un evento es de tipo conexión
 */
export function isConnectionEvent(event: EventTypes): boolean {
  return connectionEvents.connect.includes(event)
}

/**
 * Verifica si un evento es de tipo desconexión
 */
export function isDisconnectionEvent(event: EventTypes): boolean {
  return connectionEvents.disconnect.includes(event)
}

/**
 * Verifica si un evento es de tipo joinRoom
 */
export function isJoinRoomEvent(event: EventTypes): boolean {
  return joinRoomEvents.includes(event as string)
}

/**
 * Genera un historial de conexiones basado en los logs proporcionados
 */
export function generateConnectionHistory(
  logs: IPatientLog<Timestamp>[] | IProviderLog<Timestamp>[]
): IConnectionPeriod[] {
  const history: IConnectionPeriod[] = []
  let currentConnection: { startTime: Date } | null = null

  const sortedLogs = [...logs].sort((a, b) =>
    a.timestamps.dt_create.toDate().getTime() - b.timestamps.dt_create.toDate().getTime()
  )

  sortedLogs.forEach(log => {
    const timestamp = log.timestamps.dt_create.toDate()

    if (isConnectionEvent(log.event)) {
      if (!currentConnection) {
        currentConnection = { startTime: timestamp }
      }
    } else if (isDisconnectionEvent(log.event) && currentConnection) {
      const duration = calculateDuration(currentConnection.startTime, timestamp)
      history.push({
        connected_at: currentConnection.startTime,
        disconnected_at: timestamp,
        duration: {
          minutes: duration.minutes,
          seconds: duration.seconds
        }
      })
      currentConnection = null
    }
  })

  if (currentConnection) {
    const duration = calculateDuration(currentConnection.startTime, new Date())
    history.push({
      connected_at: currentConnection.startTime,
      disconnected_at: null,
      duration: {
        minutes: duration.minutes,
        seconds: duration.seconds
      }
    })
  }

  return history
}

/**
 * Genera un historial de intentos de prescripción basado en los logs del proveedor
 */
export function generatePrescriptionHistory(
  logs: IProviderLog<Timestamp>[],
  providerUid?: string
): IPrescriptionAttempt[] {
  if (!providerUid || logs.length === 0) {
    return []
  }

  const prescriptionLogs = logs.filter(log =>
    log.event === "providerErrorPrescription" &&
    log.uid === providerUid
  ) as IProviderErrorPrescriptionLog<Timestamp>[]

  return prescriptionLogs.map(log => ({
    timestamp: log.timestamps.dt_create.toDate(),
    validator: log.validator || "",
    drugNames: log.drugNames || [],
    diagnosis: log.diagnosis || "",
    error_code: log.error_code || "",
    error_message: log.error_message || "",
    patient: {
      fullname: log.patient?.fullname || "",
      dni: log.patient?.dni || "",
      uid: log.patient?.uid || ""
    }
  }))
}

/**
 * Genera un historial de eventos de tipo joinRoom para el paciente
 */
export function generateJoinRoomHistory(
  logs: IPatientLog<Timestamp>[],
  patientUid?: string
): { event: EventTypes, timestamp: Date }[] {
  if (!patientUid || logs.length === 0) {
    return []
  }

  const joinRoomLogs = logs.filter(log =>
    isJoinRoomEvent(log.event) &&
    log.uid === patientUid
  )

  return joinRoomLogs.map(log => ({
    event: log.event,
    timestamp: log.timestamps.dt_create.toDate()
  }))
}
