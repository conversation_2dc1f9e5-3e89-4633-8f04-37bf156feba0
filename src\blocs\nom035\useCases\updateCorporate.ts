import { updateCorporateInAirtable } from "src/utils/airtable"
import { IUpdateCorporate } from "../interfaces"
import { sendUpdateCacheMessage } from "./cache/sendUpdateCacheMessage"

export const updateCorporate = async (corporateId: string, updateData: IUpdateCorporate): Promise<unknown> => {
  // Adapter a formato de airtable
  const corporateAirtableData = {
    "rfc": updateData.rfc,
    "domicilio": updateData.address,
    "rubro": updateData.category,
    "correo contacto empresa": updateData.contactEmail,
    "telefono contacto empresa": updateData.contactPhone,
    "nomina empleados": updateData.employees,
    "contacto empresa": updateData.contactFullname,
    "actividad principal": updateData.mainActivity
  }

  const company = await updateCorporateInAirtable("empresa", corporateId, corporateAirtableData)

  // Update corporate details cache
  await sendUpdateCacheMessage(corporateId, "corporate")

  return company
}
