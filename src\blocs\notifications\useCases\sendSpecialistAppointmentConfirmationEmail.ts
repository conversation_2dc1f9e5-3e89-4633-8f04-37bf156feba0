import { Logger } from "@nestjs/common"
import { format } from "date-fns"
import { dopplerRecipient } from "src/utils/messaging/channels/email"
import { SenderEnum } from "src/utils/messaging/enums/sender.enum"
import { TypeEnum } from "src/utils/messaging/enums/type.enum"
import { sendChaskiEmail } from "src/utils/messaging/messaging.helper"
import { chaskiTemplatesEnum, getChaskiTemplateId } from "src/utils/messaging/useCases/utils/getChaskiTemplateId"
import { parseCorporate } from "../utils/parseCorporate"
import { ISendDoctorAppointmentConfirmation } from "../notifications.bloc.interface"
import { getTimezoneByCountry } from "src/utils/getTimezoneByCountry"
import { toZonedTime } from "date-fns-tz"

export const sendDoctorAppointmentConfirmationUseCase = async ({
  email,
  fullname,
  corporate,
  country,
  modelData: {
    doctorName,
    date,
    modality,
    motivosDeConsulta,
    patientName,
    specialty,
    cmData,
  },
}: ISendDoctorAppointmentConfirmation) => {
  const parsedCorporate = parseCorporate(corporate)
  const template = getChaskiTemplateId(chaskiTemplatesEnum.DOCTOR_APPOINTMENT_CONFIRMATION)
  const sender = SenderEnum[parsedCorporate]
  const timezone = getTimezoneByCountry(country)
  const localDate = toZonedTime(date, timezone)
  const attentionDate = format(localDate, "dd/MM/yyyy")
  const attentionTime = format(localDate, "HH:mm")

  const recipients: dopplerRecipient[] = [{
    email,
    name: fullname,
    type: "to"
  }]

  const model = {
    subject: `¡Tiene un nuevo turno! | El día ${attentionDate} a las ${attentionTime} hs - ÜMA`,
    content: `Su paciente ${patientName} confirmó un nuevo turno.`,
    doctorName,
    patientName,
    specialty,
    modality,
    date: `Día ${attentionDate} a las ${attentionTime} hs`,
    observations: motivosDeConsulta,
    address: `${cmData?.address || "N/A"} ${cmData?.state || ""}`,
    name: cmData?.name || "N/A"
  }

  try {
    return await sendChaskiEmail({
      model,
      recipients,
      sender,
      templateChaskiId: Number(template),
      type: TypeEnum.NA,
      country,
    })
  } catch(err) {
    Logger.error(`[ ${sendDoctorAppointmentConfirmationUseCase.name} ] => Could not send email to ${email} - err: ${JSON.stringify(err)}`)
  }
}