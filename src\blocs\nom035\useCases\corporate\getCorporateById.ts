import { base } from "src/utils/airtable/airtableConfiguration"

export const getCorporateById = async (corporateId: string) => {
  const table = base("empresa")
  const corporateRecord = await table.select({
    fields: [
      "id", "cuestionario obligatorio", "rfc", "domicilio", "telefono contacto empresa", "correo contacto empresa",
      "contacto empresa", "rubro", "actividad principal", "nomina empleados", "empresa", "muestra representativa",
    ],
    filterByFormula: `{empresa}="${corporateId}"`,
    maxRecords: 1
  }).firstPage()

  return corporateRecord[0].fields
}
