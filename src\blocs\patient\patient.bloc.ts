import { Injectable } from "@nestjs/common"
import { appointmentServices, callToken, countries, dependantUid, TSpecialties, IPatient, chat, CheckOneField, IHealthInsurance, IRequest, EventTypes } from "@umahealth/entities"
import { FindPatientUseCase, UpdatePatientUseCase, UpdateDependantUseCase, endCall, setActive, setInactive, startCall, saveDniImage, saveDependantDniImage, writeLogUseCase, sendVerifiedPhone, setPhoneVerification, validateDniUseCase, deleteTestUserUseCase, updateOldActiveServicesUseCase, updateNewActiveServicesUseCase } from "./useCases"
import { IDocumentList } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { GetPatientByPersonalDataUseCase } from "./useCases/patient/getPatientByPersonalDataUseCase"
import { ValidateDependantProfileUseCase } from "./useCases/dependant/validateDependantProfileUseCase"
import { ValidateProfileUseCase } from "./useCases/patient/ValidateProfileUseCase"
import { startChat } from "./useCases/patient/startChat"
import { endChat } from "./useCases/patient/endChat"
import { createChat } from "./useCases/patient/createChat"
import { getChatAtt } from "./useCases/patient/getChatAtt"
import { updateChatAtt } from "./useCases/patient/updateChatAtt"
import { IPortalLocals } from "src/utils/middleware/portalAuthorizedCorporatesMiddleware"
import { applyPFAPatientUpdates } from "./useCases/revalidatePFAPatient"
import { getDependantByDniUseCase } from "./useCases/dependant/getDependantByDniUseCase"
import { RegisterPatientUseCase } from "./useCases/patient/registerPatient"
import { checkFieldUseCase } from "./useCases/patient/checkFieldUseCase"
import { IValidateDniProps } from "src/patient-app/patient/patient.entities"
import { IUpdateNewActiveServices, IUpdateOldActiveServices } from "./patient.bloc.interface"
import { getPatientsWithAppointsAttemptsUseCase } from "./useCases/patient/getPatientsWithAppointsAttemptsUseCase"
import { getLogsUseCase } from "./useCases/patient/getLogsUseCase"
import { findPatientOrDependantUseCase } from "./useCases/find"

@Injectable()
export class PatientBloc {

  async startCall(assignation_id: string, call: callToken, country: countries, dependantUid: dependantUid, service: appointmentServices, specialty: false | TSpecialties, uid: string) {
    return await startCall(assignation_id, call, country, dependantUid, service, specialty, uid)
  }

  async endCall(uid: string) {
    return await endCall(uid)
  }

  async setActiveAppointmentForPatient(assignationId: string, call: callToken, date: Date, uid: string) {
    return await setActive(assignationId, call, date, uid)
  }

  async setInactiveAppointmentForPatient(uid: string) {
    return await setInactive(uid)
  }

  async find(uid: string): Promise<IPatient<Timestamp>> {
    return await FindPatientUseCase(uid)
  }

  async updatePatient(uid: string, data: Partial<IPatient<Timestamp>>, locals?: IPortalLocals): Promise<IDocumentList<Partial<IPatient<Timestamp>>>> {
    return await UpdatePatientUseCase(uid, data, locals)
  }

  async updateDependant(uid: string, owner_uid: string, data: Partial<IPatient<Timestamp>>, locals?: IPortalLocals): Promise<IDocumentList<Partial<IPatient<Timestamp>>>[]> {
    return await UpdateDependantUseCase(uid, owner_uid, data, locals)
  }

  async GetPatientByPersonalData(dataType: "dni" | "ws" | "email", value: string) {
    return await GetPatientByPersonalDataUseCase(dataType, value)
  }

  async ValidateDependantProfile(uid: string) {
    return await ValidateDependantProfileUseCase(uid)
  }

  async ValidateProfile(uid: string) {
    return await ValidateProfileUseCase(uid)
  }

  async saveDniImage(uid: string, country: countries, filename: string, documentSide: "front" | "back") {
    return await saveDniImage(uid, country, filename, documentSide)
  }

  async saveDependantDniImage(uid: string, dependantUid: string, country: countries, filename: string, documentSide: "front" | "back") {
    return await saveDependantDniImage(uid, dependantUid, country, filename, documentSide)
  }

  async createChatAtt(uid: string) {
    return await createChat(uid)
  }

  async startChatAtt(assignation_id: string, country: countries, dependantUid: dependantUid, service: appointmentServices, specialty: false | TSpecialties, uid: string) {
    return await startChat(assignation_id, country, dependantUid, service, specialty, uid)
  }

  async endChatAtt(uid: string) {
    return await endChat(uid)
  }

  async getChatAtt(uid: string) {
    return await getChatAtt(uid)
  }

  async updateChatAtt(uid: string, chat: chat<Timestamp>) {
    return await updateChatAtt(uid, chat)
  }

  async writeLog(uid: string, event: EventTypes, assignation_id: string, uid_dependant: dependantUid) {
    return await writeLogUseCase(uid, event, assignation_id, uid_dependant)
  }

  async sendPhoneVerification(uid: string, ws: string) {
    return await sendVerifiedPhone(uid, ws)
  }

  async revalidatePFAPatientUseCase(pfCoverages: IHealthInsurance<Timestamp>, patient: IPatient, isDependant: boolean){
    return await applyPFAPatientUpdates(pfCoverages, patient, isDependant)
  }

  /**
   * Permite desactivar o activar la validación del telefono del usuario, por defecto si no se manda verify,
   *  se entiende que estás queriendo decir que el usuario valido el telefono.
   * @param uid
   * @param verify Si estas activando o no la validación
   */
  async setPhoneVerification(uid: string, verified: boolean) {
    return await setPhoneVerification(uid, verified)
  }

  async getDependantsByDni(dni: string) {
    return await getDependantByDniUseCase(dni)
  }

  /**
   * Completa los datos de un paciente durante el flujo de registro
   * @param uid uid del paciente a completar los datos del registro
   * @param data data a completar
   * @returns IDocumentList<Partial<IPatient>>
   */
  async registerPatient(uid: string, registerPatientData: Partial<IPatient<Timestamp>>) {
    return await RegisterPatientUseCase(uid, registerPatientData)
  }

  /**
   * Funcion para checkear si existe otro usuario (distinto al uid que le pasamos)
   * con un determinado valor en un determinado campo
   * @param uid uid del usuario que queremos obviar
   * @param field campo que queremos checkear
   * @param value valor que queremos checkear
   * @returns true si encontró un usuario con uid distinta a la recibida por params, de lo contrario false
   */
  async checkField(uid: string, data: CheckOneField) {
    return await checkFieldUseCase(uid, data)
  }

  async validateDni(data: IValidateDniProps) {
    return await validateDniUseCase(data)
  }

  /** metodo para eliminar los usuarios registrados en pruebas de cypress */
  async deleteTestUser(uid: string) {
    return await deleteTestUserUseCase(uid)
  }

  async updateOldActiveServices(data: IUpdateOldActiveServices, request: IRequest<Timestamp>, assignationPath: string, type: string){
    return await updateOldActiveServicesUseCase(data, request, assignationPath, type)
  }

  async updateNewActiveServices(data: IUpdateNewActiveServices, type: string){
    return await updateNewActiveServicesUseCase(data, type)
  }
  /** Obtiene los usuarios que han intentado tener una consulta en el periodo especificado */
  async getPatientsWithAppointsAttempts(dt_from: Date, dt_to: Date) {
    return await getPatientsWithAppointsAttemptsUseCase(dt_from, dt_to)
  }

  async getLogs(uid: string, assignationId: string, event?: EventTypes) {
    return await getLogsUseCase(uid, assignationId, event)
  }

  async findPatientOrDependant(patientUid: string) {
    return await findPatientOrDependantUseCase(patientUid)
  }
}
