import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { appointmentServices, countries, IAppointment, IFreeAppointmentsFilters, TSpecialties } from "@umahealth/entities"
import { AppointmentRepository } from "@umahealth/repositories"


export const findFreeAppointmentsUseCase = async (country: countries, cuit: string, queryDate: string, service: appointmentServices, specialty: TSpecialties, uid: string): Promise<IAppointment<Timestamp>[]> => {
  try {
    const filters: IFreeAppointmentsFilters[] = [
      {field: "state", value: "FREE", comparator: "=="},
      {field: "date", value: queryDate, comparator: "=="},
    ]

    if(uid){
      filters.push(
        {field: "uid", value: uid, comparator: "=="},
      )
    }

    if(cuit){
      filters.push(
        {field: "cuit", value: cuit, comparator: "=="},
      )
    }

    if(specialty){
      filters.push(
        {field: "especialidad", value: specialty, comparator: "=="},
      )
    }
    const appointments = await AppointmentRepository.getFreeAppointmentsByFilters(service, country, filters)
    return appointments
  } catch (err) {
    throw new InternalServerErrorException(`[ Appointments | findFreeAppointments ] => ${err.message}`)
  }
}
