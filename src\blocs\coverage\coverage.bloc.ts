import { Timestamp } from "@google-cloud/firestore"
import { Injectable } from "@nestjs/common"
import { IPatientIoma } from "./apiValidations/interfaces"
import { IHealthInsurance, IPatient, IValidateHealthInsurance, PrescriptionsPatient, dependantUid } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/repositories"
import { AddCoverageUseCase, DeleteCoverageUseCase, DependantAddCoverageUseCase, DependantDeleteCoverageUseCase, DependantGetPrimaryUseCase, DependantParamsValidationUseCase, DependantSetPrimaryUseCase, DependantUpdateCoverageAfterValidationUseCase, DependantUpdateCoverageUseCase, DependantValidateCoverageByApiUseCase, DependantValidateCoverageByExternalPgUseCase, DependantValidateCoverageByPgUseCase, GetDependantsFromCoverageUseCase, GetPrimaryUseCase, ParamsValidationUseCase, SetPrimaryUseCase, UpdateCoverageAfterValidationUseCase, UpdateCoverageUseCase, ValidateCoverageByApiUseCase, ValidateCoverageByExternalPgUseCase, ValidateCoverageByPgUseCase } from "./useCases"
import { IPfaDependant } from "src/patient-app/dependant/dependant.entities"
import { IValidateCoverageByApi, IValidation } from "./coverage.bloc.interfaces"
import { searchCorporateDocumentUseCase } from "./useCases/searchCorporateDocument"


@Injectable()
export class CoverageBloc {
  async validateCoverageByApi(corporate: IHealthInsurance<Timestamp>, uid: string): Promise<IValidateCoverageByApi> {
    return await ValidateCoverageByApiUseCase(corporate, uid)
  }
  async validateCoverageByPg(validatedBy: string, healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> {
    return await ValidateCoverageByPgUseCase(validatedBy, healthInsurance)
  }
  async validateCoverageByExternalPg(healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> {
    return await ValidateCoverageByExternalPgUseCase(healthInsurance)
  }
  async addCoverage(uid: string, coverage: IHealthInsurance<Timestamp>): Promise<IDocumentList<IHealthInsurance<Timestamp>>> {
    return await AddCoverageUseCase(uid, coverage)
  }
  async deleteCoverage(uid: string, coverage: string): Promise<IDocumentList<IHealthInsurance<Timestamp>>> {
    return await DeleteCoverageUseCase(uid, coverage)
  }
  async updateCoverage(uid: string, coverageId: string, data: IHealthInsurance<Timestamp>): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> {
    return await UpdateCoverageUseCase(uid, coverageId, data)
  }
  async updateCoverageAfterValidation(uid: string, coverage: IHealthInsurance<Timestamp>, status: IValidation): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> {
    return await UpdateCoverageAfterValidationUseCase(uid, coverage, status)
  }
  async paramsValidation(uid: string): Promise<IValidateHealthInsurance> {
    return await ParamsValidationUseCase(uid)
  }
  async setPrimary(uid: string, coverageId: string): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>> | Partial<IPatient<Timestamp>>>[]> {
    return await SetPrimaryUseCase(uid, coverageId)
  }
  async getPrimary(uid: string): Promise<IHealthInsurance<Timestamp>> {
    return await GetPrimaryUseCase(uid)
  }

  async dependantValidateCoverageByApi(corporate: IHealthInsurance<Timestamp>, uid: string): Promise<IValidateCoverageByApi> {
    return await DependantValidateCoverageByApiUseCase(corporate, uid)
  }
  async dependantValidateCoverageByPg(validatedBy: string, healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> {
    return await DependantValidateCoverageByPgUseCase(validatedBy, healthInsurance)
  }
  async dependantValidateCoverageByExternalPg(healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> {
    return await DependantValidateCoverageByExternalPgUseCase(healthInsurance)
  }
  async dependantAddCoverage(uid: string, coverage: IHealthInsurance<Timestamp>): Promise<IDocumentList<IHealthInsurance<Timestamp>>> {
    return await DependantAddCoverageUseCase(uid, coverage)
  }
  async dependantDeleteCoverage(uid: string, coverage: string): Promise<IDocumentList<IHealthInsurance<Timestamp>>> {
    return await DependantDeleteCoverageUseCase(uid, coverage)
  }
  async dependantUpdateCoverage(uid: string, coverageId: string, data: IHealthInsurance<Timestamp>): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> {
    return await DependantUpdateCoverageUseCase(uid, coverageId, data)
  }
  async dependantUpdateCoverageAfterValidation(uid: string, coverage: IHealthInsurance<Timestamp>, status: IValidation): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> {
    return await DependantUpdateCoverageAfterValidationUseCase(uid, coverage, status)
  }
  async dependantParamsValidation(uid: string): Promise<IValidateHealthInsurance> {
    return await DependantParamsValidationUseCase(uid)
  }
  async dependantSetPrimary(uid: string, coverageId: string): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>> | Partial<IPatient<Timestamp>>>[]> {
    return await DependantSetPrimaryUseCase(uid, coverageId )
  }
  async dependantGetPrimary(dependantUid: dependantUid) {
    return await DependantGetPrimaryUseCase(dependantUid)
  }
  async getDependantsFromCoverage(corporate: IHealthInsurance<Timestamp>, uid: string, dependantUid?: string): Promise<Partial<IPatientIoma | IPfaDependant[]>> {
    return await GetDependantsFromCoverageUseCase(corporate, uid, dependantUid)
  }

  async searchCorporateDocument(patient: PrescriptionsPatient, country: string): Promise<IHealthInsurance<Timestamp> | null> {
    return await searchCorporateDocumentUseCase(patient, country)
  }
}
