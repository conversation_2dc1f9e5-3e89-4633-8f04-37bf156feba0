import { HealthInsuranceRepository, IDocument<PERSON>ist, PatientRepository } from "@umahealth/repositories"
import { IPatient } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { BadRequestException, ConflictException, Logger, NotFoundException, UnauthorizedException } from "@nestjs/common"
import { IPortalLocals } from "src/utils/middleware/portalAuthorizedCorporatesMiddleware"
import { dniErrorMsg, wsErrorMsg } from "../../utils/errorsMsg"
import { cleanupName } from "src/utils/patient/parseNames"
import { isNumericEmail } from "../../utils/emailValidation"

/**
 * Actualiza los datos de un paciente
 * @param uid uid del patient a updatear
 * @param data data a updatear
 * @param locals objeto enviado a traves del middleware "PortalAuthorizedCorporatesMiddleware" con los corporates sobre los que tiene permisos el usuario de portal
 * @returns IDocumentList<Partial<IPatient<Timestamp>>>
 */

export const UpdatePatientUseCase = async (uid: string, data: Partial<IPatient<Timestamp>>, locals?: IPortalLocals): Promise<IDocumentList<Partial<IPatient<Timestamp>>>> => {
  const patient = await PatientRepository.getByUid(uid)
  if (!patient) throw new NotFoundException(`[ Patient | updatePatient ] Patient not found - UID ${uid}`)
  let sameCorpo = false
  let document: IDocumentList<Partial<IPatient<Timestamp>>> = null

  // Sacamos los espacios extra que pueda haber en el nombre dado
  if (data.fullname) {
    data.fullname = cleanupName(data.fullname)
  } else if (patient.fullname) {
    data.fullname = cleanupName(patient.fullname)
  }
  // Sacamos los espacios extra que pueda haber en el nombre escogido
  if (data.chosenName) {
    data.chosenName = cleanupName(data.chosenName)
  } else if (patient.chosenName) {
    data.chosenName = cleanupName(patient.chosenName)
  }

  // Si se llama a la funcion desde el portal, se ejecuta el middleware y por lo tanto se recibe el parametro "locals"
  if (locals) {
    if (!locals.corporatesIdsAndNames) throw new BadRequestException("[updatePatientUseCase] => res.locals is empty")

    if (locals.corporatesIds.includes("UMA") || locals.superadmin) sameCorpo = true
    else {
      const activeCorporate = await HealthInsuranceRepository.getPrimary(uid)
      if (!activeCorporate) throw new BadRequestException(`[updatePatientUseCase] => active corporate has not been found | uid ${uid}`)
      locals.corporatesIdsAndNames.forEach(corpo => {
        if (corpo.id === activeCorporate.id || corpo.value === activeCorporate.id) sameCorpo = true
      })
    }

    if (sameCorpo) document = await PatientRepository.update(uid, { ...patient, ...data })
    else throw new UnauthorizedException(`[updatePatientUseCase] => patient ${uid} does not belong to your corporate`)
  } else {
    // checkeamos que el numero de telefono y el dni que esta introduciendo el usuario en la pagina de su perfil,
    // no coincidan con un usuario ya existente en la base de datos
    let wsConflictMsg = ""
    let dniConflictMsg = ""
    let existingUser: IPatient<Timestamp>[] = []

    if (data.ws) {
      existingUser = await PatientRepository.getPatientByWs(data.ws)
      if (existingUser.length && existingUser[0].id !== patient.id) wsConflictMsg = wsErrorMsg
    }

    // Solo validamos DNI si no es un email numérico
    if (data.dni && !isNumericEmail(data.email || patient.email)) {
      existingUser = await PatientRepository.getPatientByDni(data.dni)
      if (existingUser.length && existingUser[0].id !== patient.id) dniConflictMsg = dniErrorMsg
    }

    if (existingUser.length && (wsConflictMsg || dniConflictMsg)) {
      Logger.error(`${wsConflictMsg} - ${dniConflictMsg}, {dni: ${data.dni}, ws: ${data.ws}, originalId: ${uid}, foundId: ${existingUser[0].id}}`)
      Logger.error(`originalId: ${uid}, foundId: ${existingUser[0].id}`)
    }

    document = await PatientRepository.update(uid, { ...patient, ...data })
  }

  return document
}
