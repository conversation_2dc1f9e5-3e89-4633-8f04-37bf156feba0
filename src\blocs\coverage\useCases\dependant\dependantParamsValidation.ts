import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IValidateHealthInsurance } from "@umahealth/entities"
import { ICoverageIncomplete } from "../../coverage.bloc.interfaces"
import { InternalServerErrorException } from "@nestjs/common"

export const DependantParamsValidationUseCase =  async (uid: string ): Promise<IValidateHealthInsurance> => {
  const incompleteHealthInsurance: IValidateHealthInsurance = {
    coverages: []
  }
  const coverages = await HealthInsuranceRepository.getAllByUid(uid)

  coverages.map(async (item) => {
    const coverageIncomplete: ICoverageIncomplete = {
      id: item.id,
      missingFields: []
    }

    if(item?.affiliate_id === undefined || item?.affiliate_id === "" || item?.affiliate_id === null){
      coverageIncomplete.missingFields.push("affiliate_id")
    }

    if(item.userInput === ""){
      try{
        await HealthInsuranceRepository.dependantUpdateDocument(uid, item.id, {...item, userInput: item.id})
      }catch(err){
        throw new InternalServerErrorException(`[ Coverages | patient | paramsValidation ] => Failed to update dependant userInput value - UID ${uid}`)
      }
    }

    if(coverageIncomplete.missingFields.length !== 0){
      incompleteHealthInsurance.coverages.push( coverageIncomplete )
    }
  })


  return incompleteHealthInsurance
}
