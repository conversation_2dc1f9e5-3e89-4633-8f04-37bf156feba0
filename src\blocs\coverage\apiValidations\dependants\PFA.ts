import { InternalServerErrorException, Logger } from "@nestjs/common"
import axios from "axios"
import { IPfaDependant } from "src/patient-app/dependant/dependant.entities"
import { pfa_get_dependants, pfa_validate_affiliate } from "src/utils/pfa/endpoints"
import { getPfaJWT } from "src/utils/pfa/getPfaJWT"
import * as https from "https"
import { dniParserPfa } from "../../utils/functions"

interface IPfaValidation {
  result: boolean
  code: string
}

const axiosInstance = axios.create({
  httpsAgent: new https.Agent({
    rejectUnauthorized: false
  })
})


export const PostPFAWithCargasByMember = async (affiliate_number: string): Promise<IPfaDependant[]> => {
  if(!affiliate_number){
    throw new InternalServerErrorException("[ PostPFAWithCargasByMember] => affiliate_number not found")
  }

  const authJwt = await getPfaJWT()
  const patientGroupResponse = await axiosInstance.post(pfa_get_dependants, { group: affiliate_number }, { headers: { "Content-Type":"application/json", "Authorization": authJwt } }).catch(error => {
    Logger.error(`Error getting dependants data from PFA with member: ${affiliate_number} error: ${error}`)
    throw new InternalServerErrorException(`Error getting dependants data from PFA with dni: ${affiliate_number} - error: ${error}`)
  })
  return patientGroupResponse.data
}

export const ValidatePFADependant = async (dni: string, affiliate_number: string): Promise<IPfaValidation> => {
  const authJwt = await getPfaJWT()
  const validationResult = await axiosInstance.post(pfa_validate_affiliate, { document: dniParserPfa(dni), member: affiliate_number }, { headers: { "Content-Type":"application/json", "Authorization": authJwt } }).catch(error => {
    Logger.error(`Error validating dependant data from PFA with dni: ${dni} and affiliate_number: ${affiliate_number} error: ${error}`)
    throw new InternalServerErrorException(`Error validating dependant data from PFA with dni: ${dni} and affiliate_number: ${affiliate_number} - error: ${error}`)
  })
  return validationResult.data
}