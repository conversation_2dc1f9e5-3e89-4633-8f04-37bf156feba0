import { sendSMS } from "src/utils/messaging/messaging.helper"

/**
 * Envia un SMS al usuario con los primeros dígitos del uid como codigo de verificación
 * @param uid uid del usuario, usaremos los primeros 4 carácteres para el código de verificación
 * @param ws número de telefono al que se envia el mensaje, debería estar en un formato del estilo 5492975077951, sin el +
 */
export async function sendVerifiedPhone (uid: string, ws: string){
  await sendSMS(ws, `${uid.slice(0,4)} es tu codigo de verificacion para UMA SALUD`, "verification_phone_code")
}
