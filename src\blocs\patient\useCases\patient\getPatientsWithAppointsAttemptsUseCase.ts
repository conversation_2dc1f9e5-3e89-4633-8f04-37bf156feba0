import { getAllOnlineRequestByDate } from "src/blocs/requests/useCases"
import { IUniqueUsers, getUniqueUsers } from "../../utils/getUniqueUsers"

export const getPatientsWithAppointsAttemptsUseCase = async (dt_from: Date, dt_to: Date = new Date()): Promise<IUniqueUsers[]> => {
  const requestsAR = await getAllOnlineRequestByDate("AR", dt_from, dt_to)
  const requestsMX = await getAllOnlineRequestByDate("MX", dt_from, dt_to)

  const uniqueARUsers = getUniqueUsers(requestsAR)
  const uniqueMXUsers = getUniqueUsers(requestsMX)

  return [
    ...uniqueARUsers,
    ...uniqueMXUsers,
  ]
}