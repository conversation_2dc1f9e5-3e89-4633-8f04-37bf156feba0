import { HomeParameters, HomeParametersObject, countries } from "@umahealth/entities"
import { ParametersRepository } from "@umahealth/repositories"

export async function getHomeParameters(country: countries, corporate: string) {
  let parameters: HomeParameters[] = null
  const paramsByCorporate = await ParametersRepository.getParametersByCorporate(corporate)
  if (!paramsByCorporate || paramsByCorporate.length === 0) {

    parameters = await ParametersRepository.getDefaultParameters(country)
  } else {
    parameters = paramsByCorporate
  }

  // Using spread syntax to create a copy and sort without mutating the original array
  const orderedParameters = [...parameters].sort((a, b) => a.order - b.order)
  return orderedParameters.reduce(reduceParameters, {} as HomeParametersObject)
}


/** reduce que transforma un array con ids en objetos con ese id y contenido
 * @example
 * [
 *   { id: 'header', name: 'headerHomepage'},
 *   { id: 'guardia', name: 'nombre'},
 *   { id: 'test', name: 'algo'}
 * ]
 * // luego del reduce
 * const result = {
 *   header: { name : 'headerHomepage'},
 *   guardia: { name: 'nombre' },
 *   test: { name: 'algo'}
 * }
 * // Notese como el objeto pierde la propiedad id y pasa a ser parte de su nombre
 */
function reduceParameters(prev : HomeParametersObject, parameter : HomeParameters) {
  const {id, ...parameterWithoutId} = parameter
  const newParameter = {[id] : parameterWithoutId}
  return {...prev, ...newParameter}
}
