import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { appointmentServices, countries, IAppointment, IOnlineAppointment, action, IOnSiteAppointment } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { AppointmentRepository, RequestRepository } from "@umahealth/repositories"

export async function cancelSpecialistAppointmentPatient(
  service: appointmentServices,
  country: countries,
  assignationId: string,
  action?: action,
  cancelMotive?: string
): Promise<IDocumentList<Partial<IAppointment<Timestamp>>>> {
  let request
  let adminActions
  let appointment
  const cancelTime = new Date()

  if (service === "consultorio") {
    request = await RequestRepository.getOnsite(assignationId)
    if (request["adminActions"]) {
      adminActions = [...request["adminActions"], action]
    } else {
      adminActions = [action]
    }
    appointment = await AppointmentRepository.getByAssignationId<IOnSiteAppointment<Timestamp>>(service, country, assignationId)
    if (!appointment) {
      throw new NotFoundException(`Appointment not found. assignationId: ${assignationId} service: ${service} country: ${country}.`)
    }
    appointment.adminActions = adminActions
    if(cancelMotive) appointment.cancelMotive = cancelMotive
    appointment.state = "FREE"
    appointment.destino_final = "USER CANCEL"
  }
  else {
    appointment = await AppointmentRepository.getByAssignationId<IOnlineAppointment<Timestamp>>(service, country, assignationId)
    if (!appointment) {
      throw new NotFoundException(`Appointment not found. assignationId: ${assignationId} service: ${service} country: ${country}.`)
    }
    appointment.state = "FREE"
    appointment.destino_final = "USER CANCEL"
    appointment.cancel_origin = "patient"
  }

  appointment.patient = {}
  appointment.timestamps.dt_cancel = Timestamp.fromDate(cancelTime)

  return await AppointmentRepository.update<IOnSiteAppointment<Timestamp> | IOnlineAppointment<Timestamp>>(service, country, assignationId, appointment)

}
