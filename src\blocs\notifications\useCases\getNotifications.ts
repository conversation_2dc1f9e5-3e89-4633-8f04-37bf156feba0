import { HealthInsuranceRepository } from "@umahealth/repositories"
import { authRequest } from "src/utils/authRequest"

export async function getNotifications(uid: string, count = "false", status = "all") {
  const coverages = await HealthInsuranceRepository.getAllByUid(uid)
  const nameCoverages = coverages.map((coverage) => coverage.id).join(",")

  return await authRequest({
    method: "GET",
    audience: process.env.UMA_NOTIFICATIONS_URL,
    url: `${process.env.UMA_NOTIFICATIONS_URL}?uid=${uid}&status=${status}&count=${count}&coverages=${nameCoverages}`,
    payload: "",
  })
}
