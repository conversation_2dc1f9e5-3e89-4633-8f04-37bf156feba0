import {  DependantRepository, PatientRepository } from "@umahealth/repositories"
import { IHealthInsurance, IPatient } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common"
import { IPatientExternal } from "../../coverage.bloc.interfaces"
import { PostIomaPatientWithCargas } from "../../apiValidations/dependants/IOMA"
import { iomaErrorMessages, sexConvertion } from "../../utils/functions"
import { PostPFAWithCargasByMember } from "../../apiValidations/dependants/PFA"


export const GetDependantsFromCoverageUseCase = async (corporate: IHealthInsurance<Timestamp>, uid: string, dependantUid?: string): Promise<Partial<IPatientExternal>> => {
  try{
    let patient: IPatient<Timestamp>
    if (dependantUid) {
      patient = await DependantRepository.getByUid(uid, dependantUid)
    } else {
      patient = await PatientRepository.getByUid(uid)
    }
    if(!patient) throw new NotFoundException(`[ Coverages | dependant | getDependantsFromCoverage ] User not found with uid: ${dependantUid || uid}`)
    let response: Partial<IPatientExternal>

    switch(corporate.id){
    case "IOMA-APP": {
      const iomaPatient = await PostIomaPatientWithCargas(JSON.parse(process.env.IOMA_AUTH).Username, JSON.parse(process.env.IOMA_AUTH).Password, patient.dni, sexConvertion(patient.sex))
      if(iomaPatient.CodigoMensaje in iomaErrorMessages){
        Logger.error(`Error obteniendo información de IOMA para uid: ${dependantUid || uid} - msg: ${iomaErrorMessages[iomaPatient.CodigoMensaje]}`)
        throw new NotFoundException(`[ Coverages | dependant | getDependantsFromCoverage ] ${iomaErrorMessages[parseInt(iomaPatient.CodigoMensaje)]}`)
      }
      response = iomaPatient
      break
    }

    case "POLICIA FEDERAL": {
      const pfaPatient = await PostPFAWithCargasByMember(corporate.affiliate_id)
      response = pfaPatient
      break
    }
    default:{
      break
    }
    }

    return response
  } catch (err) {
    if(err instanceof NotFoundException){
      err.message = `[ Coverages | dependant | getDependantsFromCoverage ] => ${err.message}`
      throw err
    }
    throw new InternalServerErrorException(`[ Coverages | dependant | getDependantsFromCoverage ] => ${err.message}`)
  }
}
