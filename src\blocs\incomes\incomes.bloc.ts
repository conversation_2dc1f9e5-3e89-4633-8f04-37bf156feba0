import { Timestamp } from "@google-cloud/firestore"
import { Injectable } from "@nestjs/common"
import { IProviderIncomePrices, ProviderIncome, countries } from "@umahealth/entities"
import { createIncome, updateIncome, getActiveIncome, adjustDateForTimeZone, createTurboDocument} from "./useCases"

@Injectable()
export class IncomesBloc {
  async createIncome(country: countries, data: ProviderIncome<Timestamp>, type:string){
    return await createIncome(country, data, type)
  }

  async updateIncome(country: countries, data: ProviderIncome<Timestamp>, incomeId:string, type:string){
    return await updateIncome(country, data, incomeId, type)
  }

  async getActiveIncome(country: countries, specialty: string, type:string){
    return await getActiveIncome(country, specialty, type)
  }

  async adjustDateForTimeZone(date: Date){
    return adjustDateForTimeZone(date)
  }

  async createTurboDocument(incomeData: IProviderIncomePrices, startTurbo: Date, endTurbo: Date){
    return await createTurboDocument(incomeData, startTurbo, endTurbo)
  }
}
