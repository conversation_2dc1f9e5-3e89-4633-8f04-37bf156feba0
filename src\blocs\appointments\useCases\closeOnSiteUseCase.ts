import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { countries, IAppointment, IOnSiteAppointment } from "@umahealth/entities"
import { AppointmentRepository, IDocumentList } from "@umahealth/repositories"

export const closeOnSiteUseCase = async (
  assignationId: string,
  country: countries,
  appointment: Partial<IAppointment<Timestamp>>
): Promise<IDocumentList<Partial<IAppointment<Timestamp>>>> => {
  const fullAppointment = await AppointmentRepository.getByAssignationId<IOnSiteAppointment<Timestamp>>("consultorio", country, assignationId)

  appointment.state = "DONE"
  appointment.timestamps = {
    ...fullAppointment.timestamps,
    dt_close: Timestamp.now()
  }

  const closedOnSiteAppointment = await AppointmentRepository.update(
    "consultorio",
    country,
    assignationId,
    appointment
  )

  if (!closedOnSiteAppointment)
    throw new InternalServerErrorException(
      `[ Appointments | closeOnSiteUseCase ] => Error closing onsite attention appointment. Id: ${assignationId}`
    )

  return closedOnSiteAppointment
}
