import { Timestamp } from "@google-cloud/firestore"
import { IHealthInsurance, IPatient } from "@umahealth/entities"
import { DependantRepository, HealthInsuranceRepository, PatientRepository, saveBatch } from "@umahealth/repositories"
import { Logger } from "@nestjs/common"
import { IValidations } from "src/patient-app/coverage/coverage.entities"
import { dniParserPfa, sexConvertionPfa } from "src/blocs/coverage/utils/functions"
import { GetPfaPatientByAfiNum, GetPfaPatientByDocument } from "src/blocs/coverage/apiValidations/patients/PFA"
import * as moment from "moment"

const dniRegex = /^[\d]{1,3}\.?[\d]{3,3}\.?[\d]{3,3}$/
const sexStringRegex = /^[MFX]$/
export const updateDniAndSex = async(coverageData: IHealthInsurance<Timestamp>,patient: IPatient<Timestamp>, isDependant: boolean) => {
  Logger.log(`[PFA - updateDniAndSex] Updating patient based on DNI: ${patient.dni} and sex: ${patient.sex} - uid ${patient.id}`)
  try {
    const pfaApiData = await GetPfaPatientByAfiNum(coverageData.affiliate_id)

    const { dni, sex, dob } = patient
    const realDni = pfaApiData[0].credential.replace(/\./g, "") || dni
    const realSex = sexConvertionPfa(pfaApiData[0].sex) || sex
    const realDob = pfaApiData[0].birth || dob

    if (dni === realDni && sex === realSex) {
      return // No es necesario actualizar
    }

    if (isDependant) {
      const updateDependant = await DependantRepository.doublePathUpdate(patient.core_id, patient.id, { sex: realSex, dni: realDni, dob: realDob })
      await saveBatch(updateDependant)
    } else {
      await PatientRepository.updateDocument(patient.id, { sex: realSex, dni: realDni, dob: realDob })
    }
  } catch (error) {
    Logger.error(`[PFA - updateAffiliateNumber] Error updating patient: ${error}`)
  }
}



export const updateAffiliateNumber = async(coverageData: IHealthInsurance<Timestamp>, patient: IPatient<Timestamp>, isDependant: boolean) => {
  try {
    Logger.log(`[PFA - updateDniAndSex] Updating patient based on affiliate number: ${coverageData.affiliate_id}`)
    const { dni, sex, dob } = patient
    const pfaApiData = await GetPfaPatientByDocument(dniParserPfa(dni))
    const affiliate_id = pfaApiData[0].medicalInsurance.member

    const realDni = pfaApiData[0].credential.replace(/\./g, "") || dni
    const realSex = sexConvertionPfa(pfaApiData[0].sex) || sex
    const realDob = pfaApiData[0].birth || dob

    if (isDependant) {
      await HealthInsuranceRepository.dependantUpdateDocument(patient.id, coverageData.id, { affiliate_id })
      await DependantRepository.doublePathUpdate(patient.core_id, patient.id, { sex: realSex, dni: realDni, dob: realDob })
    } else {
      await HealthInsuranceRepository.updateDocument(patient.id, coverageData.id, { affiliate_id })
      await PatientRepository.updateDocument(patient.id, { sex: realSex, dni: realDni, dob: realDob })
    }
  } catch (error) {
    Logger.error(`[PFA - updateAffiliateNumber] Error updating patient: ${error}`)
  }
}


// Utilizamos esta función para emparejar los datos de nuestra DB con los datos del padrón de IOMA,
// Esto solo va a funcionar en caso de que el número de afiliado esté bien o el dni esté bien, si ambos datos están mal no podemos hacer mucho
export const revalidatePatient = async (coverageData: IHealthInsurance<Timestamp>, patient: IPatient<Timestamp>, isDependant: boolean) => {
  const { affiliate_id } = coverageData

  // En caso de que el número de afiliado no tenga inconsistencias claras, actualizamos solo sexo y dni, en caso de que no sean iguales a los que tiene el padrón de IOMA
  if (affiliate_id) {
    return await updateDniAndSex(coverageData, patient, isDependant)
  } else {
    // En caso de notar inconsistencias en el numero de afiliado, lo buscamos en el api de ioma utilizando DNI y SEXO
    return await updateAffiliateNumber(coverageData, patient, isDependant)
  }
}


function isPediatric(birth: string) {
  const today = moment()
  const dob = moment(birth, "YYYY-MM-DD") // Asegúrate de que el formato de la fecha esté correcto
  const diff = today.diff(dob, "years")
  return diff < 18
}

// Iteramos sobre los coverages del paciente y aplicamos los cambios solo sobre los coverages necesarios
export const applyPFAPatientUpdates = async(corporate: IHealthInsurance<Timestamp>, patient: IPatient<Timestamp>, isDependant: boolean)=>{
  // nos traemos toda la data de los coverages
  if (
    !corporate.affiliate_id ||
        !dniRegex.test(patient.dni) ||
        !sexStringRegex.test(patient.sex) ||
        isPediatric(patient.dob)
  ) {
    await revalidatePatient(corporate, patient, isDependant)
  }
}