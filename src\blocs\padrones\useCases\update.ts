import { Padrones } from "@umahealth/sql-models"
import { IPadrones } from "@umahealth/entities"
import { NotFoundException } from "@nestjs/common"

export const update = async (corporate: string, rows: IPadrones[]): Promise<boolean> => {

  const transaction = await Padrones.sequelize.transaction()

  try {
    await Padrones.update({ "active":false }, {where:{ corporate }, transaction})

    await Promise.all(rows.map(async (row) => {
      const updated = await Padrones.update({ "active":row.active }, { where: { corporate, "documentType": row.documentType, "document": row.document, "affiliateNumber":row.affiliateNumber }, transaction})
      if(updated[0] < 1){
        await Padrones.create({"corporate": row.corporate,
          "documentType": row.documentType, "document": row.document, "active": row.active, "affiliateNumber": row.affiliateNumber,
          "plan": row.plan, "nationality": row.nationality, "name": row.name, "surname": row.surname,
          "address": row.address, "phoneNumber": row.phoneNumber}, {transaction})
      }
    }))

    await transaction.commit()
    return true
  } catch (err) {
    await transaction.rollback()
    throw new NotFoundException(`Error updating padron => ${err.message}`)
  }
}
