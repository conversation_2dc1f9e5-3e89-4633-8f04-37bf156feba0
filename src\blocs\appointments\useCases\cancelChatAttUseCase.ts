import { AppointmentRepository } from "@umahealth/repositories"
import { IGuardiaAppointment, appointmentStates, countries } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"

export const cancelChatAttAppointmentUseCase = async (country: countries, assignationId: string, state: appointmentStates) => {
  const currentAppointment: IGuardiaAppointment = await AppointmentRepository.getByAssignationId("chatAtt", country, assignationId)
  return await AppointmentRepository.update("chatAtt", country, assignationId, {
    state,
    timestamps: {
      ...currentAppointment.timestamps,
      dt_cancel: Timestamp.now()
    }
  })
}
