import { InternalServerErrorException } from "@nestjs/common"
import { INom035User } from "@umahealth/entities"
import { Nom035Form, Nom035UserPosition, Nom035Users } from "@umahealth/sql-models"
import { fn, Op, Transaction, WhereOptions } from "sequelize"
import { Gender, GenderTypes, IExtraFilters, INom035CorporatePosition, INom035Person, INom035UserFilters, UserWithPosition } from "../interfaces"

class nom035_DTO {

  async createUser(profile: INom035Person, t?: Transaction){
    return await Nom035Users.create({...profile}, {transaction: t})
  }

  async createPosition(position: INom035CorporatePosition, t?: Transaction){
    return await Nom035UserPosition.create({...position}, {transaction: t})
  }

  async getAllUsers(attributesOptions?: string[], emailFilter?: string[]): Promise<INom035User[]> {
    const whereOptions = emailFilter?.length ? {email: {[Op.in]: emailFilter}} : undefined

    const options = {
      attributes: attributesOptions,
      where: whereOptions,
    }

    return Nom035Users.findAll(options)
  }

  async getTotalUsers(corporateId: string, filters?: INom035UserFilters): Promise<number>{
    return await Nom035Users.count({
      include:[
        {
          model: Nom035UserPosition,
          where: { "corporateId": corporateId },
        },
      ],
      where: {
        ...(filters?.searchText && {
          fullname: {
            [Op.iLike]: `%${filters?.searchText}%`
          },
        }
        )
      }
    })
  }

  async getUsers(corporateId: string, filters?: INom035UserFilters): Promise<INom035User[]>{
    return await Nom035Users.findAll({
      include:[
        {
          model: Nom035UserPosition,
          where: { "corporateId": corporateId },
        },
      ],
      where: {
        ...(filters?.searchText && {
          fullname: {
            [Op.iLike]: `%${filters.searchText}%`
          },
        }
        )
      },
      ...(filters?.offset && { offset: filters.offset }),
      ...(filters?.pageSize && { limit: filters.pageSize })
    })
  }

  private createUserWhereOptions(extraFilters: Partial<IExtraFilters>) {
    const usersWhereOptions: { active: boolean, fullname?: { [key: string]: string } } = { active: true }

    if(Boolean(extraFilters?.filterByState)) {
      usersWhereOptions.active = extraFilters.filterByState
    }
    if(Boolean(extraFilters?.filterByName)) {
      usersWhereOptions.fullname = { [Op.iLike]: fn("unaccent", `%${extraFilters.filterByName}%`) }
    }

    return usersWhereOptions
  }

  private createUserPositionWhereOptions(corporateId: string, extraFilters: IExtraFilters) {
    const userPositionsWhereOptions: { corporateId: string, area?: string } = { "corporateId": corporateId }

    if(Boolean(extraFilters?.filterByArea)) {
      userPositionsWhereOptions.area = extraFilters.filterByArea
    }

    return userPositionsWhereOptions
  }

  private async createFormWhereOptions(extraFilters: IExtraFilters) {
    let userFormWhereOptions: WhereOptions = { form: { [Op.in]: ["c1", "c2", "c3"] } }
    const allowedUids: string[] = []

    if (Boolean(extraFilters?.filterByCuestionarios)) {
      const cuestionarios = extraFilters?.filterByCuestionarios.split("-")

      userFormWhereOptions.form = { [Op.iLike]: cuestionarios[0] }
      if (cuestionarios.length > 1) {
        userFormWhereOptions = {
          [Op.and]: cuestionarios.map((form) => ({
            form: { [Op.iLike]: `${form}` }
          }))
        }
      }
    }

    const users = await Nom035Users.findAll({
      attributes:["uid"],
      include:[
        {
          model: Nom035Form,
          attributes: [],
          where: userFormWhereOptions,
          required: true,
        }
      ]
    })

    users.forEach(user => {
      allowedUids.push(user.dataValues.uid)
    })

    return allowedUids
  }

  async getUsernamesByProximity(corporateId: string, extraFilters: Partial<IExtraFilters>) {
    const userWhereOptions = this.createUserWhereOptions(extraFilters)

    const users = Nom035Users.findAll({
      attributes:["fullname", "uid", "id"],
      where: userWhereOptions,
      include:[
        {
          model:Nom035UserPosition,
          attributes: ["area"],
          where: { "corporateId": corporateId }
        }
      ],
      limit: 20,
    })

    const usernames = (await users).map(user => {
      const area = user.positions[0]?.area || "Sin area"
      return { fullname: user.get("fullname"), area, id: user.get("id"), uid: user.get("uid") }
    })

    return usernames
  }

  async getInformsUsers(corporateId: string, extraFilters: IExtraFilters): Promise<INom035User[]>{
    let userPositionsWhereOptions: WhereOptions = { "corporateId": corporateId }
    let userWhereOptions: WhereOptions = { active: true }
    const offset = (extraFilters.page - 1) * 20
    const limit = 20

    if(Object.keys(extraFilters).length > 1) {
      userPositionsWhereOptions = this.createUserPositionWhereOptions(corporateId, extraFilters)
      userWhereOptions = this.createUserWhereOptions(extraFilters)
    }

    if(extraFilters.filterByCuestionarios) {
      const allowedUids = await this.createFormWhereOptions(extraFilters)

      userWhereOptions = {...userWhereOptions, uid: {[Op.in]: allowedUids}}
    }

    return Nom035Users.findAll({
      attributes:["fullname","active","uid", "email"],
      where: userWhereOptions,
      offset,
      limit,
      include:[
        {
          model:Nom035UserPosition,
          attributes:["area", "branch", "position", "division"],
          where: userPositionsWhereOptions
        },
        {
          model: Nom035Form,
          attributes: ["form"],
        },
      ]
    })
  }

  async countUsers(corporateId: string, extraFilters: IExtraFilters) {
    let userPositionsWhereOptions: WhereOptions = { "corporateId": corporateId }
    let userWhereOptions: WhereOptions = { active: true }

    if(Object.keys(extraFilters).length > 1) {
      userPositionsWhereOptions = this.createUserPositionWhereOptions(corporateId, extraFilters)
      userWhereOptions = this.createUserWhereOptions(extraFilters)
    }

    if(extraFilters.filterByCuestionarios) {
      const allowedUids = await this.createFormWhereOptions(extraFilters)

      userWhereOptions = {...userWhereOptions, uid: {[Op.in]: allowedUids}}
    }

    return Nom035Users.count({
      where: userWhereOptions,
      include:[
        {
          model:Nom035UserPosition,
          where: userPositionsWhereOptions
        }
      ]
    })
  }

  async getUserById(userId: string): Promise<INom035User>{
    return await Nom035Users.findByPk(userId, {
      include: [{
        attributes: {
          exclude: ["userId", "created_at", "modified_at"]
        },
        model: Nom035UserPosition,
        where: { userId }
      }]
    })
  }

  async getUserByUid(uid: string): Promise<INom035User>{
    return await Nom035Users.findOne({where: {uid}})
  }

  async getInformUserByUid(uid: string): Promise<INom035User> {
    return await Nom035Users.findOne({
      where: { uid },
      attributes: [
        "fullname",
        "email",
        "birthDate",
        "phoneNumber",
        "maritalStatus",
        "gender",
        "nationalId",
        "active"
      ],
      include: [{
        model: Nom035UserPosition,
        attributes: [
          "corporateId",
          "area",
          "contractType",
          "workingSchedule",
          "rotationalShifts",
          "educationLevel",
          "hiringDate",
          "positionDate",
          "area",
          "seniority",
          "branch",
          "position",
          "division"
        ]
      }]
    })
  }

  async getUsersByCoach(corporateId: string, emails: string[]){
    return await Nom035Users.findAll({where: {email: emails},
      attributes: ["uid", "fullname", "active", "email"],
      include:[
        {
          model: Nom035UserPosition,
          attributes: ["corporateId"],
          where: { "corporateId": corporateId },
        },
      ],})
  }

  async createUserInSQLBulk(users:Omit<number, string>[]){
    try{
      return Nom035Users.bulkCreate(users, {
        ignoreDuplicates: true,
        returning: ["id","email"],
        hooks: true
      })
    }catch(error) {
      throw new InternalServerErrorException(`Error on SQL Bulk. Error: ${error.message}`)
    }
  }

  async createUserPositionInSQLBulk(userPosition: Omit<number, string>[]){
    await Nom035UserPosition.bulkCreate(userPosition)
  }

  async updateUserStatus(uid: string, active: boolean){
    const [affectedRowsNumber] = await Nom035Users.update({active}, {where: {uid}})
    return affectedRowsNumber > 0
  }

  async updateUser(uid: string, data: Partial<INom035Person>){
    const [affectedRowsNumber] = await Nom035Users.update({...data}, {where: {uid}})
    return affectedRowsNumber > 0
  }

  async updateUserUid(user: Partial<INom035Person>) {
    const whereCondition: Record<string, string | number> = {}

    if (user.id) {
      whereCondition.id = user.id
    }

    if (user.email) {
      whereCondition.email = user.email
    }

    const [affectedRowsNumber] = await Nom035Users.update(
      {uid: user.uid},
      {
        where: whereCondition
      }
    )
    return affectedRowsNumber > 0
  }

  async updateUserPosition(id: number, data: Partial<INom035CorporatePosition>){

    const [affectedRowsNumber] = await Nom035UserPosition.update({...data}, {where: {userId: id}})
    return affectedRowsNumber > 0

  }

  async getUsersSeniority(corporateId: string): Promise<INom035User[]>{
    return await Nom035Users.findAll({
      attributes: {
        include: []
      },
      include:[
        {
          attributes: ["seniority"],
          model: Nom035UserPosition,
          where: { "corporateId": corporateId },
        },
      ],
    })
  }

  async getCompanyUserAttributes(corporateId: string) {
    const results = await Nom035UserPosition.findAll({
      attributes: [
        "area",
        "seniority",
        "branch"
      ],
      where: { corporateId },
      raw: true,
    })
    const attributes = {
      area: new Set<string>(),
      seniority: new Set<string>(),
      branch: new Set<string>(),
    }

    results.forEach(result => {
      if (result.area) attributes.area.add(result.area)
      if (result.seniority) attributes.seniority.add(result.seniority)
      if (result.branch) attributes.branch.add(result.branch)
    })

    return {
      area: Array.from(attributes.area),
      seniority: Array.from(attributes.seniority),
      branch: Array.from(attributes.branch),
    }

  }

  async updateUserForm(email: string, newEmail: string) {
    return await Nom035Form.update(
      { email: newEmail },
      {
        where: {
          email,
        }
      }
    )
  }

  async getEmployeesStatistics(corporateId: string) {
    const users = await Nom035Users.findAll({
      attributes: [
        "gender",
        "birthDate",
      ],
      include: [{
        model: Nom035UserPosition,
        attributes: ["area", "branch"],
        where: {corporateId},
      }],
      raw: true,
    }) as unknown as UserWithPosition[]

    const areaStats: Record<string, number> = {}
    const branchStats: Record<string, number> = {}
    const ageGenderStats: Record<number, Record<string, number>> = {}

    users.forEach((user) => {
      const area = user["positions.area"]
      const branch = user["positions.branch"]
      const age = new Date().getFullYear() - new Date(user.birthDate).getFullYear()
      const genderKey = user.gender as keyof typeof Gender

      if (area) {
        areaStats[area] = (areaStats[area] || 0) + 1
      }
      if (branch) {
        branchStats[branch] = (branchStats[branch] || 0) + 1
      }
      if (!ageGenderStats[age]) {
        ageGenderStats[age] = {
          "Masculino": 0,
          "Femenino": 0,
          "No especificado": 0,
        }
      }

      const mappedGender = GenderTypes[genderKey]
      ageGenderStats[age][mappedGender] += 1
    })

    return {
      ageGenderStats,
      area: areaStats,
      branches: branchStats,
      totalUsers: users.length,
    }
  }

}

const nom035DTO = new nom035_DTO()
export default nom035DTO
