import {
  IDocumentList,
  MedicalRecordRepository,
} from "@umahealth/repositories"
import {
  dependantUid,
  IHealthInsurance,
  IMedicalRecord,
  IPatient,
  MedicalRecord,
  medicalRecordMr,
  medicalRecordPatient,
  paymentData,
} from "@umahealth/entities"
import { convertDateToTimezoneString } from "@umahealth/time"
import { Timestamp } from "@google-cloud/firestore"
import { IMrPreds, IPatientGeo } from "../medical-records-bloc.interfaces"

export const CreateGuardiaMedicalRecordNewUseCase = async (
  assignationId: string,
  patientDocument: IPatient<Timestamp>,
  coverage: IHealthInsurance,
  dependantUid: dependantUid,
  dt_create: Date,
  motivosDeConsulta: string,
  uid: string,
  payment_data: paymentData,
): Promise<IDocumentList<IMedicalRecord<Timestamp>>> => {
  const patient: medicalRecordPatient = {
    address: patientDocument.address || "",
    antecedentes: "",
    affiliate_number: coverage.affiliate_id || "",
    dependant_uid: dependantUid,
    dni: patientDocument.dni,
    dob: patientDocument.dob,
    country: patientDocument.country || "AR",
    fullname: patientDocument.fullname,
    n_afiliado: coverage.affiliate_id || "",
    obra_social: coverage.id,
    sex: patientDocument.sex,
    uid: uid,
    ws: patientDocument.ws,
  }
  const geo: IPatientGeo = {
    geohash: patientDocument?.geohash ||null,
    lat: patientDocument?.lat || null,
    lon: patientDocument?.lon || null
  }
  const mr_preds: IMrPreds = {
    abort_description: null,
    destino_final: "",
    diagnostico: "",
    epicrisis: "",
    gduh: null,
    motivos_de_consulta: motivosDeConsulta,
    observaciones: null,
    pre_clasif: ""
  }
  const mr: medicalRecordMr  = {
    alertas: null,
    destino_final: "",
    diagnostico: "",
    dt: null,
    dt_cierre: null,
    epicrisis: "",
    motivos_de_consulta: motivosDeConsulta,
    observaciones: null,
    ordenes: [],
    receta: [],
    reposo: null,
    receta_ref: null,
    tratamiento: null,
    specialist_referral: null
  }
  const provider = {
    cuit: "bag",
    especialidad: "online_clinica_medica",
    fullname: "",
    uid: "",
    ws: ""
  }


  const medicalRecord = new MedicalRecord<Timestamp>()
  medicalRecord.assignation_id = assignationId
  medicalRecord.att_category = "GUARDIA_RANDOM"
  medicalRecord.created_dt = convertDateToTimezoneString(dt_create, "YYYY-MM-DD HH:mm:ss")
  medicalRecord.especialidad = "online_clinica_medica"
  medicalRecord.payment_data = payment_data
  medicalRecord.incidente_id = assignationId
  medicalRecord.mr_preds = mr_preds
  medicalRecord.mr = mr
  medicalRecord.patient = patient
  medicalRecord.provider = provider
  medicalRecord.geo = geo
  medicalRecord.timestamps = {
    dt_create: Timestamp.fromDate(dt_create),
    dt_booking: Timestamp.fromDate(dt_create),
    dt_assignation: Timestamp.fromDate(dt_create),
  }

  /* This will be soon deprecated */
  medicalRecord.created_dt = convertDateToTimezoneString(dt_create, "YYYY-MM-DD HH:mm:ss")

  return await MedicalRecordRepository.create(uid, assignationId, medicalRecord as IMedicalRecord<Timestamp>)
}
