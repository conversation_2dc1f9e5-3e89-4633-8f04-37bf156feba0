import { action, appointmentServices, countries, dependantUid, IAppointment, IChatAttAppointment, IOnSitePatient, paymentData, practice, TSpecialties, IOnDemandAppointment, IOnSiteAppointment, appointmentStates, call, IOnlineAppointment, IPatient, IHealthInsurance } from "@umahealth/entities"
import { BadRequestException, Injectable, Logger } from "@nestjs/common"
import { DependantRepository, IDocumentList, PatientRepository } from "@umahealth/repositories"
import { IGeo } from "src/patient-app/guardia/guardia.interface"
import {
  cancelSpecialistAppointment,
  CreateGuardiaAppointmentUseCase,
  findFreeAppointmentsUseCase,
  updateOnSiteAppointmentUseCase,
  createExpressUseCase,
  startExpressUseCase,
  updateExpressUseCase,
  rateExpressUseCase,
  getAppointmentById,
  CreateChatAttAppointmentUseCase,
  updateChatAttAppointment,
  confirmAppointmentUseCase,
  getTakenAppointmentsLastMonthUseCase,
  preassignGuardiaUseCase,
  CreateQuickPrescriptionUseCase,
  updateQuickPrescriptionAppointment,
  CreateOnDemandAppointmentUseCase,
  UpdateOnDemandAppointmentUseCase,
  closeOnSiteUseCase,
  updateOnsiteAppointmentAndRequestUseCase,
  getAppointmentsByProviderUid,
  createAppointmentOnsiteUseCase,
  updateOnlineAppointmentTransactionUseCase,
  getOnlineAppointmentsByState,
  updateAssignationUseCase,
  getDoneGuardiaAppointmentsByDay,
  getAverageWaitingTimeUseCase,
  analyzeAppointmentLogsUseCase
} from "./useCases"
import { appointmentCancelMotive } from "@umahealth/entities/src/entities/appointments/types"
import { IUpdateOnlineAppointment } from "./useCases/interfaces"
import { DocumentReference, Transaction, Timestamp } from "@google-cloud/firestore"
import { CreateGuardiaAppointmentNewUseCase } from "./useCases/createGuardiaNewUseCase"
import { closeOnlineUseCase } from "./useCases/closeOnlineUseCase"
import { closeOnDemandUseCase } from "./useCases/closeOnDemandUseCase"
import { IMakeOnsiteFarmatodoAppointmentData } from "src/patient-app/onsite/onsite.interface"
import { takeOnsiteAppointmentTransactionUseCase } from "./useCases/takeOnsiteAppointmentTransactionUseCase"
import { resetOnsiteAppointmentTransactionUseCase } from "./useCases/resetOnsiteAppointmentUseCase"
import { IAppointmentLogsAnalysis } from "./appointments.entities"

@Injectable()
export class AppointmentsBloc {
  async updateOnlineTransaction(transactionObj: Transaction,
    lockedAppointment: IOnlineAppointment<Timestamp>,
    appointmentReference: DocumentReference,
    appointmentData: IUpdateOnlineAppointment) {
    return updateOnlineAppointmentTransactionUseCase(transactionObj, lockedAppointment, appointmentReference, appointmentData)
  }

  async createGuardia(assignationId: string, country: countries, coverageName: string, creationTime: Date, dependantUid: dependantUid, motivosDeConsulta: string, paymentData: paymentData, uid: string, vip: boolean, state?: appointmentStates, providerUid?: string, call?: IDocumentList<call>, geo?: IGeo) {
    return CreateGuardiaAppointmentUseCase(assignationId, country, coverageName, creationTime, dependantUid, motivosDeConsulta, paymentData, uid, vip, state, providerUid, call, geo)
  }

  async findFreeAppointments(country: countries, cuit: string, queryDate: string, service: appointmentServices, specialty: TSpecialties, uid: string): Promise<IAppointment<Timestamp>[]> {
    return findFreeAppointmentsUseCase(country, cuit, queryDate, service, specialty, uid)
  }

  async updateOnSiteAppointment(action: action, assignationId: string, country: countries, specialization: string, patient: IOnSitePatient, practices: Array<practice>, service: string, uid: string) {
    return updateOnSiteAppointmentUseCase(action, assignationId, country, specialization, patient, practices, uid, service)
  }

  async updateOnsiteAppointmentAndRequest(country: countries, assignationId: string, appointment: IOnSiteAppointment) {
    return updateOnsiteAppointmentAndRequestUseCase(country, assignationId, appointment)
  }

  async patientCancelSpecialist(service: appointmentServices, country: countries, assignationId: string, action?: action, cancelMotive?: appointmentCancelMotive) {
    return cancelSpecialistAppointment(service, country, assignationId, action, cancelMotive)
  }

  async getAppointmentById<T>(service: appointmentServices, country: countries, assignationId: string) {
    return getAppointmentById<T>(service, country, assignationId)
  }

  async createChatAtt(assignationId: string, country: countries, uid: string, dependantUid: dependantUid, motivosDeConsulta: string, creationTime: Date, paymentData: paymentData, pediatric: boolean, patientDocument: IPatient<Timestamp>): Promise<IDocumentList<IChatAttAppointment<Timestamp>>> {
    return CreateChatAttAppointmentUseCase(assignationId, country, uid, dependantUid, motivosDeConsulta, creationTime, paymentData, pediatric, patientDocument)
  }

  async updateChatAttAppointment(country: countries, assignationId: string, appointment: Partial<IChatAttAppointment<Timestamp>>): Promise<IDocumentList<Partial<IChatAttAppointment<Timestamp>>>> {
    return updateChatAttAppointment(assignationId, country, appointment)
  }

  async createQuickPrescription(assignationId: string, country: countries, uid: string, dependantUid: dependantUid, motivosDeConsulta: string, creationTime: Date, paymentData: paymentData, pediatric: boolean, specialty: string): Promise<IDocumentList<IChatAttAppointment<Timestamp>>> {
    return CreateQuickPrescriptionUseCase(assignationId, country, uid, dependantUid, motivosDeConsulta, creationTime, paymentData, pediatric, specialty)
  }

  async updateQuickPrescriptionAppointment(country: countries, assignationId: string, appointment: Partial<IChatAttAppointment<Timestamp>>): Promise<IDocumentList<Partial<IChatAttAppointment<Timestamp>>>> {
    return updateQuickPrescriptionAppointment(assignationId, country, appointment)
  }

  async createExpressAppointment() {
    return createExpressUseCase()
  }

  async startExpressAppointment() {
    return startExpressUseCase()
  }

  async updateExpressAppointment() {
    return updateExpressUseCase()
  }

  async rateExpressAppointment() {
    return rateExpressUseCase()
  }

  async confirmAppointment(assignationId: string, country: countries) {
    return confirmAppointmentUseCase(assignationId, country)
  }

  async getTakenAppointmentsLastMonth(corporate: string, uid: string, dependantUid?: dependantUid) {

    let patient : IPatient<Timestamp>

    if (dependantUid) {
      try {
        patient = await DependantRepository.getByUidFromDependant(dependantUid)
      } catch(err){
        /** A veces vienen pacientes que dicen ser dependants y son users principales, no logré encontrar el error pero este workaround fixea que no los encuentre */
        Logger.error(`[ ${this.getTakenAppointmentsLastMonth.name} ] => Dependant with uid ${dependantUid} not found`)
        patient = await PatientRepository.getByUid(uid)
      }
    } else {
      patient = await PatientRepository.getByUid(uid)
    }
    if (!patient) {
      throw new BadRequestException(`[ ${this.getTakenAppointmentsLastMonth.name} ] => ${dependantUid ? `Dependant with uid ${dependantUid}` : `User with uid ${uid}`} not found`)
    }
    return getTakenAppointmentsLastMonthUseCase(patient.dni, corporate)
  }

  async preassignGuardia(assignation: IDocumentList<Partial<IAppointment<Timestamp>>>, providerUid: string) {
    return preassignGuardiaUseCase(assignation, providerUid)
  }

  async createOnDemand(assignationId: string, country: countries, providerUid: string, uid: string, dependantUid: dependantUid, motivosDeConsulta: string, creationTime: Date, paymentData: paymentData, pediatric: boolean): Promise<IDocumentList<IOnDemandAppointment<Timestamp>>> {
    return CreateOnDemandAppointmentUseCase(assignationId, country, providerUid, uid, dependantUid, motivosDeConsulta, creationTime, paymentData, pediatric)
  }

  async updateOnDemandAppointment(country: countries, assignationId: string, appointment: Partial<IOnDemandAppointment<Timestamp>>): Promise<IDocumentList<Partial<IOnDemandAppointment<Timestamp>>>> {
    return UpdateOnDemandAppointmentUseCase(assignationId, country, appointment)
  }

  async closeOnSiteUseCase(country: countries, assignationId: string, appointment: Partial<IAppointment<Timestamp>>): Promise<IDocumentList<Partial<IAppointment<Timestamp>>>> {
    return closeOnSiteUseCase(assignationId, country, appointment)
  }

  async getAllByProviderUid(providerUid: string, country: countries, services: appointmentServices[], states: appointmentStates[]) {
    return getAppointmentsByProviderUid(providerUid, country, services, states)
  }

  async createOnsiteAppointment(assignationId: string, appointment: IAppointment) {
    return createAppointmentOnsiteUseCase(assignationId, appointment)
  }

  async getOnlineAppointmentsByState(state: appointmentStates) {
    return getOnlineAppointmentsByState(state)
  }

  async updateAssignationUseCase(service: appointmentServices, country: countries, assignationId: string, data: Partial<IAppointment<Timestamp>>) {
    return updateAssignationUseCase(service, country, assignationId, data)
  }

  async createGuardiaNew(assignationId: string, country: countries, creationTime: Date, dependantUid: dependantUid, motivosDeConsulta: string, paymentData: paymentData, uid: string, patientDocument: IPatient<Timestamp>, coverageDocument?: IHealthInsurance<Timestamp>, geo?: IGeo, emergenciaIncidenteId?: string, invitationId?: string) {
    return CreateGuardiaAppointmentNewUseCase(assignationId, country, creationTime, dependantUid, motivosDeConsulta, paymentData, uid, patientDocument, coverageDocument, geo, emergenciaIncidenteId, invitationId)
  }

  async getDoneByProviderOnDay(uid: string, country: countries, day: Date, limit: number) {
    return getDoneGuardiaAppointmentsByDay(uid, country, day, limit)
  }

  async getAverageWaitingTime() {
    return getAverageWaitingTimeUseCase()
  }

  async analyzeAppointmentLogs(assignationId: string, patientUid: string, providerUid: string): Promise<IAppointmentLogsAnalysis> {
    return analyzeAppointmentLogsUseCase(assignationId, patientUid, providerUid)
  }

  async closeOnline(assignationId: string, country: countries, appointment: Partial<IAppointment<Timestamp>>) {
    return closeOnlineUseCase(assignationId, country, appointment)
  }

  async closeOnDemand(assignationId: string, country: countries, appointment: Partial<IOnDemandAppointment<Timestamp>>) {
    return closeOnDemandUseCase(assignationId, country, appointment)
  }

  async updateOnsiteTransaction(
    transactionObj: Transaction,
    lockedAppointment: IOnSiteAppointment<Timestamp>,
    appointmentReference: DocumentReference,
    appointmentData: IMakeOnsiteFarmatodoAppointmentData
  ) {
    return takeOnsiteAppointmentTransactionUseCase(transactionObj, lockedAppointment, appointmentReference, appointmentData)
  }

  async resetOnsiteTransaction(
    transactionObj: Transaction,
    lockedAppointment: IOnSiteAppointment<Timestamp>,
    appointmentReference: DocumentReference,
  ) {
    return resetOnsiteAppointmentTransactionUseCase(transactionObj, lockedAppointment, appointmentReference)
  }
}
