import AiModelsBloc from "../../ai-models/ai-models.bloc"
import { MedicalRecordRepository } from "@umahealth/repositories"

export async function addLabel(uid: string, assignationId: string, labels: string) {
  const medicalRecordDocument = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  const motiveFromMR = medicalRecordDocument["mr"]["motivos_de_consulta"]
  const epicrisisFromMR = medicalRecordDocument["mr"]["epicrisis"]

  // Construct phrase for NLP predict
  const epicrisis = `${epicrisisFromMR}. ${labels}`
  const frase = `<MOTIVODECONSULTA> ${motiveFromMR} <EPICRISIS> ${epicrisis}`

  // Try predict
  const predictedDiagnostic = await AiModelsBloc.diagnosticPredict(frase)

  // Update document with new diagnostico and epicrisis
  const dataToUpdateMR = {
    ...medicalRecordDocument,
    mr: {
      ...medicalRecordDocument.mr,
      epicrisis
    },
    mr_preds: {
      ...medicalRecordDocument.mr_preds,
      diagnostico: typeof predictedDiagnostic !== "boolean" ? predictedDiagnostic : "",
      epicrisis
    }
  }
  return await MedicalRecordRepository.update(uid, assignationId, dataToUpdateMR)
}
