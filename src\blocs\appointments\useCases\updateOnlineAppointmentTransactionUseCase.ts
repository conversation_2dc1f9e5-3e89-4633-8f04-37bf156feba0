import {  ConflictException } from "@nestjs/common"
import { ProviderRepository } from "@umahealth/repositories"
import { IOnlineAppointment } from "@umahealth/entities"
import { DocumentReference, Timestamp, Transaction } from "@google-cloud/firestore"
import { IUpdateOnlineAppointment } from "./interfaces"

export const updateOnlineAppointmentTransactionUseCase = async(
  transactionObj: Transaction,
  lockedAppointment: IOnlineAppointment<Timestamp>,
  appointmentReference: DocumentReference,
  appointmentData: IUpdateOnlineAppointment) =>{
  const { country, providerUid, startDate, token, room } = appointmentData
  const provider = await ProviderRepository.getByProviderUid(providerUid)

  if (lockedAppointment.state !== "ASSIGN" && lockedAppointment.uid !== provider.uid) {
    throw new ConflictException(`This appointment ${lockedAppointment.assignation_id} is already taken by ${lockedAppointment.uid}, cannot start appointment`)
  }

  if (lockedAppointment.state === "ASSIGN") {
    lockedAppointment.state = "ATT"
  }

  lockedAppointment.fullname = provider.fullname
  lockedAppointment.uid = provider.uid
  lockedAppointment.cuit = provider.cuit
  lockedAppointment.country = country
  lockedAppointment.room = room
  lockedAppointment.token = token
  lockedAppointment.timestamps.dt_start = Timestamp.fromDate(startDate)

  transactionObj.update(appointmentReference, { ...lockedAppointment })
}
