import { DependantRepository, HealthInsuranceRepository, PatientRepository } from "@umahealth/repositories";
import { 
    updateAffiliationData,
    updatePersonalData,
 } from "../useCases/revalidateIOMAPatient";
import { getIomaPatientByNumAfi } from "src/blocs/coverage/apiValidations/patients/IOMA";
import { IHealthInsurance, IPatient } from "@umahealth/entities";
import { coverageDataMock, iomaApiDataMock, patientMock } from "./__mocks__";

  jest.mock("@umahealth/repositories");
  jest.mock('src/blocs/coverage/apiValidations/patients/IOMA');
  jest.mock('src/blocs/coverage/apiValidations/dependants/IOMA');




  describe('updateDniAndSex', () => {
    afterEach(() => {
        jest.clearAllMocks()
    });

    it('should update patient data if IOMA data is different', async () => {
        const isDependant = false;
        (PatientRepository.updateDocument as jest.Mock).mockResolvedValue({});
        await updatePersonalData(patientMock as IPatient, isDependant, iomaApiDataMock);
        expect(PatientRepository.updateDocument).toHaveBeenCalledTimes(1)
        expect(DependantRepository.doublePathUpdate).toHaveBeenCalledTimes(0)
    
      });
    
      it('should do nothing patient data if IOMA data is equal', async () => {
        const isDependant = false; 
        const iomaApiDataMockDniEqual = {...iomaApiDataMock, NumeroDocumento: 111, Sexo: 1, FechaNacimiento: "28/06/1984"}               
        await updatePersonalData(patientMock as IPatient, isDependant, iomaApiDataMockDniEqual);
        expect(PatientRepository.updateDocument).toHaveBeenCalledTimes(0)
        expect(DependantRepository.doublePathUpdate).toHaveBeenCalledTimes(0)
    
      });

      it("should update dependant data if IOMA data is different", async () => {
        const isDependant = true;
        
        (DependantRepository.doublePathUpdate as jest.Mock).mockResolvedValue({});
        (getIomaPatientByNumAfi as jest.Mock).mockResolvedValue(iomaApiDataMock)
        
        await updatePersonalData(patientMock as IPatient, isDependant, iomaApiDataMock);
        expect(DependantRepository.doublePathUpdate).toHaveBeenCalledTimes(1)
        expect(PatientRepository.updateDocument).toHaveBeenCalledTimes(0)
      })

      it('should do nothing dependant data if IOMA data is equal', async () => {
        const isDependant = true;
        const iomaApiDataMockDniEqual = {...iomaApiDataMock, NumeroDocumento: 111, Sexo: 1, FechaNacimiento: "28/06/1984"}   
        await updatePersonalData(patientMock as IPatient, isDependant, iomaApiDataMockDniEqual);
    
        expect(DependantRepository.doublePathUpdate).toHaveBeenCalledTimes(0)
        expect(PatientRepository.updateDocument).toHaveBeenCalledTimes(0)
      });

  })

  describe("updateAffiliateNumber", () => {
    afterEach(() => {
        jest.clearAllMocks()
    });
    beforeEach(() => {
      jest.spyOn(JSON, 'parse').mockReturnValue({
        Username: 'mocked_app_url',
        Password: 'mocked_app_url',
        client_id: 'mocked_client_id',
        client_secret: 'mocked_client_secret',
        password: 'mocked_password',
        username: 'mocked_username',
      });
    });


    it('shoukd update patient affiliate number if IOMA data is available', async () => {
        const isDependant = false;

        (HealthInsuranceRepository.updateDocument as jest.Mock).mockResolvedValue({});
        (HealthInsuranceRepository.dependantUpdateDocument as jest.Mock).mockResolvedValue({});
        await updateAffiliationData(coverageDataMock as IHealthInsurance, patientMock as IPatient, isDependant, iomaApiDataMock)

        expect(HealthInsuranceRepository.updateDocument).toHaveBeenCalledTimes(1)
        expect(HealthInsuranceRepository.dependantUpdateDocument).toHaveBeenCalledTimes(0)
    })

    it('shoukd update dependant affiliate number if IOMA data is available', async () => {
        const isDependant = true;

        (HealthInsuranceRepository.updateDocument as jest.Mock).mockResolvedValue({});
        (HealthInsuranceRepository.dependantUpdateDocument as jest.Mock).mockResolvedValue({});

        await updateAffiliationData(coverageDataMock as IHealthInsurance, patientMock as IPatient, isDependant, iomaApiDataMock)

        expect(HealthInsuranceRepository.updateDocument).toHaveBeenCalledTimes(0)
        expect(HealthInsuranceRepository.dependantUpdateDocument).toHaveBeenCalledTimes(1)
    })

  })