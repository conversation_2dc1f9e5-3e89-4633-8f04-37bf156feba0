import { IMedicalRecord, IProvider } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import * as moment from "moment"
import { TDocumentDefinitions } from "pdfmake/interfaces"
import { imgUrlToBase64 } from "src/utils/imgToBase64"
import emptyImg64 from "src/assets/emptyImgBase64"
import { IProviderWithMatricula } from "src/blocs/health-providers/entities/IProviderWithMatricula"

export async function createResumeTemplate(mr: IMedicalRecord<Timestamp>, provider: IProviderWithMatricula<Timestamp>) {
  const { timestamps, patient } = mr
  const utcTimestamp = timestamps?.dt_close?.toDate() || timestamps?.dt_assignation?.toDate() || timestamps?.dt_start?.toDate()
  let parsedDate = ""
  if (patient.country === "AR") {
    parsedDate = moment(utcTimestamp).tz("America/Argentina/Buenos_Aires").format("DD/MM/YYYY - HH:mm")
  } else {
    parsedDate = moment(utcTimestamp).tz("America/Mexico_City").format("DD/MM/YYYY - HH:mm")
  }

  const content = [
    {
      text: `${patient.fullname}`,
      style: "header",
    },
    {
      text: `${parsedDate} hs`,
      style:"fecha"
    },
    {
      text: "Resumen de la atención",
      style: "subheader",
    },
    {
      table: {
        widths: ["*"],
        body: [
          [
            {
              text: `${mr.mr.epicrisis}`,
              style: "text"
            },
          ]
        ]
      },
      layout: "noBorders"
    },
    {
      text: "Motivo de consulta",
      style: "subheader",
    },
    {
      table: {
        widths: ["*"],
        body: [
          [
            {
              text: `${mr.mr.motivos_de_consulta ?? "No se definió el motivo de la consulta"}`,
              style: "text"
            },
          ]
        ]
      },
      layout: "noBorders"
    },
    {
      text: "Diagnóstico",
      style: "subheader",
    },
    {
      table: {
        widths: ["*"],
        body: [
          [
            {
              text: `${mr.mr.diagnostico}`,
              style: "text"
            },
          ]
        ]
      },
      layout: "noBorders"
    },
    {
      text: "Tratamiento",
      style: "subheader",
    },
    {
      table: {
        widths: ["*"],
        body: [
          [
            {
              text: `${mr.mr.tratamiento}`,
              style: "text"
            },
          ]
        ]
      },
      layout: "noBorders"
    },
    {
      text: "Datos del profesional médico",
      style:"subheader"
    },
    {
      table: {
        widths: ["*"],
        body: [
          [
            {
              text: `Nombre: ${provider.fullname}`,
              style: "text"
            },
          ]
        ]
      },

      layout: "noBorders"
    },
    {
      table: {
        widths: ["*"],
        body: [
          [
            {
              text: `Matrícula: ${provider.matricula}`,
              style: "text"
            },
          ]
        ]
      },
      layout: "noBorders"
    },
    {
      table: {
        widths: ["*"],
        body: [
          [
            {
              image: "firma",
              style: {
                fillColor: "#e0e0e0"
              },
              width: 250,
              height: 130,
              alignment: "left",
            },
          ]
        ]
      },
      layout: "noBorders"
    }
  ]

  const styles= {
    header: {
      fontSize: 18,
      bold: true,
    },
    fecha: {
      fontSize: 14,
      bold: false,
    },
    subheader: {
      fontSize: 16,
      bold: true,
      marign:10
    },
    text: {
      fontSize: 14,
      bold: false,
      width: 100,
      margin: 7,
      fillColor: "#e0e0e0"
    }
  }

  const images = {
    firma: provider.signature ? await imgUrlToBase64(provider.signature) : emptyImg64,
  }
  // Configuración del documento
  const docDefinition = {
    content,
    defaultStyle: {
      fontSize: 11,
      lineHeight: 1.5,
    },
    styles,
    images,
    pageSize: "A4",
    pageMargins: [30, 30, 30, 30],
  } as unknown as TDocumentDefinitions

  return docDefinition
}
