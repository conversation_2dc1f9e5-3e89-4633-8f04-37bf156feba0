import { InternalServerErrorException } from "@nestjs/common"
import { TDocumentDefinitions } from "pdfmake/interfaces"
import * as PdfPrinter from "pdfmake/build/pdfmake"
import * as vfsFonts from "pdfmake/build/vfs_fonts"
import * as fs from "fs"
import { waitForFileExistence } from "src/utils/waitForFileExistence"
import { uploadLocalFileStorage } from "src/utils/files"
import { IMedicalRecord } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { createConstancyTemplate } from "../templates/constancyTemplate"

/**
 * Generates a PDF constancy from a medical record and uploads it to storage.
 *
 * @param mr - The medical record containing the data for the constancy.
 * @param file_path - The path in the storage where the PDF file will be saved.
 * @param chosenName - The desired name by the user.
 * @returns A promise that resolves in the urlFile when the PDF file has been successfully uploaded.
 * @throws If an error occurs during template creation or PDF generation.
 */

export async function createPdfConstancy(mr: IMedicalRecord<Timestamp>, file_path: string, chosenName: string): Promise<{ pdf: Buffer, url: string }> {
  let docDefinition: TDocumentDefinitions
  // localPath temporal storage
  const localPath = `./uploads/${mr.id}.pdf`
  try {
    // creation constancyTemplate
    docDefinition = await createConstancyTemplate(mr, chosenName)

  } catch (err) {
    throw new InternalServerErrorException(`[createPdfConstancyUseCase] => error creating constancy from template: ${err}`, err)
  }
  // set fonts
  const Roboto = {
    normal: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Regular.ttf" ], "base64"),
    bold: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Medium.ttf" ], "base64"),
    italics: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Italic.ttf" ], "base64"),
    bolditalics: Buffer.from(
      vfsFonts.pdfMake.vfs[ "Roboto-MediumItalic.ttf" ],
      "base64"
    ),
  }
  // creation localPdf
  try {
    const pdfDoc = PdfPrinter.createPdf(docDefinition, {}, { Roboto }).getStream()
    const createPdfPromise = await new Promise<Buffer>((resolve, reject) => {
      try {
        const chunks: Uint8Array[] = []
        pdfDoc.on("data", (chunk: Uint8Array) => chunks.push(chunk))
        pdfDoc.on("end", () => resolve(Buffer.concat(chunks)))
        pdfDoc.pipe(fs.createWriteStream(localPath))
        pdfDoc.end()
      } catch (err) {
        reject(err)
      }
    })

    await waitForFileExistence(localPath)
    // upload pdf file
    const storageUrl = await uploadLocalFileStorage(localPath, file_path)
    return { pdf: createPdfPromise, url: storageUrl }
  } catch (err) {
    throw new InternalServerErrorException(`[createPdfConstancyUseCase] => failed to generate pdf. ${err}`, err)
  }
}