import { IExtraFilters, INom035UserInformWithFormStatus } from "../../interfaces"
import nom035DTO from "../../models/nom035_userPositionDTO"
import { FieldSet } from "airtable"
import { PaginationResponseDto } from "src/utils/paginate/pagination-response"

// Se debe en un futuro quitar el users array ya que deberia dejar de usarse por completo
export const getInformsUsers = async (_usersArray: FieldSet[], corporateId: string, extraFilters?: IExtraFilters): Promise<PaginationResponseDto<INom035UserInformWithFormStatus>> => {
  const users = await nom035DTO.getInformsUsers(corporateId, extraFilters) as unknown as INom035UserInformWithFormStatus[]
  const countTotalUsers = await nom035DTO.countUsers(corporateId, extraFilters)
  if(!Boolean(users.length)){
    return { page: 1, pageSize: 20, maxPageNumber: 1, totalCount: 0, data: [] }
  }

  users.map((user, index) => {
    const temp_user = user
    temp_user.dataValues.area = temp_user.positions[0].area
    if(temp_user.positions[0]?.branch){
      temp_user.dataValues.branch = temp_user.positions[0].branch
    }
    if (temp_user.positions[0]?.position) {
      temp_user.dataValues.position = temp_user.positions[0].position
    }
    if (temp_user.positions[0]?.division) {
      temp_user.dataValues.division = temp_user.positions[0].division
    }
    delete temp_user.dataValues.positions
    users[index] = temp_user
  })

  const parsedFormUsers = users.map(user => {
    const c1Status = {
      "name": "c1",
      "completed": false
    }
    const c3Status = {
      "name": "c3",
      "completed": false
    }
    let hasInforms = false

    user.dataValues.forms.forEach(form => {
      switch (form.form) {
      case "c1":
        c1Status.completed = true
        hasInforms = true
        break
      case "c3":
        c3Status.completed = true
        hasInforms = true
        break
      default:
        break
      }
    })

    user.dataValues.hasInforms = hasInforms
    user.dataValues.formStatus = [c1Status, c3Status]
    return user
  })

  const response = parsedFormUsers.sort((a, b) => (a?.dataValues.hasInforms === b?.dataValues.hasInforms) ? 0 : a?.dataValues?.hasInforms ? -1 : 1)

  return { page: Number(extraFilters.page), pageSize: 20, maxPageNumber: Math.ceil(countTotalUsers / 20), totalCount: countTotalUsers, data: response }
}



