import { IMedicalRecord } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { TDocumentDefinitions } from "pdfmake/interfaces"
import * as moment from "moment"
import logoUma64 from "src/assets/logoUmaBase64"
import logoIoma64 from "src/assets/logoIomaBase64"
import { getInitials } from "src/utils/patient/parseNames"

export async function createConstancyTemplate(mr: IMedicalRecord<Timestamp>, chosenName?: string) {
  const { timestamps, patient, provider } = mr

  const isIoma = patient.corporate_norm?.toUpperCase().includes("IOMA") || patient.obra_social?.toUpperCase().includes("IOMA")

  const isNotLaborJustification = [
    "POLICIA FEDERAL",
    "PODER JUDICIAL",
    "FATSA",
    "OSSEG",
    "DOSUBA",
  ].includes(patient.obra_social?.toUpperCase())

  const restOptions = ["24", "48", "72"]
  const justifiedRest = "justificado"
  const noRestOptions = ["no", "no justificado"]

  const getReposo = () => {
    const reposo = mr.mr?.reposo?.toLowerCase() ?? "no"
    let restText = ""
    const validRest = reposo && !(noRestOptions.includes(reposo))

    if (restOptions.includes(reposo)) {
      restText = `Se sugiere reposo de ${mr.mr.reposo} hs.`
    } else if (reposo.includes(justifiedRest)) {
      const splittedRest = reposo.split("//")
      const startDate = moment(splittedRest[1], "YYYY-MM-DD").format("DD/MM/YYYY")
      const endDate = moment(splittedRest[2], "YYYY-MM-DD").format("DD/MM/YYYY")
      restText = `Se sugiere reposo desde el ${startDate} hasta el ${endDate}.`
    } else if (reposo === "alta") {
      restText = "Se encuentra en condiciones de ALTA."
    }
    else restText = reposo

    if (validRest) {
      return restText
    }

    return null
  }

  const reposoString = getReposo()

  const utcTimestamp = timestamps?.dt_close?.toDate() || timestamps?.dt_assignation?.toDate() || timestamps?.dt_start?.toDate()
  let parsedDate = ""
  if (patient.country === "AR") {
    parsedDate = moment(utcTimestamp).tz("America/Argentina/Buenos_Aires").format("DD/MM/YYYY - HH:mm")
  } else {
    parsedDate = moment(utcTimestamp).tz("America/Mexico_City").format("DD/MM/YYYY - HH:mm")
  }

  let initialsName = ""
  if (chosenName) {
    initialsName = getInitials(patient.fullname)
  }

  const fullName = chosenName ? `${initialsName} - ${chosenName}` : patient.fullname
  const locationPrefix = patient.country === "AR" ? "Buenos Aires - " : ""

  const patientDNI = patient.dni ? patient.dni : "-"
  const providerFullName = provider.fullname ? provider.fullname : "-"
  const providerMatricula = provider.matricula ? provider.matricula : "-"

  const content = [
    {
      image: "logo",
      width: 100,
      style: "logo"
    },
    {
      text: "Constancia de atención",
      style: "header",
    },
    {
      text: fullName,
      style: "subheader",
    },
    {
      text: `${locationPrefix}${parsedDate} hs`,
      alignment: "left",
      margin: [50, 0],
      lineHeight: 1
    },
    {
      text: "A quién corresponda:",
      margin: [50, 26, 50, 0]
    },
    {
      text: `Se deja constancia de que el día ${parsedDate} hs, ${fullName} (DNI ${patientDNI}) recibió asistencia a través de nuestro servicio de consultas por el profesional de nuestro equipo médico ${providerFullName} - Matrícula ${providerMatricula}`,
      margin: [50, 0],
      alignment: "justify",
      lineHeight: 1
    },
    {
      columns: [
        {
          text: "Diagnóstico presuntivo: ",
          style: "b",
          width: "auto",
          margin: [0, 0, 5, 0]
        },
        {
          text: `${mr.mr.diagnostico ? mr.mr.diagnostico : "-"}.`,
          width: "auto"
        }
      ],
      margin: [50, 15, 50, 0]
    },
    {
      text: reposoString,
      margin: [50, 0]
    },
    {
      text: "Puede verificar la atención en el siguiente link:",
      margin: [50, 15, 50, 2]
    },
    {
      text: `https://umasalud.com/att/${mr.patient.uid}/${Buffer.from(mr.assignation_id).toString("base64")}`,
      link: `https://umasalud.com/att/${mr.patient.uid}/${Buffer.from(mr.assignation_id).toString("base64")}`,
      style: "link"
    },
    {
      qr: `https://umasalud.com/att/${mr.patient.uid}/${Buffer.from(mr.assignation_id).toString("base64")}`,
      style: "qr"
    },
    {
      text: "Constancia emitida según criterio profesional médico ante consulta online, su aceptación queda sujeta a la reglamentación y procedimientos del organismo o institución donde sea presentada.",
      style: "disclaimer"
    },
  ]

  const getFooterBody = () => {
    return isNotLaborJustification
      ? ([
        [
          {
            text: "EL PRESENTE NO CONSTITUYE JUSTIFICATIVO LABORAL",
            style: "laboralDisclaimer",
            fillColor: "#0a6dd7",
          },
        ],
        [
          {
            text: "Atención brindada por www.umasalud.com",
            style: "footer",
            fillColor: "#0a6dd7",
          },
        ],
      ])
      : ([
        [
          {
            text: "Atención brindada por www.umasalud.com",
            style: "footer",
            fillColor: "#0a6dd7",
          },
        ],
      ])
  }

  const footer = {
    table: {
      widths: ["*"],
      body: getFooterBody(),
    },
    layout: "noBorders",
  }

  const styles = {
    logo: {
      alignment: "center",
      margin: [0, 15, 0, 20],
    },
    header: {
      fontSize: 18,
      color: "#0a6dd7",
      alignment: "center",
      bold: true,
      margin: [0, 5, 0, 15],
    },
    subheader: {
      fontSize: 16,
      color: "#000",
      alignment: "center",
      bold: true,
      margin: [0, 0, 0, 0]
    },
    footer: {
      fontSize: 9,
      color: "#fff",
      alignment: "center",
      margin: [0, 8, 0, 2],
    },
    b: {
      bold: true,
    },
    link: {
      decoration: "underline",
      margin: [50, 0]
    },
    qr: {
      alignment: "center",
      margin: isNotLaborJustification ? [0, 75, 0, 75] : [0, 85, 0, 95]
    },
    disclaimer: {
      fontSize: 9,
      margin: [50, 0, 50, 0],
      alignment: "justify",
      italics: true,
      lineHeight: 1
    },
    laboralDisclaimer: {
      color: "#fff",
      alignment: "center",
      margin: [0, 6, 0, 1],
      bold: true,
    }
  }

  const images = {
    logo: isIoma ? logoIoma64 : logoUma64,
  }

  // Configuración del documento
  const docDefinition = {
    content,
    footer,
    styles,
    defaultStyle: {
      fontSize: 11,
      lineHeight: 1.5,
    },
    images,
    pageSize: "A4",
    pageMargins: isNotLaborJustification ? [30, 30, 30, 60] : [30, 30, 30, 30],
  } as unknown as TDocumentDefinitions

  return docDefinition
}
