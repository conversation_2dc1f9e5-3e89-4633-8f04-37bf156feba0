interface ICorporateMapping {
  searchName: string | ((country: string) => string)
  aliases: string[]
}

export const Corporates = {
  IOMA: "IOMA",
  UMA: "UMA",
  POLICIA_FEDERAL: "POLICIA_FEDERAL",
  PODER_JUDICIAL: "PODER_JUDICIAL"
} as const

export type TCorporates = typeof Corporates[ keyof typeof Corporates ]

export const CORPORATE_MAPPINGS: Record<TCorporates, ICorporateMapping> = {
  [ Corporates.IOMA ]: {
    searchName: "IOMA-APP",
    aliases: [ "IOMA" ]
  },
  [ Corporates.UMA ]: {
    searchName: (country: string) => `UMA ${country}`,
    aliases: [ "UMA" ]
  },
  [ Corporates.POLICIA_FEDERAL ]: {
    searchName: "POLICIA FEDERAL",
    aliases: [ "P<PERSON>", "POLICIA FEDERAL" ]
  },
  [ Corporates.PODER_JUDICIAL ]: {
    searchName: "PODER JUDICIAL",
    aliases: [ "PJ", "<PERSON><PERSON><PERSON> JUDICIAL" ]
  }
}
