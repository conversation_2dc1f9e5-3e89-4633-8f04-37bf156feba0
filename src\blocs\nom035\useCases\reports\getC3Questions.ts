import { base } from "src/utils/airtable/airtableConfiguration"

export const getC3Questions = async () => {
  const table = base("c3 preguntas")
  const c3Questions = await table.select({
    fields: ["num preg", "pregunta", "dimensiones", "max puntaje", "dominio", "categoria"]
  }).all()
  const c3 = c3Questions.map(record => {
    record.fields.id = record.id
    return record.fields
  })
  return c3
}