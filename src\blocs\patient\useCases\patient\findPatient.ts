import { PatientRepository } from "@umahealth/repositories"
import { IPatient } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"


export const FindPatientUseCase = async (uid: string): Promise<IPatient<Timestamp>> => {
  const patientExists = await PatientRepository.getByUid(uid)
  if(patientExists && typeof(patientExists) != "boolean") {
    return patientExists
  } else {
    throw new NotFoundException(`[ Patient | find ] => Patient not found with uid: ${uid}`)
  }
}
