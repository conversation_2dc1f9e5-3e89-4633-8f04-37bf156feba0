import { IHealthInsurance, IPatient } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { HealthInsuranceRepository, PatientRepository } from "@umahealth/repositories"
import { InternalServerErrorException, NotFoundException } from "@nestjs/common"

export const SetPrimaryUseCase = async (uid: string, coverageId:string): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>> | Partial<IPatient<Timestamp>>>[]> => {

  const updatedCoverages: IDocumentList<Partial<IHealthInsurance<Timestamp>>| Partial<IPatient<Timestamp>>>[] = []
  const coverages = await HealthInsuranceRepository.getAllByUid(uid)
  if(!coverages) throw new InternalServerErrorException(`[ Coverages | patient | Set primary ] Failed getting coverages - UID ${uid}`)

  const exists = coverages.filter(item => item.id === coverageId)
  if(exists.length === 0) throw new NotFoundException(`[ Coverages | patient | Set primary ] Coverage not found - UID ${uid} Coverage ${coverageId}`)

  coverages.map(async (coverage) => {
    // Si la cobertura es la seleccionada como primary, setea true
    if(coverage.id === coverageId){
      coverage["primary"] = true
      coverage["active"] = true
      const document = await HealthInsuranceRepository.update(uid, coverage.id, coverage)
      updatedCoverages.push(document)
    } else if(coverage?.primary === undefined || coverage?.primary === true){
      // si la cobertura no tiene param primary o si es la primary anterior, setea false.
      // No se le setea false a todo para evitar escrituras en la db innecesarias.
      coverage["primary"] = false
      const document = await HealthInsuranceRepository.update(uid, coverage.id, coverage)
      updatedCoverages.push(document)
    }
  })
  // Temporal hasta que se deje de usar corporate_norm
  const patient = await PatientRepository.getByUid(uid)
  const patientDocument = await PatientRepository.update(uid, {...patient, corporate_norm:coverageId})
  updatedCoverages.push(patientDocument)

  return updatedCoverages
}
