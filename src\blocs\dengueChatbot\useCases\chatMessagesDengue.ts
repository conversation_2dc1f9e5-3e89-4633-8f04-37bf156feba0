import { DengueChatbotMessage } from "@umahealth/sql-models"

export const chatMessages = async (conversation_id: number) => {
  const findMessages = await DengueChatbotMessage.findAll({
    where: {
      conversation_id
    },
    order: [["dt_create", "ASC"]]
  })

  if(findMessages.length > 0){
    const messages = findMessages.map(message => message?.dataValues)
    return messages
  }
  return findMessages
}
