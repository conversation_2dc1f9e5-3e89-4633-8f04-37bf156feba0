import { connection } from "src/utils/postgresConnection"
import { getCompanyIdFromAirtable, saveUserToAirtable } from "../../../utils/airtable"
import { IAuthUserResult, ICreateNom035User, INom035CorporatePosition, INom035Person, INom035PersonWithActive } from "../interfaces"
import { InternalServerErrorException, Logger } from "@nestjs/common"
import { Nom035Repository} from "@umahealth/repositories"
import { sendWelcomeEmail } from "../utils/sendWelcomeEmail"
import * as moment from "moment"
import nom035DTO from "../models/nom035_userPositionDTO"
import { authClient, getUserByEmail } from "@umahealth/auth"
import { generatePassword } from "../utils/functions"
import { sendUpdateCacheMessage } from "./cache/sendUpdateCacheMessage"
import { Transaction } from "sequelize"

async function createAuthUser(profile: INom035<PERSON><PERSON>): Promise<IAuthUserResult> {
  try {
    const authUser = await Nom035Repository.createUserInAuth({
      email: profile.email,
      password: generatePassword(),
      displayName: profile.fullname,
    })

    await Promise.all([
      authClient.setCustomUserClaims(authUser.uid, {
        requirePasswordChange: true
      }),
      sendWelcomeEmail({
        email: profile.email,
        fullname: profile.fullname,
        pass: profile.nationalId.toString()
      })
    ])

    return { uid: authUser.uid, success: true }
  } catch (error) {
    Logger.error(`[ Nom035 | Create User ] Error: ${error.message}`)
    return { uid: "", success: false }
  }
}

async function createDatabaseEntries(
  userProfile: INom035PersonWithActive,
  userPosition: INom035CorporatePosition,
  transaction: Transaction
): Promise<void> {
  const userInDB = await nom035DTO.createUser(userProfile, transaction)
  userPosition.userId = userInDB.id
  await nom035DTO.createPosition(userPosition, transaction)
}

export const createUser = async (
  user: Partial<ICreateNom035User>,
  corporateName: string
): Promise<Partial<ICreateNom035User>> => {
  const transaction = await connection().transaction()

  try {
    // Validación inicial
    const existingUser = await getUserByEmail(user.profile.email).catch(() => undefined)
    if (existingUser) {
      throw new InternalServerErrorException(`Usuario ya existe: ${user.profile.email}`)
    }

    // Usuario en Auth
    const authResult = await createAuthUser(user.profile)
    if (!authResult.success) {
      throw new InternalServerErrorException("Error en la creación del usuario en Auth")
    }

    const userProfile: INom035PersonWithActive = {
      ...user.profile,
      maritalStatus: "",
      active: true,
      uid: authResult.uid,
      phoneNumber: user.profile.phone,
    }
    delete userProfile.phone

    const userPosition: INom035CorporatePosition = {
      ...user.position,
      corporateId: corporateName
    }

    const [companyId] = await Promise.all([
      getCompanyIdFromAirtable("empresa", corporateName),
      createDatabaseEntries(userProfile, userPosition, transaction)
    ])

    await saveUserToAirtable("nomina", {
      nomina: user.profile.fullname,
      "INE / FM": user.profile.nationalId,
      "fecha nacimiento": moment(user.profile.birthDate, "YYYY-MM-DD").startOf("day").toISOString(),
      genero: user.profile.gender === "M" ? "Masculino" :
        user.profile.gender === "F" ? "Femenino" : "Sin especificar",
      correo: user.profile.email,
      telefono: user.profile.phone || "",
      area: user.position.area || "",
      "c trabajo": user.position.branch || "",
      seniority: user.position.seniority,
      empresa: [companyId],
      activo: true,
    })

    await transaction.commit()
    await sendUpdateCacheMessage(corporateName, "users")

    return user
  } catch (error) {
    await transaction.rollback()
    throw new InternalServerErrorException(
      `[ NOM035 | Create user ] Error en proceso de creación: ${error.message}`
    )
  }
}
