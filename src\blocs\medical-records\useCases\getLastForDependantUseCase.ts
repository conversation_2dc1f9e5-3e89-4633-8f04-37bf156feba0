import { attCategories, dependantUid } from "@umahealth/entities"
import { MedicalRecordRepository } from "@umahealth/repositories"

/**
 * Retrieves the latest medical record for a dependant based on a specific category.
 * @param uid user uid
 * @param dependantUid dependant uid
 * @param category The category of the medical record to filter
 * @returns The latest medical record in the specified category, or null if none is found.
 */
export async function getLastForDependantUseCase(uid: string, dependantUid: dependantUid, category: attCategories) {
  const mrs = await MedicalRecordRepository.getAllForDependant(uid, dependantUid)
  if (!mrs.length) return null
  const filteredMrs = mrs.filter((mr) => mr.att_category === category)
  if (!filteredMrs.length) return null
  const orderedMrs = filteredMrs.sort((a, b) =>
    b.timestamps.dt_create.toDate().getTime() - a.timestamps.dt_create.toDate().getTime()
  )
  return orderedMrs[0]
}