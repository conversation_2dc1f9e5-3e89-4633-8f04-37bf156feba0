
import { IMedicalRecord } from "@umahealth/entities"
import { MedicalRecordRepository } from "@umahealth/repositories"

export const createOnsiteUseCase = async (uid: string, assignationId: string, medicalRecord: IMedicalRecord) => {
  return await MedicalRecordRepository.create(uid, assignationId, {
    ...medicalRecord,
    mr: {
      ...medicalRecord.mr,
      alertas: "",
      destino_final: null,
      diagnostico: "",
      dt: null,
      dt_cierre: null,
      epicrisis: "",
      motivos_de_consulta: "",
      observaciones: null,
      ordenes: [],
      receta: [],
      receta_ref: null,
      reposo: null,
      tratamiento: null,
    },
  })
}
