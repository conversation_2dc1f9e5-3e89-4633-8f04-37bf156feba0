import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import * as moment from "moment"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { IValidation } from "../../coverage.bloc.interfaces"
import { parseValidObjectTimestamp } from "../../../../utils/parseValidTimestamp"

export const UpdateCoverageAfterValidationUseCase = async (uid: string, coverage: IHealthInsurance<Timestamp>, status: IValidation): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> => {

  const defaultTimestamps: IHealthInsurance<Timestamp>[ "timestamps" ] = {
    dt_create: Timestamp.fromDate(moment().utc().toDate()),
    dt_expire: Timestamp.fromDate(moment().utc().add(1, "month").toDate()),
    dt_last_validation: Timestamp.fromDate(moment().utc().toDate()),
  }

  // Validar los campos de coverage.timestamps
  const { timestamps: parsedTimestamps, isValid } = parseValidObjectTimestamp<IHealthInsurance<Timestamp>[ "timestamps" ]>(coverage.timestamps)
  // Construir el objeto timestamps asegurando los campos requeridos y su validez
  coverage.timestamps = {
    dt_create: isValid?.dt_create ? parsedTimestamps.dt_create : defaultTimestamps.dt_create,
    dt_expire: isValid?.dt_expire ? parsedTimestamps.dt_expire : defaultTimestamps.dt_expire,
    dt_last_validation: isValid?.dt_last_validation ? parsedTimestamps.dt_last_validation : defaultTimestamps.dt_last_validation,
  }

  if (!status.passValidation) {
    coverage.timestamps.dt_expire = Timestamp.fromDate(moment().utc().add(1, "month").toDate())
  }

  coverage.timestamps.dt_last_validation = Timestamp.fromDate(moment().utc().toDate())
  coverage.active = status.valid

  // Si no existe primary, seteo false como parámetro custom para que no fallen muchos otros flujos.
  // Si existe primary y está como true -> Si la OS es válida, lo mantengo como true, sino lo paso a false

  if (coverage.primary === undefined || (coverage.primary && !status.valid)) {
    coverage.primary = false
  }

  const document = await HealthInsuranceRepository.update(uid, coverage.id, { ...coverage, ...status.updatedData })
  if (!document) {
    throw new InternalServerErrorException(`[ ${UpdateCoverageAfterValidationUseCase.name} ] => Error updating coverage with uid: ${uid} and coverage: ${JSON.stringify(coverage)}`)
  }
  return document
}
