# 🔄 Guía de Desarrollo y Mejores Prácticas

Esta guía establece los estándares, flujos y mejores prácticas para el desarrollo en Megalith.

## 📋 Tabla de Contenidos

1. [Flu<PERSON> de Git](#flujo-de-git)
2. [Convenciones de Código](#convenciones-de-código)
3. [Arquitectura DDD](#arquitectura-ddd)
4. [Testing](#testing)
5. [Documentación](#documentación)
6. [Performance](#performance)
7. [Seguridad](#seguridad)

## 🌿 Flujo de Git

### Estructura de Ramas

```
main (producción)
├── next (staging)
├── beta (desarrollo)
└── feature/nueva-funcionalidad
```

### Flujo de Trabajo Completo

#### 1. Crear Nueva Funcionalidad

```bash
# 1. Actualizar main
git checkout main
git pull origin main

# 2. Crear rama de feature
git checkout -b feature/AGS-123-nueva-funcionalidad

# 3. Desarrollo
# ... hacer cambios ...

# 4. Commits siguiendo conventional commits
git add .
git commit -m "feat(appointments): agregar cancelación de citas"

# 5. Push de la rama
git push origin feature/AGS-123-nueva-funcionalidad
```

#### 2. Pull Requests

```bash
# Orden de PRs:
# 1. feature/AGS-123-nueva-funcionalidad → beta (desarrollo)
# 2. feature/AGS-123-nueva-funcionalidad → next (staging)  
# 3. next → main (producción)

# ⚠️ NUNCA crear PRs desde beta a next o main
```

#### 3. Conventional Commits

```bash
# Formato: tipo(scope): descripción

# Tipos permitidos:
feat:     # Nueva funcionalidad
fix:      # Corrección de bug
docs:     # Cambios en documentación
style:    # Cambios de formato (no afectan lógica)
refactor: # Refactoring de código
test:     # Agregar o modificar tests
chore:    # Tareas de mantenimiento

# Ejemplos:
git commit -m "feat(appointments): implementar cancelación de citas"
git commit -m "fix(payments): corregir cálculo de descuentos"
git commit -m "docs(api): actualizar documentación de endpoints"
git commit -m "test(patients): agregar tests para validación de DNI"
```

#### 4. Changesets para Versionado

```bash
# Generar changeset antes del merge a main
npx changeset

# Seleccionar tipo de cambio:
# patch: 1.0.0 → 1.0.1 (bug fixes)
# minor: 1.0.0 → 1.1.0 (nuevas features)
# major: 1.0.0 → 2.0.0 (breaking changes)

# Escribir descripción clara del cambio
# Incluir en el commit
git add .changeset/
git commit -m "chore: add changeset for appointment cancellation"
```

## 📝 Convenciones de Código

### Nomenclatura

```typescript
// Archivos y carpetas
kebab-case: appointment-controller.ts, medical-records/

// Clases
PascalCase: AppointmentController, PatientService, MedicalRecordsBloc

// Interfaces
PascalCase con I: IAppointment, IPatientData, ICreateAppointmentRequest

// Types
PascalCase con T: TSpecialties, TAppointmentStatus

// Enums
PascalCase: AppointmentStates, PaymentMethods

// Constantes
UPPER_SNAKE_CASE: MAX_APPOINTMENTS_PER_DAY, DEFAULT_TIMEOUT

// Variables y funciones
camelCase: createAppointment, patientData, appointmentId

// Casos de uso
camelCase + UseCase: createAppointmentUseCase, cancelAppointmentUseCase
```

### Estructura de Archivos

```typescript
// Controladores
@Controller('appointments')
export class AppointmentsController {
  constructor(private readonly appointmentsBloc: AppointmentsBloc) {}

  @Post()
  @UsePipes(new JoiValidationPipe(createAppointmentSchema))
  async createAppointment(@Body() data: ICreateAppointmentRequest) {
    return await this.appointmentsBloc.create(data)
  }
}

// Casos de uso
export async function createAppointmentUseCase(
  data: ICreateAppointmentRequest
): Promise<IAppointment> {
  // 1. Validaciones
  // 2. Lógica de negocio
  // 3. Persistencia
  // 4. Eventos
}

// Interfaces
export interface ICreateAppointmentRequest {
  patientId: string
  providerId: string
  scheduledAt: Date
  specialty: TSpecialties
}

// Schemas de validación
export const createAppointmentSchema = Joi.object({
  patientId: Joi.string().required(),
  providerId: Joi.string().required(),
  scheduledAt: Joi.date().iso().required(),
  specialty: Joi.string().valid(...specialtiesArray).required()
})
```

## 🏗️ Arquitectura DDD

### Principios Fundamentales

#### 1. Separación de Responsabilidades

```typescript
// ✅ CORRECTO: Lógica en casos de uso
export async function createAppointmentUseCase(data: ICreateAppointmentRequest) {
  // Validaciones de dominio
  if (!isValidAppointmentTime(data.scheduledAt)) {
    throw new BadRequestException('Horario inválido')
  }
  
  // Lógica de negocio
  const appointment = await AppointmentRepository.create(data)
  
  // Eventos de dominio
  await EventEmitter.emit('appointment.created', appointment)
  
  return appointment
}

// ❌ INCORRECTO: Lógica en controladores
@Post()
async createAppointment(@Body() data: any) {
  // NO poner lógica de negocio aquí
  const appointment = await this.repository.save(data)
  return appointment
}
```

#### 2. Dependencias hacia el Dominio

```typescript
// ✅ CORRECTO: Dependencias apuntan hacia el dominio
// Controller → Bloc → UseCase → Repository

// ❌ INCORRECTO: Dependencias circulares o hacia infraestructura
```

#### 3. Inmutabilidad y Validaciones

```typescript
// ✅ CORRECTO: Validaciones explícitas
export async function updateAppointmentUseCase(
  appointmentId: string,
  updates: Partial<IAppointment>
): Promise<IAppointment> {
  const appointment = await AppointmentRepository.getById(appointmentId)
  
  if (!appointment) {
    throw new NotFoundException('Cita no encontrada')
  }
  
  if (appointment.state === AppointmentStates.COMPLETED) {
    throw new BadRequestException('No se puede modificar una cita completada')
  }
  
  return await AppointmentRepository.update(appointmentId, updates)
}
```

### Patrones de Implementación

#### Patrón Repository

```typescript
// Abstracción del repositorio
export interface IAppointmentRepository {
  create(data: ICreateAppointmentRequest): Promise<IAppointment>
  getById(id: string): Promise<IAppointment | null>
  update(id: string, data: Partial<IAppointment>): Promise<IAppointment>
  delete(id: string): Promise<void>
}

// Implementación específica (Firestore, PostgreSQL, etc.)
export class FirestoreAppointmentRepository implements IAppointmentRepository {
  async create(data: ICreateAppointmentRequest): Promise<IAppointment> {
    // Implementación específica de Firestore
  }
}
```

#### Patrón Event Sourcing

```typescript
// Eventos de dominio
export interface IDomainEvent {
  eventType: string
  aggregateId: string
  timestamp: Date
  data: any
}

// Emisión de eventos
await EventEmitter.emit('appointment.created', {
  eventType: 'AppointmentCreated',
  aggregateId: appointment.id,
  timestamp: new Date(),
  data: appointment
})
```

## 🧪 Testing

### Estructura de Tests

```typescript
// Tests de casos de uso
describe('CreateAppointmentUseCase', () => {
  beforeEach(() => {
    // Setup mocks
    jest.clearAllMocks()
  })

  describe('when data is valid', () => {
    it('should create appointment successfully', async () => {
      // Arrange
      const appointmentData = {
        patientId: 'patient-123',
        providerId: 'provider-456',
        scheduledAt: new Date('2024-12-01T10:00:00Z'),
        specialty: 'cardiology'
      }
      
      const expectedAppointment = { id: 'apt-789', ...appointmentData }
      AppointmentRepository.create = jest.fn().mockResolvedValue(expectedAppointment)
      
      // Act
      const result = await createAppointmentUseCase(appointmentData)
      
      // Assert
      expect(result).toEqual(expectedAppointment)
      expect(AppointmentRepository.create).toHaveBeenCalledWith(appointmentData)
    })
  })

  describe('when data is invalid', () => {
    it('should throw error for past appointment time', async () => {
      // Arrange
      const invalidData = {
        patientId: 'patient-123',
        providerId: 'provider-456',
        scheduledAt: new Date('2020-01-01T10:00:00Z'), // Fecha pasada
        specialty: 'cardiology'
      }
      
      // Act & Assert
      await expect(createAppointmentUseCase(invalidData))
        .rejects
        .toThrow('Horario inválido')
    })
  })
})
```

### Mocking de Dependencias

```typescript
// Mock de repositorios
jest.mock('@umahealth/repositories', () => ({
  AppointmentRepository: {
    create: jest.fn(),
    getById: jest.fn(),
    update: jest.fn(),
    delete: jest.fn()
  }
}))

// Mock de servicios externos
jest.mock('../providers/external-service', () => ({
  ExternalService: {
    syncData: jest.fn().mockResolvedValue({ success: true })
  }
}))
```

### Coverage Requirements

```json
// jest.config.js
{
  "coverageThreshold": {
    "global": {
      "branches": 80,
      "functions": 80,
      "lines": 80,
      "statements": 80
    }
  }
}
```

## 📚 Documentación

### Documentación de APIs

```typescript
// Swagger/OpenAPI
@ApiTags('appointments')
@Controller('appointments')
export class AppointmentsController {
  
  @Post()
  @ApiOperation({ summary: 'Crear nueva cita médica' })
  @ApiResponse({ status: 201, description: 'Cita creada exitosamente', type: AppointmentDto })
  @ApiResponse({ status: 400, description: 'Datos inválidos' })
  @ApiResponse({ status: 409, description: 'Conflicto de horario' })
  async createAppointment(@Body() data: CreateAppointmentDto) {
    return await this.appointmentsBloc.create(data)
  }
}
```

### Comentarios en Código

```typescript
/**
 * Crea una nueva cita médica validando disponibilidad y restricciones de negocio
 * 
 * @param data - Datos de la cita a crear
 * @returns Promise<IAppointment> - La cita creada
 * @throws BadRequestException - Cuando los datos son inválidos
 * @throws ConflictException - Cuando hay conflicto de horario
 * 
 * @example
 * ```typescript
 * const appointment = await createAppointmentUseCase({
 *   patientId: 'patient-123',
 *   providerId: 'provider-456',
 *   scheduledAt: new Date('2024-12-01T10:00:00Z'),
 *   specialty: 'cardiology'
 * })
 * ```
 */
export async function createAppointmentUseCase(
  data: ICreateAppointmentRequest
): Promise<IAppointment> {
  // Implementación...
}
```

## ⚡ Performance

### Optimizaciones de Base de Datos

```typescript
// ✅ CORRECTO: Usar índices y consultas eficientes
const appointments = await AppointmentRepository.findByDateRange(
  startDate,
  endDate,
  { 
    index: 'scheduledAt',
    limit: 100,
    select: ['id', 'patientId', 'scheduledAt'] // Solo campos necesarios
  }
)

// ❌ INCORRECTO: Consultas sin optimizar
const allAppointments = await AppointmentRepository.findAll()
const filtered = allAppointments.filter(apt => apt.scheduledAt >= startDate)
```

### Caching

```typescript
// Redis para cache
@Injectable()
export class AppointmentService {
  constructor(private readonly redis: Redis) {}

  async getAppointment(id: string): Promise<IAppointment> {
    // Intentar obtener del cache
    const cached = await this.redis.get(`appointment:${id}`)
    if (cached) {
      return JSON.parse(cached)
    }

    // Si no está en cache, obtener de BD
    const appointment = await AppointmentRepository.getById(id)
    
    // Guardar en cache por 1 hora
    await this.redis.setex(`appointment:${id}`, 3600, JSON.stringify(appointment))
    
    return appointment
  }
}
```

## 🔒 Seguridad

### Validación de Entrada

```typescript
// ✅ CORRECTO: Validación exhaustiva
export const createAppointmentSchema = Joi.object({
  patientId: Joi.string().uuid().required(),
  providerId: Joi.string().uuid().required(),
  scheduledAt: Joi.date().iso().min('now').required(),
  specialty: Joi.string().valid(...specialtiesArray).required(),
  notes: Joi.string().max(1000).optional()
}).required()
```

### Autorización

```typescript
// Guards para proteger endpoints
@UseGuards(AuthGuard, RoleGuard)
@Roles('doctor', 'admin')
@Get(':id')
async getAppointment(@Param('id') id: string, @Request() req: any) {
  // Verificar que el usuario tiene acceso a esta cita
  await this.authorizationService.checkAppointmentAccess(req.user, id)
  return await this.appointmentsBloc.getById(id)
}
```

### Sanitización

```typescript
// Sanitizar datos de entrada
import { sanitize } from 'class-sanitizer'

@Post()
async createAppointment(@Body() data: CreateAppointmentDto) {
  const sanitizedData = sanitize(data)
  return await this.appointmentsBloc.create(sanitizedData)
}
```

---

Esta guía debe seguirse consistentemente en todo el proyecto para mantener la calidad y coherencia del código. 🚀
