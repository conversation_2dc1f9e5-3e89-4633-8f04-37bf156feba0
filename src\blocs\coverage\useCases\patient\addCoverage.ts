import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"


export const AddCoverageUseCase = async(uid: string, coverage:IHealthInsurance<Timestamp>): Promise<IDocumentList<IHealthInsurance<Timestamp>>>  => {
  const document = await HealthInsuranceRepository.create(uid, coverage)
  if(!document) throw new InternalServerErrorException(`[ Coverages | patient | AddCoverage ] Error creating coverage with data: ${JSON.stringify(coverage)}`)
  return document
}

