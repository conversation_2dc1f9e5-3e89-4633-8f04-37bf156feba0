import { IDocument<PERSON><PERSON>, PatientRepository } from "@umahealth/repositories"
import { IPatient } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { ConflictException, Logger, NotFoundException } from "@nestjs/common"
import { dniErrorMsg, wsErrorMsg } from "../../utils/errorsMsg"
import { cleanupName } from "src/utils/patient/parseNames"
import { isNumericEmail } from "../../utils/emailValidation"

/**
 * Completa los datos de un paciente durante el flujo de registro
 * @param uid uid del patient a completar
 * @param data data a completar
 * @returns IDocumentList<Partial<IPatient<Timestamp>>>
 */

export const RegisterPatientUseCase = async (uid: string, data: Partial<IPatient<Timestamp>>): Promise<IDocumentList<Partial<IPatient<Timestamp>>>> => {
  const patient = await PatientRepository.getByUid(uid)
  if (!patient) throw new NotFoundException(`[ Patient | registerPatient ] Patient not found - UID ${uid}`)

  // Sacamos los espacios extra que pueda haber en el nombre dado
  if (data.fullname) {
    data.fullname = cleanupName(data.fullname)
  } else if (patient.fullname) {
    data.fullname = cleanupName(patient.fullname)
  }
  // Sacamos los espacios extra que pueda haber en el nombre escogido
  if (data.chosenName) {
    data.chosenName = cleanupName(data.chosenName)
  } else if (patient.chosenName) {
    data.chosenName = cleanupName(patient.chosenName)
  }

  // checkeamos que el numero de telefono y el dni que esta introduciendo el usuario
  // no coincidan con un usuario ya existente en la base de datos
  let wsConflictMsg = ""
  let dniConflictMsg = ""
  let existingUser: IPatient<Timestamp>[] = []

  if (data.ws) {
    existingUser = await PatientRepository.getPatientByWs(data.ws)
    if (existingUser.length && existingUser[0].id !== patient.id && patient.verified === false) wsConflictMsg = wsErrorMsg
  }

  // Solo validamos DNI si no es un email numérico
  if (data.dni && !isNumericEmail(data.email || patient.email)) {
    existingUser = await PatientRepository.getPatientByDni(data.dni)
    if (existingUser.length && existingUser[0].id !== patient.id && patient.verified === false) dniConflictMsg = dniErrorMsg
  }

  if (existingUser.length && (wsConflictMsg || dniConflictMsg)) {
    Logger.error(`${wsConflictMsg} - ${dniConflictMsg}, {dni: ${data.dni}, ws: ${data.ws}, originalId: ${uid}, foundId: ${existingUser[0].id}}`)
    Logger.error(`originalId: ${uid}, foundId: ${existingUser[0].id}`)
    throw new ConflictException(`${wsConflictMsg} - ${dniConflictMsg}`)
  }

  const document = await PatientRepository.update(uid, { ...patient, ...data })

  return document
}
