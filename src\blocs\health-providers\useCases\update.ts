import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { IProvider } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"

export async function updateProvider(providerId: string, data: Partial<IProvider<Timestamp>>) {
  const updatedProvider = await ProviderRepository.update(providerId, data)
  if(!updatedProvider) throw new InternalServerErrorException("[ Provider | updateProvider ] => could not update provider")
  return updatedProvider
}

