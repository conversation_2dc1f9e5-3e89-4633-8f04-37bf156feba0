import {
  IDocumentList,
  MedicalRecordRepository,
} from "@umahealth/repositories"
import {
  IMedicalRecord,
  MedicalRecord,
  medicalRecordMr,
  medicalRecordPatient,
  medicalRecordPreds,
  paymentData,
} from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { IMakeOnsiteFarmatodoAppointmentData } from "src/patient-app/onsite/onsite.interface"

export const createFarmatodoMedicalRecordUseCase = async ({
  body: {
    assignation_id,
    doctorUid,
    event_type,
    motivos_de_consulta,
    uid,
  },
  patient: {
    address,
    country,
    dni,
    dob,
    fullname,
    affiliate_number,
    corporate_norm,
    sex,
    ws,
  },
  provider: {
    cuit,
    fullname: providerFullname,
    matricula_especialidad,
    uid: providerUid,
    ws: providerWs,
  },
  appointment: {
    timestamps: {
      dt_assignation
    }
  },
  payment_data,
}: IMakeOnsiteFarmatodoAppointmentData): Promise<IDocumentList<IMedicalRecord<Timestamp>>> => {
  const dt_create = new Date()

  const mrPatient: medicalRecordPatient = {
    address: address || "",
    antecedentes: "",
    country,
    dependant_uid: false,
    dni,
    dob: dob || "",
    fullname,
    n_afiliado: affiliate_number || "",
    obra_social: corporate_norm,
    sex,
    uid,
    ws,
  }

  const mrProvider = {
    cuit,
    especialidad: matricula_especialidad || "online_clinica_medica",
    fullname: providerFullname,
    uid: doctorUid || providerUid,
    ws: providerWs,
  }

  const mr_preds: medicalRecordPreds = {
    abort_description: "",
    destino_final: "",
    diagnostico: "",
    epicrisis: "",
    gduh: "",
    motivos_de_consulta,
    observaciones: "",
    pre_clasif: ""
  }

  const mr: medicalRecordMr  = {
    alertas: null,
    destino_final: "",
    diagnostico: "",
    dt: null,
    dt_cierre: null,
    epicrisis: "",
    motivos_de_consulta,
    observaciones: null,
    ordenes: [],
    receta: [],
    reposo: null,
    receta_ref: null,
    specialist_referral: null,
    tratamiento: null,
  }

  const medicalRecord = new MedicalRecord<Timestamp>({
    affiliateNumber: affiliate_number,
    affiliateType: "",
    assignation_id: assignation_id,
    att_category: "MI_ESPECIALISTA",
    especialidad: mrProvider.especialidad || "online_clinica_medica",
    event_type: event_type as "online" | "onsite",
    incidente_id: assignation_id,
    mr_preds,
    mr,
    patient: mrPatient,
    payment_data: {
      price: payment_data.price,
      full_price: payment_data.full_price,
      method: payment_data.method,
      trueMethod: (payment_data as unknown as paymentData).trueMethod,
      currency: "",
      id: "",
    },
    provider: mrProvider,
    timestamps: {
      dt_assignation: Timestamp.fromDate(dt_assignation.toDate()),
      dt_booking: Timestamp.fromDate(dt_create),
      dt_create: Timestamp.fromDate(dt_create),
    }
  })

  return await MedicalRecordRepository.create(uid, assignation_id, medicalRecord)
}

