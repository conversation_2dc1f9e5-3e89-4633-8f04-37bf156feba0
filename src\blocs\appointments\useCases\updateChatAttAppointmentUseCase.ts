import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { countries, IChatAttAppointment} from "@umahealth/entities"
import { AppointmentRepository, IDocumentList } from "@umahealth/repositories"

export const updateChatAttAppointment = async (
  assignationId: string,
  country: countries,
  appointment: Partial<IChatAttAppointment<Timestamp>>
): Promise<IDocumentList<Partial<IChatAttAppointment<Timestamp>>>> => {
  const updatedChatAttAppointment = await AppointmentRepository.update("chatAtt",country,assignationId,appointment)

  if(!updatedChatAttAppointment) throw new InternalServerErrorException(`[ Appointments | updateChatAttAppointment ] => Error updating chat attention appointment. Id: ${assignationId}`)

  return updatedChatAttAppointment
}