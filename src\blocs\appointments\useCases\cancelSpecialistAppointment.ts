import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { appointmentServices, countries, IAppointment, IOnlineAppointment, action, IOnSiteAppointment } from "@umahealth/entities"
import { appointmentCancelMotive } from "@umahealth/entities/src/entities/appointments/types"
import { IDocumentList } from "@umahealth/firestore"
import { AppointmentRepository, RequestRepository } from "@umahealth/repositories"
import { isBefore } from "date-fns"

export async function cancelSpecialistAppointment(
  service: appointmentServices,
  country: countries,
  assignationId: string,
  action?: action,
  cancelMotive?: appointmentCancelMotive
): Promise<IDocumentList<Partial<IAppointment<Timestamp>>>> {
  let request
  let adminActions
  let appointment
  const cancelTime = new Date()

  if (service === "consultorio") {
    request = await RequestRepository.getOnsite(assignationId)
    if (request["adminActions"]) {
      adminActions = [...request["adminActions"], action]
    } else {
      adminActions = [action]
    }
    appointment = await AppointmentRepository.getByAssignationId<IOnSiteAppointment<Timestamp>>(service, country, assignationId)
    if (!appointment) {
      throw new NotFoundException(`Appointment not found. assignationId: ${assignationId} service: ${service} country: ${country}.`)
    }
    appointment.adminActions = adminActions
    appointment.state = "FREE"
    appointment.destino_final = cancelMotive? `${cancelMotive} CANCEL` : "USER CANCEL"
  }
  else {
    appointment = await AppointmentRepository.getByAssignationId<IOnlineAppointment<Timestamp>>(service, country, assignationId)
    if (!appointment) {
      throw new NotFoundException(`Appointment not found. assignationId: ${assignationId} service: ${service} country: ${country}.`)
    }
    appointment.state = "FREE"

    if (cancelMotive === "PORTAL") {
      appointment.destino_final = "PORTAL CANCEL"
      appointment.cancel_origin = "portal"
    }
    else if (cancelMotive === "DELAY") {
      appointment.destino_final = "DELAY CANCEL"
      appointment.cancel_origin = "patient"
      appointment.state = "DELAY_CANCEL"
    }
    else {
      appointment.destino_final = "USER CANCEL"
      appointment.cancel_origin = "patient"
    }
  }

  /**
   * If the appointment has already passed, keep the patient info.
   * If it hasn't, clear it so another patient can take the slot.
   */
  const now = new Date()
  const assignationDate = appointment.timestamps.dt_assignation.toDate()

  const hasAppointmentPassed = isBefore(assignationDate, now)

  if (!hasAppointmentPassed) {
    appointment.patient = {}
  }

  appointment.timestamps.dt_cancel = Timestamp.fromDate(cancelTime)

  return await AppointmentRepository.update<IOnSiteAppointment<Timestamp> | IOnlineAppointment<Timestamp>>(service, country, assignationId, appointment)

}
