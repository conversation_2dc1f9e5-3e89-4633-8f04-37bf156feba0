import * as sharp from "sharp"
import { promises as fsPromises } from "fs"
import { join } from "path"

/**
* Procesa y optimiza la imagen del logo corporativo
* @param file - Archivo de imagen subido (Multer File)
* @param corporateName - Nombre de la Obra Social
* @returns Objeto con las rutas temporales y de almacenamiento
* @throws {Error} Si falla la creación del directorio temporal o el procesamiento de la imagen
*/
export async function processLogoImageUseCase(
  file: Express.Multer.File,
  corporateName: string
) {
  const tempDir = join(process.cwd(), "temp")
  await fsPromises.mkdir(tempDir, { recursive: true })

  const normalizedCorporateName = corporateName.split(" ").join("-").toUpperCase()
  const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1E9)
  const filename = `${uniqueSuffix}-${file.originalname}`
  const tempFilePath = join(tempDir, filename)
  const storagePath = `corporateLogos/${normalizedCorporateName}-${filename}`

  await sharp(file.buffer)
    .resize({
      fit: "contain",
      background: "#FFF",
      width: 300,
      height: 300,
    })
    .toFile(tempFilePath)

  return { tempFilePath, storagePath }
}
