import { action, countries, IOnSiteAppointment, IOnSitePatient, practice, appointmentServices, appointmentStates, EventTypes } from "@umahealth/entities"


export interface IReceivedOnSiteAppointment {
  action: action,
  assignationId: string,
  country: countries,
  especialidad: string,
  patient: IOnSitePatient,
  practices: Array<practice>,
  service: string,
  uid: string,
}

export interface ICancelAppointment {
  assignationId: string
  country: countries
  uid: string
}

export interface IReceiveAppointment {
  assignationId: string
  appointment: IOnSiteAppointment
  country: countries
}

export interface specificAppointmentForPatientAndProviderBody {
  patientUid: string
  providerUid: string
}

export interface IUpdateRestHours {
  assignationId: string
  restHours: string
  uid: string
  zendeskTicket: string
}

export interface IGetAppointmentLogsQuery {
  assignationId: string
  patientUid: string
  providerUid: string
  service: "consultorio" | "online" | "bag"
  country: countries
  useNewLogs?: string
}

export interface IGetNewLogsQuery {
  assignationId: string
  patientUid: string
  providerUid: string
  service: "consultorio" | "online" | "bag"
  country: countries
}

export interface IConnectionPeriod {
  connected_at: Date
  disconnected_at: Date | null
  duration: {
    minutes: number
    seconds: number
  }
}

export interface IPrescriptionAttempt {
  timestamp: Date
  validator: string
  drugNames: string[]
  diagnosis: string
  error_code: string
  error_message: string
  patient: {
    fullname: string
    dni: string
    uid: string
  }
}

export type ParticipantConnectionStatus = "connected" | "disconnected" | "unknown"

export interface IJoinRoomEvent {
  event: EventTypes
  timestamp: Date
}

export interface IParticipantLogs {
  connection_history: IConnectionPeriod[]
  current_status: ParticipantConnectionStatus
  total_time_connected: {
    minutes: number
    seconds: number
  }
  prescription_history?: IPrescriptionAttempt[]
  join_room_history?: IJoinRoomEvent[]
  info?: string
}

export interface ITechnicalIssue {
  actor: "patient" | "provider"
  description: string
  end_time: Date
  start_time: Date
}

export type EventMap = {
  [key: string]: ITechnicalIssue
}

export type AppointmentConnectionStatus = "failed" | "in_progress" | "successful"

export interface IAppointmentLogsAnalysis {
  analysis: {
    appointment_status: AppointmentConnectionStatus
    summary: string
    details: string[]
  }
  assignation_id: string
  patient: IParticipantLogs
  provider: IParticipantLogs
  simultaneous_connection: {
    minutes: number
    seconds: number
  }
  technical_issues: ITechnicalIssue[]
}

