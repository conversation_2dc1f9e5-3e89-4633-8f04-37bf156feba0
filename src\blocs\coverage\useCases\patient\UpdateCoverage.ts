import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"



export const UpdateCoverageUseCase =  async(uid: string, coverageId:string, data:IHealthInsurance<Timestamp> ): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> => {

  const document = await HealthInsuranceRepository.update(uid, coverageId, data)
  if(!document) throw new InternalServerErrorException(`[ Coverages | patient | update ] Error updating coverage with uid: ${uid} & data: ${JSON.stringify(data)}`)
  return document

}
