import { Injectable } from "@nestjs/common"
import {  reportTechnicalProblems } from "./useCases/reportTecnicalProblem"
import { ITecnicalProblem } from "src/doctor-app/online/appointments.interfaces"

@Injectable()
export class JiraBloc {
  async reportTechnicalProblems(problems: ITecnicalProblem, assignationId: string, providerUid: string) {
    return await reportTechnicalProblems(problems, assignationId, providerUid)
  }
}
