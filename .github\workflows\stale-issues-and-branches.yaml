name: '<PERSON><PERSON> issues, PR, branches'
on:
  schedule:
    - cron: '0 12 * * 1-5'
  workflow_dispatch:

jobs:
  stale_issues_and_branches:
    permissions:
      issues: write
      pull-requests: write
      contents: write
    secrets: inherit
    uses: umahealth/ci-workflows/.github/workflows/stale-branches-and-issues.yaml@main
    with:
      issues-days-before-stale: 30
      issues-days-before-close: 7
      branches-days-before-stale: 60
      branches-days-before-delete: 90
      branches-max-issues: 40
