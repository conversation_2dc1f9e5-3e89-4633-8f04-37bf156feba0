import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, Logger } from "@nestjs/common"
import { ISpecialtyNotificationEvent } from "@umahealth/entities"
import { IDocumentList, NotificationRepository } from "@umahealth/repositories"


export async function createSpecialtyNotificationEventBatch(notificationData: ISpecialtyNotificationEvent<Timestamp>): Promise<IDocumentList<ISpecialtyNotificationEvent<Timestamp>>> {
  try {
    return await NotificationRepository.createSpecialtyNotificationEventBatch(notificationData)
  } catch(error) {
    Logger.error(`[ createSpecialtyNotificationEventBatch ] - Error creating notification - error: ${error}`)
    throw new InternalServerErrorException("createSpecialtyNotificationEventBatch")
  }
}