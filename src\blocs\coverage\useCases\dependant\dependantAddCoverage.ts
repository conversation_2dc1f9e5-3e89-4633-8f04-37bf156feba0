import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"


export const DependantAddCoverageUseCase = async (uid: string, coverage:IHealthInsurance<Timestamp>): Promise<IDocumentList<IHealthInsurance<Timestamp>>> => {

  const document = await HealthInsuranceRepository.dependantCreate(uid, coverage)
  if(!document) throw new InternalServerErrorException(`[ Coverages | dependant | create ] Error creating dependant coverage with uid: ${uid} & coverage: ${JSON.stringify(coverage)}`)
  return document
}

