import { Timestamp } from "@google-cloud/firestore"
import { IHealthInsurance, IPatient } from "@umahealth/entities"
import { DependantRepository, HealthInsuranceRepository, PatientRepository, saveBatch } from "@umahealth/repositories"
import { getIomaPatient, getIomaPatientByNumAfi } from "src/blocs/coverage/apiValidations/patients/IOMA"
import { Logger } from "@nestjs/common"
import {  IPatientIomaGetAfiSex } from "src/blocs/coverage/apiValidations/interfaces"
import { sexConvertion, sexConvertionToString } from "src/blocs/coverage/utils/functions"
import * as moment from "moment"

const affiliateNumberRegex = /^[A-Za-z0-9]{12}$/
const dniRegex = /^[\d]{1,3}\.?[\d]{3,3}\.?[\d]{3,3}$/
const sexStringRegex = /^[MFX]$/

export const updatePersonalData = async(patient: IPatient<Timestamp>, isDependant: boolean, iomaApiData: IPatientIomaGetAfiSex) => {
  try {
    const { dni, sex, dob } = patient
    const realDni = iomaApiData.NumeroDocumento.toString()
    const realSex = sexConvertionToString(iomaApiData.Sexo)
    const realDob = moment(iomaApiData?.FechaNacimiento?.replaceAll("\/", "/"), "DD/MM/YYYY").format("YYYY-MM-DD")
    if (dni === realDni && sex === realSex && dob === realDob) {
      return // No es necesario actualizar
    }
    if (isDependant) {
      const updateDependant = await DependantRepository.doublePathUpdate(patient.core_id, patient.id, { sex: realSex, dni: realDni, dob: realDob })
      await saveBatch(updateDependant)
    } else {
      await PatientRepository.updateDocument(patient.id, { sex: realSex, dni: realDni, dob: realDob })
    }
  } catch (error) {
    Logger.error(`Error en la función updateDniAndSex: ${error}. uid ${patient.id}`)
  }
}

export const updateAffiliationData = async(coverageData: IHealthInsurance<Timestamp>, patient: IPatient<Timestamp>, isDependant: boolean, iomaApiData: IPatientIomaGetAfiSex ) => {
  try {
    coverageData.affiliate_id = iomaApiData.NumeroAfiliado
    coverageData.affiliateType = iomaApiData.TipoAfiliatorio
    if (isDependant) {
      await HealthInsuranceRepository.dependantUpdateDocument(patient.id, coverageData.id, { ...coverageData })
    } else {
      await HealthInsuranceRepository.updateDocument(patient.id, coverageData.id, { ...coverageData })
    }
  } catch (error) {
    Logger.error(`Error en la función updateAffiliateNumber: ${error}. uid ${patient.id}`)
  }
}

export const revalidatePatient = async (coverageData: IHealthInsurance<Timestamp>, patient: IPatient<Timestamp>, isDependant: boolean, iomaAfiData: IPatientIomaGetAfiSex) => {
  if(iomaAfiData){
    await updatePersonalData(patient, isDependant, iomaAfiData)
    await updateAffiliationData(coverageData, patient, isDependant, iomaAfiData)
  }
}


export const runIomaValidation = async (corporate: IHealthInsurance<Timestamp>, patient: IPatient<Timestamp>, isDependant: boolean): Promise<boolean> => {
  const {Username, Password} = JSON.parse(process.env.IOMA_AUTH)
  let patientIoma:IPatientIomaGetAfiSex
  let response = false

  if((!dniRegex.test(patient.dni) || !sexStringRegex.test(patient.sex)) && affiliateNumberRegex.test(corporate.affiliate_id)){
    // Si el dni o sexo no son correctos y el numero de afiliado es correcto, obtenemos la data usando este último

    patientIoma = await getIomaPatientByNumAfi(Username, Password, corporate.affiliate_id)
    Logger.log(`[runIomaValidation] affiliate_id ${corporate.affiliate_id}, patientIoma: ${JSON.stringify(patientIoma)}`)
    // Hago esto porque el api de ioma siempre tira 200 y en caso de error te lo manda en el body de la respuesta
    response  = !["4","5","20"].includes(patientIoma.CodigoMensaje)
    // Si falló la primera condición o no se pudo validar usando número de afiliado pruebo con dni
    if (!response) {
      // Si el DNI y el sexo son correctos, intentamos con esos datos
      patientIoma = await getIomaPatient(
        Username,
        Password,
        patient.dni,
        sexConvertion(patient.sex),
        corporate.affiliate_id
      )
      Logger.log(`[runIomaValidation] patient dni ${patient.dni}, sex: ${sexConvertion(patient.sex)}, patientIoma by DNI: ${JSON.stringify(patientIoma)}`)

      // Verificamos si la respuesta es válida (la API devuelve errores en el body)
      response = !["4", "5", "20"].includes(patientIoma.CodigoMensaje)
    }
  } else {
    // Si el dni y el sexo son correctos, obtenemos la data usando estos
    patientIoma = await getIomaPatient(Username, Password, patient.dni, sexConvertion(patient.sex), corporate.affiliate_id)
    // Hago esto porque el api de ioma siempre tira 200 y en caso de error te lo manda en el body de la respuesta
    response  = !["4","5","20"].includes(patientIoma.CodigoMensaje)
  }

  if(response && patientIoma){
    // Si obtuvimos data de ioma, la aprovechamos para actualizar los datos del paciente para resolver inconsistencias
    await revalidatePatient(corporate, patient, isDependant, patientIoma)
  }

  return response
}
