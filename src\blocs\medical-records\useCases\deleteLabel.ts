import { NotFoundException } from "@nestjs/common"
import AiModelsService from "../../ai-models/ai-models.bloc"
import { MedicalRecordRepository } from "@umahealth/repositories"

export async function deleteLabel(uid: string, assignationId: string, labels: string) {
  // Get medical record
  const medicalRecordDocument = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  if (!medicalRecordDocument) {
    throw new NotFoundException(`[ Medical records | deleteLabel ] => Medical record not found for assignationId: ${assignationId}`)
  }

  // Replace epicrisis
  medicalRecordDocument["mr"]["epicrisis"] = medicalRecordDocument["mr"]["epicrisis"].replace(`${labels}. `, "")

  const motiveFromMR = medicalRecordDocument["mr"]["motivos_de_consulta"]
  const epicrisisFromMR = medicalRecordDocument["mr"]["epicrisis"]

  // Construct phrase for NLP predict
  const frase = `<MOTIVODECONSULTA> ${motiveFromMR} <EPICRISIS> ${epicrisisFromMR}`

  // Try predict
  const predictedDiagnostic = await AiModelsService.diagnosticPredict(frase)

  // Replace diagnostic
  medicalRecordDocument["mr_preds"]["diagnostico"] = typeof predictedDiagnostic !== "boolean" ? predictedDiagnostic : ""

  // Update document with new diagnostico and epicrisis
  return await MedicalRecordRepository.update(uid, assignationId, medicalRecordDocument)
}
