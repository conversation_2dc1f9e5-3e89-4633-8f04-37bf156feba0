import { IDocumentList, AppointmentRepository, DependantRepository, PatientRepository } from "@umahealth/repositories"
import { dependantUid, IPatient, IOnDemandAppointment, OnDemandAppointment, countries, paymentData } from "@umahealth/entities"
import { convertDateToTimezoneString } from "@umahealth/time"
import { Timestamp } from "@google-cloud/firestore"

/** Cuando el doctor crea una consulta de onDemand, se genera la consulta y se abre automáticamente */
/**
 * This method is called on the first workflow to take a onDemand appointment, and it creates an assignation document that will be updated later.
 * @param assignationId - will be the ID of this document
 * @param uid - the id of the patient
 * @param providerUid - the id of the providerUid
 * @param dependantUid - the id of the dependant this appointment is for, or false if no dependant
 * @param motivosDeConsulta - the symptoms the patient logged
 * @param creationTime - when the workflow was called
 * @returns a DocumentList<IAppointment> to be saved in batch
 */
export const CreateOnDemandAppointmentUseCase = async (
  assignationId: string,
  country: countries,
  providerUid: string,
  uid: string,
  dependantUid: dependantUid,
  motivosDeConsulta: string,
  creationTime: Date,
  paymentData: paymentData,
  pediatric: boolean
): Promise<IDocumentList<IOnDemandAppointment<Timestamp>>> => {
  let patientDocument: IPatient<Timestamp>
  if (dependantUid && typeof (dependantUid) !== "boolean") {
    patientDocument = await DependantRepository.getByUid(uid, dependantUid)
  } else if(uid) {
    patientDocument = await PatientRepository.getByUid(uid)
  }

  const appointmentDocument = new OnDemandAppointment<Timestamp>()

  appointmentDocument.appointment_data = {
    motivos_de_consulta: motivosDeConsulta,
    alertas: ""
  }
  appointmentDocument.assignation_id = assignationId
  appointmentDocument.cm = patientDocument?.corporate_norm || `UMA ${country}`
  appointmentDocument.country = patientDocument?.country || country
  appointmentDocument.cuil = "onDemand"
  appointmentDocument.cuit = "onDemand"
  appointmentDocument.especialidad = ""
  appointmentDocument.fullname = ""
  appointmentDocument.max_appointments = 1
  appointmentDocument.patient = {
    corporate: patientDocument?.corporate_norm || `UMA ${country}`,
    country: patientDocument?.country || country,
    dni: patientDocument?.dni || "",
    dob: patientDocument?.dob || "",
    fullname: patientDocument?.fullname || "",
    sex: patientDocument?.sex || "",
    uid: uid || "onDemand",
    uid_dependant: dependantUid,
    ws: patientDocument?.ws || "",
  }
  appointmentDocument.social_work = ""
  appointmentDocument.state = "ASSIGN"
  appointmentDocument.timestamps = {
    dt_create: Timestamp.fromDate(creationTime)
  }

  appointmentDocument.payment_data = paymentData || null
  appointmentDocument.pediatric = pediatric || false
  appointmentDocument.uid = providerUid

  /* This will be soon deprecated */
  appointmentDocument.date = convertDateToTimezoneString(creationTime, "YYYY-MM-DD")
  appointmentDocument.datetime = convertDateToTimezoneString(creationTime, "YYYYMMDDHHmm")
  appointmentDocument.time = convertDateToTimezoneString(creationTime, "HH:mm")

  return await AppointmentRepository.createOnDemand(assignationId, appointmentDocument, country)
}