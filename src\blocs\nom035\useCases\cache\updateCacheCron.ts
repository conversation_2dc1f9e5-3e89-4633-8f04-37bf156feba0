import { PubSub } from "@google-cloud/pubsub"
import { Logger } from "@nestjs/common"
import { getCorporates } from "../corporate/getCorporates"
import { ICacheType } from "../../interfaces"

export const updateCacheCron = async () => {
  const corporates = await getCorporates()
  const pubSubClient = new PubSub()
  const pubSubTopic = pubSubClient.topic("nom035-update-cache-topic")

  for (const corporate of corporates) {
    Logger.log(`[Nom035 | updateCacheCron] Updating cache for company: ${corporate.empresa}`)

    const cacheTypesToUpdate: ICacheType[] = ["corporate", "users", "indexResponses", "c1Responses", "c3Responses"]

    for (const type of cacheTypesToUpdate){
      try{
        await pubSubTopic
          .publishMessage({ json: {
            corporateId: corporate.empresa,
            type
          }
          })
      } catch (error) {
        Logger.error(`[Nom035 | updateCacheCron] Error publishing message to topic. Cache type: ${type}, Corporate: ${corporate.empresa}: ${error}`)
        throw error
      }
    }

    // Delay 1s
    await new Promise((resolve) => setTimeout(resolve, 1000))
  }
}