import { Timestamp } from "@google-cloud/firestore"
import { Injectable } from "@nestjs/common"
import { CreateHypertensionRecordUseCase } from "./useCases/CreateHypertensionRecordUseCase"
import { CreateHypertensionRecordBody } from "src/patient-app/monitoring/monitoring.entities"
import { updateStatsCounter } from "./useCases/updateStatsCounter"
import { getRecords } from "./useCases/getHypertensionRecords"


@Injectable()
export class MonitoringBloc {

  async createHypertensionService(data: CreateHypertensionRecordBody, dt_create: Timestamp) {
    return await CreateHypertensionRecordUseCase(data, dt_create)
  }

  async updateStatsCounter(uid: string, dt_create: Timestamp) {
    return await updateStatsCounter(uid, dt_create)
  }

  async getHypertensionRecords(uid: string) {
    return await getRecords(uid)
  }
}
