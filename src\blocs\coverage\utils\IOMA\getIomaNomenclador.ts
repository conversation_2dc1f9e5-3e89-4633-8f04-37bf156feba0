import { InternalServerErrorException } from "@nestjs/common"
import axios from "axios"
import { IIomaNomenclator } from "../../apiValidations/interfaces"
import { getIomaJWT } from "src/utils/getIomaJWT"

async function getIomaNomenclator(): Promise<IIomaNomenclator> {
  const code = 420199
  const jwt = await getIomaJWT()
  const responseData = await axios
    .get(`https://sistemas.ioma.gba.gov.ar/consumostest/api/Nomenclador/${code}`, {
      headers: { "Authorization": jwt },
    })
    .catch((error) => {
      throw new InternalServerErrorException(`Error getting Nomenclator from IOMA = ${error}`)
    })
  return responseData.data
}

export default getIomaNomenclator