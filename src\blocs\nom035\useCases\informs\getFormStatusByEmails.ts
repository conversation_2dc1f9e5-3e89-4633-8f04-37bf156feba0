import { Logger, NotFoundException } from "@nestjs/common"
import { FieldSet } from "airtable"
import { nom035InformsFormStatus } from "src/utils/airtable"

export const getFormStatusByEmails = (usersArray: FieldSet[], emails: string[]) => {
  try {
    if(!emails.length) throw new NotFoundException(409, "[ Airtable | getFormStatusByEmails ] Email not found")
    const formStatusArray: {[key:string]: nom035InformsFormStatus } = {}

    const users = usersArray.filter(user => emails.includes(user.correo as string))

    users.map(user => {
      const nom035Forms: nom035InformsFormStatus = {
        forms: [],
        hasInforms: false
      }

      if(user["cuestionario obligatorio (from empresa)"]){
        const formsTypes = (user["cuestionario obligatorio (from empresa)"] as string[])[0].split("+")
        formsTypes.map((types) => {
          switch(types){
          case "c1":
            nom035Forms.forms.push({
              name: "c1",
              completed: user["año mes (from c1)"] ? true : false
            })
            if((user["año mes (from c1)"] as string[])?.length){
              nom035Forms.hasInforms = true
            }
            break
          case "c2":
            nom035Forms.forms.push({
              name: "c2",
              completed: user["c2"] ? true : false
            })
            if((user["c2"] as string[])?.length){
              nom035Forms.hasInforms = true
            }
            break
          case "c3":
            nom035Forms.forms.push({
              name: "c3",
              completed: user["c3"] ? true : false
            })
            if((user["c3"] as string[])?.length){
              nom035Forms.hasInforms = true
            }
            break
          }
        })

      }

      formStatusArray[user.correo as string] = nom035Forms
    })

    return formStatusArray

  } catch (err) {
    Logger.log(`[ Airtable ] Error getting form status: ${err}`)
    // throw new InternalServerErrorException(`[ Airtable ] Error getting form status: ${err}`)
  }
}