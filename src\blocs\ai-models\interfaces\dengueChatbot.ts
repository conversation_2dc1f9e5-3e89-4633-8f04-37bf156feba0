export interface BaseRequest<T> {
	conversation_id: string;
	data: T;
}

export interface DengueChatbotProps {
	text: string;
	conversation_id: string;
}

export interface DengueChatbotBody {
	text: string;
}

export type DengueChatbotRequest = BaseRequest<DengueChatbotBody>;

export interface DengueChatbotResponse {
	output: {
		response_to_user: string,
		comorbilidades: string[] | null | undefined,
		telemedicine: boolean | null | undefined,
		ambulance: boolean | null | undefined
	}
}
