import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { countries, ProviderIncome } from "@umahealth/entities"
import { ProviderIncomesRepository } from "@umahealth/repositories"

export async function createIncome (country: countries, data: ProviderIncome<Timestamp>, type:string) {
  const newIncome = await ProviderIncomesRepository.create(country, data, type)
  if (!newIncome) throw new InternalServerErrorException(`[ Incomes | createIncome ] => Could not create income for type ${type}`)
  return newIncome
}