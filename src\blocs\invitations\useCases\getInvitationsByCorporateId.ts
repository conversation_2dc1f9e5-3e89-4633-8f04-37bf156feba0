import { Timestamp } from "@google-cloud/firestore"
import { InvitationsRepository } from "@umahealth/repositories"


export const getInvitationsByCorporateId = async (corporate: string, dateFrom: Date, dateTo: Date) => {

  const dateFromTimestamp = dateFrom ? Timestamp.fromDate(dateFrom): undefined
  const dateToTimestamp = dateTo ? Timestamp.fromDate(dateTo): undefined

  return await InvitationsRepository.getByOneCorporate(corporate, dateFromTimestamp, dateToTimestamp )

}
