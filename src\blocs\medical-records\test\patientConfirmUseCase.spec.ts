import { Timestamp } from "@google-cloud/firestore";
import { MedicalRecordRepository } from "@umahealth/repositories";
import { patientConfirmCloseMr } from "../useCases/patientConfirmCloseUseCase";
import { NotFoundException } from "@nestjs/common";

jest.mock("@umahealth/repositories");
describe("Patient confirm close MR", () => {
    const assignation_id = "assignation_id_1"
    const uid = "uid_1"
    const service = "chatAtt"
    afterEach(() => {
        jest.clearAllMocks()
    })

    it("should update dt_close in MR", async() => {
        (MedicalRecordRepository.getByAssignationId as jest.Mock).mockResolvedValue(
            {timestamps: {
                dt_close: Timestamp.fromDate(Timestamp.now().toDate())}
            }
        );
        (MedicalRecordRepository.update as jest.Mock).mockResolvedValue({})

        const confirmClose = await patientConfirmCloseMr(assignation_id, uid, service)

        expect(MedicalRecordRepository.getByAssignationId).toBeCalledTimes(1)
        expect(MedicalRecordRepository.update).toHaveBeenCalledTimes(1)
        expect(confirmClose).toEqual({})
    })

    it("Should throw a NotFound exception if MR not exist", async() => {
        (MedicalRecordRepository.getByAssignationId as jest.Mock).mockResolvedValue(false);
        
        try {
            await patientConfirmCloseMr(assignation_id, uid, service)
            fail("Expected an error to be thrown")
        } catch (error) {
            expect(error).toBeInstanceOf(NotFoundException)
            expect(error.message).toBe(`[ MedicalRecords | close ] => Medical record not found. Service: ${service} Assignation Id: ${assignation_id} Uid: ${uid}`)
        }

        expect(MedicalRecordRepository.getByAssignationId).toBeCalledTimes(1);
        expect(MedicalRecordRepository.update).toBeCalledTimes(0);
    })

    it("Should throw a NotFound exception if MR can't be updated", async() => {
        (MedicalRecordRepository.update as jest.Mock).mockResolvedValue(false);
        (MedicalRecordRepository.getByAssignationId as jest.Mock).mockResolvedValue(
            {timestamps: {
                dt_close: Timestamp.fromDate(Timestamp.now().toDate())}
            }
        );
        
        try {
            await patientConfirmCloseMr(assignation_id, uid, service)
            fail("Expected an error to be thrown")
        } catch (error) {
            expect(error).toBeInstanceOf(NotFoundException)
            expect(error.message).toBe(`[ MedicalRecords | close ] => Error closing medical record ${assignation_id} from user ${uid}`)
        }

        expect(MedicalRecordRepository.getByAssignationId).toBeCalledTimes(1)
        expect(MedicalRecordRepository.update).toBeCalledTimes(1)
    })
})