import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common"
import { INotification, INotificationBatch, NotificationSend, countries } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { NotificationRepository, ParametersRepository } from "@umahealth/repositories"
import * as moment from "moment"
import { eventData } from "src/patient-app/pillbox/pillbox.interface"

export async function createNotificationsProviderDocuments(
  country: countries,
  uid: string,
  notificationType: string,
  notificationsData: eventData[],
  receiverUid: string,
): Promise<IDocumentList<INotification<Timestamp>>> {
  const campaign = "providerDocuments"
  const templateDocument = await ParametersRepository.getProviderDocumentsCampaigns()
  if (!templateDocument)
    throw new NotFoundException("[ Notifications | createNotificationsProviderDocuments ] => providerDocuments campaign not found.")

  const allNotifications: NotificationSend[] = []
  const notificationDates = []

  for (const notification of notificationsData) {
    const { date, template, type, params } = notification
    switch (type) {
    case "email": {
      let body = templateDocument["providerDocumentsNotification"]["FCM_email"]
      if (params && params.length) {
        for (const key in params) {
          body = body.replace(`{{${key}}}`, params[key])
        }
      }
      allNotifications.push({
        date,
        sent: false,
        template,
        body,
        type,
      })
      notificationDates.push(Number(date))
      break
    }
    default: {
      Logger.error(
        `[ Notifications | createNotificationsProviderDocuments ] => ${type} notification has not been implemented yet`
      )
      break
    }
    }
  }

  const notificationsBatch: INotificationBatch = {
    uidReceiver: receiverUid,
    uid: uid,
    send: allNotifications,
    country: country,
    campaign,
    type: notificationType,
    next_send: Math.min(
      ...notificationDates.filter(
        (el) => Number(el) > Number(moment().tz("America/Argentina/Buenos_Aires").format("YYYYMMDDHHmm"))
      )
    ).toString(),
    forever: false,
  }

  return await NotificationRepository.createBatch(
    notificationsBatch,
    campaign
  )
}
