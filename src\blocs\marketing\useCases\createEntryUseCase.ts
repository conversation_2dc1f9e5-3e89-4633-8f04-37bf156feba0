import { BadRequestException, InternalServerErrorException, Logger } from "@nestjs/common"
import { countries, gender, IClientEntry } from "@umahealth/entities"
import axios from "axios"

export async function createEntryUseCase(
  corporateNorm: string,
  country: countries,
  dob: string,
  email: string,
  firstName: string,
  lastName: string,
  listId: string,
  sex: gender,
) {

  const marketingUrl = "https://restapi.fromdoppler.com/accounts/fmurzone%40uma-health.com"
  const apiKey = process.env.DOPPLER_MKT_API_KEY


  const clientEntry: IClientEntry = {
    email,
    fields: [
      { name: "COUNTRY", value: country },
      { name: "BIRTHDAY", value: dob },
      { name: "FIRSTNA<PERSON>", value: firstName },
      { name: "GENDER", value: sex},
      { name: "LASTNAME", value: lastName },
      { name: "CORPORATE", value: corporateNorm}
    ]
  }

  try {
    const res = await axios.post(
      `${marketingUrl}/lists/${listId}/subscribers?api_key=${apiKey}`,
      clientEntry
    )

    return {
      status: res.status,
      data: res.data.message,
    }
  } catch (err) {
    if (err.isAxiosError) {
      Logger.warn("Falló post en marketing list", " Marketing | createEntry ")
      Logger.warn(`${err.response.status}: ${err.response.config.url} ${err.response.statusText}`)

      if (err.response.status === 400) {
        throw new BadRequestException("No se pudo crear la entrada, verifique el ID de la lista")
      } else {
        throw new InternalServerErrorException(err)
      }
    }
  }
}