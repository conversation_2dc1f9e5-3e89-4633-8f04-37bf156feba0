import { IAppointmentLogsAnalysis, IParticipantLogs, IConnectionPeriod, IPrescriptionAttempt, IJoinRoomEvent, ITechnicalIssue } from "../appointments.entities"
import { calculateTotalTimeConnected, calculateOverlappingTime, calculateDuration } from "../utils/analyzeAppointmentLogs/timeCalculations"
import { generateAnalysis } from "../utils/analyzeAppointmentLogs/analysisGenerator"
import { IAppointmentLog, IProviderLog } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { generatePrescriptionHistory } from "../utils/analyzeAppointmentLogs/connectionHistory"

// Helpers para identificar eventos
const connectionEvents = {
  connect: [
    "connectionCreated",
    "streamCreated",
  ],
  disconnect: [
    "streamDestroyed",
    "connectionDestroyed",
  ]
}


function isConnectionEvent(event: string): boolean {
  return connectionEvents.connect.includes(event)
}
function isDisconnectionEvent(event: string): boolean {
  return connectionEvents.disconnect.includes(event)
}

function toDate(ts: any): Date {// eslint-disable-line @typescript-eslint/no-explicit-any
  // Convierte Firestore Timestamp-like a Date
  if (ts instanceof Date) return ts
  if (ts && typeof ts._seconds === "number") return new Date(ts._seconds * 1000)
  return new Date(ts)
}

function generateConnectionHistoryFromEvents(logs: IAppointmentLog[]): IConnectionPeriod[] {
  const history: IConnectionPeriod[] = []
  let currentConnection: { startTime: Date } | null = null
  const sortedLogs = [...logs].sort((a, b) => toDate(a.timestamp).getTime() - toDate(b.timestamp).getTime())
  sortedLogs.forEach(log => {
    const timestamp = toDate(log.timestamp)
    if (isConnectionEvent(log.event)) {
      if (!currentConnection) {
        currentConnection = { startTime: timestamp }
      }
    } else if (isDisconnectionEvent(log.event) && currentConnection) {
      const duration = calculateDuration(currentConnection.startTime, timestamp)
      history.push({
        connected_at: currentConnection.startTime,
        disconnected_at: timestamp,
        duration: {
          minutes: duration.minutes,
          seconds: duration.seconds
        }
      })
      currentConnection = null
    }
  })
  if (currentConnection) {
    const duration = calculateDuration(currentConnection.startTime, new Date())
    history.push({
      connected_at: currentConnection.startTime,
      disconnected_at: null,
      duration: {
        minutes: duration.minutes,
        seconds: duration.seconds
      }
    })
  }
  return history
}

export function analyzeAppointmentLogsFromEvents(logEvents: IAppointmentLog[], assignationId: string, patientUid?: string, providerUid?: string, oldProviderLogs?: IProviderLog<Timestamp>[]): IAppointmentLogsAnalysis {
  // Filtrar logs por assignationId y roles
  const patientLogs = logEvents.filter(log => log.data?.assignationId === assignationId && log.data?.role === "patient" && (!patientUid || log.data?.userId === patientUid))
  const providerLogs = logEvents.filter(log => log.data?.assignationId === assignationId && log.data?.role === "doctor" && (!providerUid || log.data?.userId === providerUid))

  const patientHistory = patientUid ? generateConnectionHistoryFromEvents(patientLogs) : []
  const providerHistory = providerUid ? generateConnectionHistoryFromEvents(providerLogs) : []

  const overlappingTime = calculateOverlappingTime(patientHistory, providerHistory)

  const analysis = generateAnalysis(
    patientHistory,
    providerHistory,
    [],
    {
      patientUidProvided: !!patientUid,
      providerUidProvided: !!providerUid
    },
    overlappingTime
  )

  let prescriptionHistory: IPrescriptionAttempt[] = []
  if (oldProviderLogs) {
    prescriptionHistory = generatePrescriptionHistory(oldProviderLogs, providerUid)
  }

  const result: IAppointmentLogsAnalysis = {
    analysis,
    assignation_id: assignationId,
    patient: patientUid ? {
      connection_history: patientHistory,
      total_time_connected: calculateTotalTimeConnected(patientHistory),
      current_status: patientHistory.some(p => !p.disconnected_at) ? "connected" : "disconnected",
      // no tiene sentido esto aca porque al obtener los eventos directo de opentok, no se sabe desde donde se conecto el paciente
      // join_room_history: []
    } : {
      connection_history: [],
      total_time_connected: { minutes: 0, seconds: 0 },
      current_status: "unknown",
      info: "No se proporcionó uid del paciente"
    },
    provider: providerUid ? {
      connection_history: providerHistory,
      total_time_connected: calculateTotalTimeConnected(providerHistory),
      current_status: providerHistory.some(p => !p.disconnected_at) ? "connected" : "disconnected",
      prescription_history: prescriptionHistory,
    } : {
      connection_history: [],
      total_time_connected: { minutes: 0, seconds: 0 },
      current_status: "unknown",
      info: "No se proporcionó uid del médico"
    },
    simultaneous_connection: overlappingTime,
    // lo mismo con technical issues, opentok no emite estos eventos
    technical_issues: []
  }
  return result
}
