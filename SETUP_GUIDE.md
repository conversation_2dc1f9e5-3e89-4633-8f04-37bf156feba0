# 🛠️ Guía Detallada de Configuración del Entorno

Esta guía te llevará paso a paso para configurar tu entorno de desarrollo local para Megalith.

## 📋 Checklist de Requisitos

Antes de comenzar, asegúrate de tener instalado:

- [ ] Node.js v16 o superior
- [ ] npm v8 o superior  
- [ ] Git
- [ ] Google Cloud SDK (gcloud)
- [ ] Editor <PERSON> (VSCode recomendado)
- [ ] Acceso a los repositorios de UMA Health

## 🚀 Configuración Paso a Paso

### Paso 1: Configuración de Node.js y npm

```bash
# Verificar versiones instaladas
node --version  # Debe ser v16+
npm --version   # Debe ser v8+

# Si necesitas instalar/actualizar Node.js
# Opción 1: Usar nvm (recomendado)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# Opción 2: <PERSON><PERSON><PERSON> desde nodejs.org
# https://nodejs.org/en/download/
```

### Paso 2: Configuración de Google Cloud SDK

```bash
# Instalar gcloud CLI
# Para Ubuntu/Debian:
echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
sudo apt-get update && sudo apt-get install google-cloud-cli

# Para macOS:
brew install --cask google-cloud-sdk

# Para Windows:
# Descargar desde: https://cloud.google.com/sdk/docs/install

# Inicializar gcloud
gcloud init
gcloud auth login
gcloud config set project uma-development-ar
```

### Paso 3: Clonar y Configurar el Proyecto

```bash
# Clonar el repositorio
git clone [URL_DEL_REPOSITORIO]
cd BE-megalith-main

# Instalar dependencias
npm install

# Verificar que no hay errores de instalación
npm audit
```

### Paso 4: Configuración de Base de Datos PostgreSQL

#### Opción A: Conexión a Base de Datos en la Nube (Recomendado)

```bash
# Establecer túnel SSH a través del bastión
gcloud compute ssh sql-bastion-host \
  --tunnel-through-iap \
  --zone=us-central1-b \
  --ssh-flag="-fN -L 5432:localhost:5432" \
  --project=uma-development-ar

# En otra terminal, verificar la conexión
psql -h localhost -p 5432 -U [USUARIO] -d [BASE_DATOS]
```

#### Opción B: PostgreSQL Local (Para desarrollo offline)

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS
brew install postgresql
brew services start postgresql

# Windows
# Descargar desde: https://www.postgresql.org/download/windows/

# Crear base de datos local
sudo -u postgres createdb megalith_dev
sudo -u postgres createuser --interactive
```

### Paso 5: Configuración de Redis

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# Configurar Redis para iniciar automáticamente
sudo systemctl enable redis-server
sudo systemctl start redis-server

# macOS
brew install redis
brew services start redis

# Windows
# Descargar desde: https://github.com/microsoftarchive/redis/releases

# Verificar que Redis está funcionando
redis-cli ping
# Respuesta esperada: PONG
```

### Paso 6: Configuración de Variables de Entorno

#### Crear archivo secret_creds.json

```bash
# Crear el archivo en la raíz del proyecto
touch secret_creds.json
```

#### Estructura del archivo secret_creds.json

```json
{
  "NODE_ENV": "development",
  "PORT": "8081",
  
  "DATABASE_URL": "postgresql://usuario:password@localhost:5432/database_name",
  "REDIS_URL": "redis://localhost:6379",
  
  "FIREBASE_PROJECT_ID": "uma-development-ar",
  "FIREBASE_PRIVATE_KEY": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n",
  "FIREBASE_CLIENT_EMAIL": "<EMAIL>",
  
  "GOOGLE_CLOUD_PROJECT": "uma-development-ar",
  "GOOGLE_APPLICATION_CREDENTIALS": "path/to/service-account-key.json",
  
  "JWT_SECRET": "your-jwt-secret-key",
  
  "STRIPE_SECRET_KEY": "sk_test_...",
  "STRIPE_WEBHOOK_SECRET": "whsec_...",
  
  "MERCADOPAGO_ACCESS_TOKEN": "TEST-...",
  
  "SENDGRID_API_KEY": "SG...",
  
  "TWILIO_ACCOUNT_SID": "AC...",
  "TWILIO_AUTH_TOKEN": "...",
  
  "OPENTOK_API_KEY": "...",
  "OPENTOK_SECRET": "...",
  
  "AIRTABLE_API_KEY": "key...",
  "AIRTABLE_BASE_ID": "app...",
  
  "POSTHOG_API_KEY": "phc_...",
  
  "OSDE_API_URL": "https://api.osde.com.ar",
  "OSDE_API_KEY": "...",
  
  "AUDIBAIRES_API_URL": "https://api.audibaires.com",
  "AUDIBAIRES_API_KEY": "...",
  
  "PRESERFAR_API_URL": "https://api.preserfar.com",
  "PRESERFAR_API_KEY": "..."
}
```

⚠️ **Importante**: 
- Este archivo contiene información sensible
- Nunca lo commitees al repositorio
- Pide las credenciales reales al equipo de desarrollo
- Está incluido en `.gitignore`

### Paso 7: Configuración de Firebase

```bash
# Instalar Firebase CLI (opcional, para debugging)
npm install -g firebase-tools

# Login a Firebase
firebase login

# Seleccionar proyecto
firebase use uma-development-ar
```

### Paso 8: Verificación de la Configuración

```bash
# Ejecutar la aplicación en modo desarrollo
npm run start:dev

# En otra terminal, verificar que la API responde
curl http://localhost:8081/api

# Verificar logs para errores
# Los logs aparecerán en la terminal donde ejecutaste start:dev
```

### Paso 9: Configuración del IDE (VSCode)

#### Instalar extensiones recomendadas

```bash
# Instalar extensiones desde la terminal
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension esbenp.prettier-vscode
code --install-extension ms-vscode.vscode-eslint
code --install-extension ms-vscode.vscode-jest
code --install-extension humao.rest-client
code --install-extension bradlc.vscode-tailwindcss
```

#### Configuración de workspace (.vscode/settings.json)

```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "jest.autoRun": "off",
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true
  }
}
```

## ✅ Verificación Final

Ejecuta estos comandos para verificar que todo está configurado correctamente:

```bash
# 1. Verificar que la aplicación compila
npm run build

# 2. Ejecutar linting
npm run lint

# 3. Ejecutar tests
npm run test

# 4. Verificar conexión a base de datos
npm run start:dev
# Buscar en los logs: "Database connected successfully"

# 5. Verificar conexión a Redis
# Buscar en los logs: "Redis connected successfully"

# 6. Verificar que la API responde
curl http://localhost:8081/api
# Debe devolver la documentación de Swagger
```

## 🚨 Solución de Problemas Comunes

### Error: ECONNREFUSED 5432

```bash
# Verificar que el túnel SSH está activo
ps aux | grep ssh
gcloud compute ssh sql-bastion-host --tunnel-through-iap --zone=us-central1-b --ssh-flag="-fN -L 5432:localhost:5432" --project=uma-development-ar
```

### Error: ECONNREFUSED 6379

```bash
# Verificar estado de Redis
sudo systemctl status redis-server

# Reiniciar Redis si es necesario
sudo systemctl restart redis-server
```

### Error: Variables de entorno no cargadas

```bash
# Verificar que secret_creds.json existe y tiene el formato correcto
cat secret_creds.json | jq .

# Reiniciar el servidor
npm run start
```

### Error: Permission denied en gcloud

```bash
# Re-autenticar con gcloud
gcloud auth login
gcloud auth application-default login
```

## 📞 Soporte

Si encuentras problemas durante la configuración:

1. **Revisa los logs** de la aplicación para errores específicos
2. **Consulta la documentación** en el README.md
3. **Pregunta al equipo** en el canal de desarrollo
4. **Revisa issues conocidos** en el repositorio

---

¡Tu entorno está listo! 🎉 Ahora puedes proceder con el desarrollo siguiendo las guías en ONBOARDING.md
