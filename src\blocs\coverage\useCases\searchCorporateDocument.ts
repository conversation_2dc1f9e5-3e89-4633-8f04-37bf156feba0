import { IHealthInsurance, PrescriptionsPatient } from "@umahealth/entities"
import { CORPORATE_MAPPINGS, Corporates, TCorporates } from "../utils/corporateMappings"
import { BadRequestException, Logger } from "@nestjs/common"
import { Timestamp } from "@google-cloud/firestore"
import { HealthInsuranceRepository } from "@umahealth/repositories"

export const searchCorporateDocumentUseCase = async (
  patient: PrescriptionsPatient,
  country: string
): Promise<IHealthInsurance<Timestamp> | null> => {
  if (!patient.corporate) {
    throw new BadRequestException(`[ ${searchCorporateDocumentUseCase.name} ] => Patient corporate is required - uid: ${patient.uid}`)
  }

  const corporateType = identifyCorporateType(patient.corporate)

  if (patient.dependantUid) {
    return searchDependantCorporate(patient, corporateType, country)
  }

  return searchPatientCorporate(patient, corporateType, country)
}

const identifyCorporateType = (corporateName: string): TCorporates | null => {
  const normalizedName = corporateName?.toUpperCase()?.trim()

  for (const [ key, mapping ] of Object.entries(CORPORATE_MAPPINGS)) {
    if (mapping.aliases.some(alias => normalizedName?.includes(alias))) {
      return key as TCorporates
    }
  }

  return null
}

const getSearchName = (corporateType: TCorporates, country: string): string => {
  const mapping = CORPORATE_MAPPINGS[ corporateType ]
  if (!mapping) {
    return null
  }

  if (typeof mapping.searchName === "function") {
    return mapping.searchName(country)
  }
  return mapping.searchName
}

const searchDependantCorporate = async (
  patient: PrescriptionsPatient,
  corporateType: TCorporates,
  country: string
): Promise<IHealthInsurance<Timestamp>> => {
  if (patient.dependantUid) {
    const searchName = corporateType
      ? getSearchName(corporateType, country)
      : patient.corporate

    let corporateDocument = await HealthInsuranceRepository.dependantGetByUid(patient.dependantUid, searchName)

    // Fallback search for IOMA
    if (!corporateDocument && corporateType === Corporates.IOMA) {
      corporateDocument = await HealthInsuranceRepository.getByName(patient.uid, Corporates.IOMA)
    }

    if (typeof corporateDocument === "boolean") {
      Logger.error(`[ ${searchCorporateDocumentUseCase.name} | ${searchDependantCorporate} ] => Could not find coverage ${searchName} for dependant: ${patient.dependantUid} - titular: ${patient.uid}`)
      return null
    }

    return corporateDocument
  }
}

const searchPatientCorporate = async (
  patient: PrescriptionsPatient,
  corporateType: TCorporates,
  country: string
): Promise<IHealthInsurance<Timestamp>> => {
  const searchName = corporateType
    ? getSearchName(corporateType, country)
    : patient.corporate

  let corporateDocument = await HealthInsuranceRepository.getByName(patient.uid, searchName)

  // Fallback search for IOMA
  if (!corporateDocument && corporateType === Corporates.IOMA) {
    corporateDocument = await HealthInsuranceRepository.getByName(patient.uid, Corporates.IOMA)
  }

  if (!corporateDocument) {
    Logger.error(`[ ${searchCorporateDocumentUseCase.name} | ${searchDependantCorporate} ] => Could not find coverage ${searchName} for patient: ${patient.uid}`)
    return null
  }

  return corporateDocument
}
