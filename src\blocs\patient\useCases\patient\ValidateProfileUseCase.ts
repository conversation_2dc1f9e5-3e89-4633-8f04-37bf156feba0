import { NotFoundException } from "@nestjs/common"
import { IPatient, IValidatePatient } from "@umahealth/entities"
import { PatientRepository } from "@umahealth/repositories"
import * as moment from "moment"
import { ValidateDni } from "src/utils/patient/validateDni"

export const ValidateProfileUseCase = async (uid: string): Promise<IValidatePatient> => {
  const missingField: Array <keyof Pick<IPatient, "dni"|"dob">> = []
  const patient = await PatientRepository.getByUid(uid)
  if(!patient) throw new NotFoundException(`Patient not found with uid: ${uid}`)

  // Validate DNI
  switch(patient.country){
  case("AR"):{
    if(patient?.dni === "" || !ValidateDni(patient?.dni) || patient?.dni === undefined){
      missingField.push("dni")
    }
    break
  }
  default: {
    if(patient?.dni === "" || patient?.dni === undefined || patient?.dni === null){
      missingField.push("dni")
    }
  }
  }

  // Validate date of birth -  format and years
  if(patient?.dob !== undefined && patient?.dob.match(/^(\d{4}-\d{2}-\d{2})/).length !== 0) {
    if( moment(patient?.dob).toDate() < moment("1900-01-01").toDate() || moment(patient?.dob).toDate() > moment().toDate()){
      missingField.push("dob")
    }
  }else{
    missingField.push("dob")
  }

  const profile: IValidatePatient = {
    profile: missingField
  }
  return profile
}
