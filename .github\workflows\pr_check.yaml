name: Code Test

on:
  pull_request:
    branches:
      - main
      - beta
    # paths:
    #   - src/**/*
    #   - .eslintrc.js
    #   - package-lock.json
    #   - tsconfig.json
    #   - tsconfig.build.json
    #   - .github/workflows/pr_check.yaml
    #   - .changeset/**/*
    #   - package.json

concurrency:
  group: ${{ github.workflow_ref }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  checks:
    uses: umahealth/ci-workflows/.github/workflows/npm-checks.yaml@main
    with:
      node-version: 20
      dependencies-additional-flags: --cpu=x64 --os=linux --libc=glibc
      eslint-extensions: "ts"
      lint-job: true
      build-job: true
      jest-job: true
      jest-additional-flags: "--detectOpenHandles"
      github-jest-runner: "basic-runners"
      sonar-job: true
      sonar-quality-gate-check: false
    secrets:
      npm-token: ${{ secrets.NPM_READ_TOKEN }}
      sonar-token: ${{ secrets.SONAR_TOKEN }}
      sonar-project-key: ${{ secrets.SONAR_PROJECT_KEY }}
