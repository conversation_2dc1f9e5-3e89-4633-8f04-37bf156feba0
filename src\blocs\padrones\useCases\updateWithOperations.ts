import { IPadrones } from "@umahealth/entities"
import { Padron<PERSON> } from "@umahealth/sql-models"
import { NotFoundException } from "@nestjs/common"

export const updateWithOperations = async (corporate: string, rows: IPadrones[]): Promise<boolean> => {

  const transaction = await Padrones.sequelize.transaction()
  try {
    await Promise.all(rows.map(async (row) => {
      const operation = row.operation?.toUpperCase()
      if(operation !== null || operation !== undefined || operation !== ""){
        if(operation.toUpperCase() === "ALTA"){
          const updated = await Padrones.update({ "active": true }, { where: { corporate, "documentType": row.documentType, "document": row.document, "affiliateNumber":row.affiliateNumber }, transaction},)
          if(updated[0] < 1){
            await Padrones.create({"corporate": row.corporate,
              "documentType": row.documentType, "document": row.document, "active": true, "affiliateNumber": row.affiliateNumber,
              "plan": row.plan, "nationality": row.nationality, "name": row.name, "surname": row.surname,
              "address": row.address, "phoneNumber": row.phoneNumber}, {transaction})
          }
        } else {
          const updateRow = await Padrones.update({ "active": false }, { where: { corporate, "documentType": row.documentType, "document": row.document, "affiliateNumber":row.affiliateNumber }, transaction})
          if(updateRow[0] < 1){
            await Padrones.create({"corporate": row.corporate,
              "documentType": row.documentType, "document": row.document, "active": false, "affiliateNumber": row.affiliateNumber,
              "plan": row.plan, "nationality": row.nationality, "name": row.name, "surname": row.surname,
              "address": row.address, "phoneNumber": row.phoneNumber}, { transaction })
          }
        }
      }
    }))
    await transaction.commit()
    return true
  } catch (err) {
    await transaction.rollback()
    throw new NotFoundException(`Error updating padron with operations => ${err.message}`)
  }

}
