import { Transaction, Timestamp, DocumentReference } from "@google-cloud/firestore"
import { IOnSiteAppointment } from "@umahealth/entities"

export const resetOnsiteAppointmentTransactionUseCase = async (
  transactionObj: Transaction,
  lockedAppointment: IOnSiteAppointment<Timestamp>,
  appointmentReference: DocumentReference,
) => {
  const assignation: Partial<IOnSiteAppointment<Timestamp>> = {
    appointment_data: {
      motivos_de_consulta: ""
    },
    state: "FREE",
    patient: {},
    payment_data: {
      method: "",
      practices: "",
      billedBy: "",
      full_price: 0,
      nc: false,
      paid: false,
      plan: "",
      price: 0,
    },
    practices: [{
      cod: "",
      name: "",
      plan: "",
      price: "",
    }],
  }

  transactionObj.update(appointmentReference, { ...assignation })
}