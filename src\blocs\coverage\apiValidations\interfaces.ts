import { IPfaHome, IPfaMedicalInsurance } from "src/patient-app/dependant/dependant.entities"

/* IOMA */
interface IIomaInformedPractice {
  codigo: string;
  porcentaje: number;
  efectorApeyNom: string;
  efectorCategoria: string;
  efectorCuit: string;
  efectorMatricula: number;
  efectorActuacion: number;
  guardia: boolean;
  estadoPractica: number;
  viaFacturacion: number;
  fechaRealizacion: string;
  valorIOMA: number;
  valorAfiliado: number;
  valorEntidad: number;
  valorHonorarios: number;
  valorGastos: number;
  valorCopago: number;
  codigoPatologia: string;
}

export interface IIomaTransaction {
  nroTransaccion: number;
  fechaPrestacion: string;
  codigoPatologia: string;
  cuitPrestador: string;
  codigoEstablecimiento: string;
  idPrestadorEntidadDomicilio: number;
  usuarioId: number;
  solicitanteApeyNom: string;
  solicitanteCuit: string;
  solicitanteMatricula: string;
  solicitanteIdPup: number;
  tipoAutorizacion: number;
  mismaVia: boolean;
  mismaPatologia: boolean;
  urgencia: boolean;
  porAuditoria: boolean;
  observaciones: string;
  fechaSolicitud: string;
  fechaAutorizacion: string;
  fechaInformada: string;
  valorTotalIOMA: number;
  valorTotalAfiliado: number;
  valorTotalEntidad: number;
  pupEntidadId: number;
  nroSolicitudEntidad: number;
  practicaInformada: IIomaInformedPractice[];
}
export interface IPatientIoma {
  "CodigoMensaje": string,
  "Mensaje": string,
  "NroSolicitud": number,
  "NroTransaccion": number,
  "afiliado"?:{
    "ApellidoNombre": string,
    "CUIL": string,
    "CodigoBaja": string,
    "FechaCese": string,
    "FechaIngreso": string,
    "FechaNacimiento": string,
    "IdAfi": number ,
    "Localidad": string,
    "LocalidadDescripcion": string,
    "Mail": string,
    "NumeroAfiliado": string,
    "NumeroCredencial": string,
    "NumeroDocumento": number ,
    "Partido": string,
    "PartidoDescripcion": string,
    "Periodo": string,
    "Sexo": number,
    "TipoAfiliatorio": string,
    "TipoDocumento": number,
    "TipoDocumentoDescripcion": string ,
    "anexoAfiliatorio": string,
    "descripcionOrigenAfiliatorio": string,
    "domicilio": string,
    "entidadAfiliatorio": string,
    "origenAfiliatorio": string,
    "telefonoCelular": string,
    "telefonoFijo": string,
    "tipoAfiliado": string
  },
  "patient":{
    "ApellidoNombre": string,
    "CUIL": string,
    "CodigoBaja": string,
    "FechaCese": string,
    "FechaIngreso": string,
    "FechaNacimiento": string,
    "IdAfi": number ,
    "Localidad": string,
    "LocalidadDescripcion": string,
    "Mail": string,
    "NumeroAfiliado": string,
    "NumeroCredencial": string,
    "NumeroDocumento": number ,
    "Partido": string,
    "PartidoDescripcion": string,
    "Periodo": string,
    "Sexo": number,
    "TipoAfiliatorio": string,
    "TipoDocumento": number,
    "TipoDocumentoDescripcion": string ,
    "anexoAfiliatorio": string,
    "descripcionOrigenAfiliatorio": string,
    "domicilio": string,
    "entidadAfiliatorio": string,
    "origenAfiliatorio": string,
    "telefonoCelular": string,
    "telefonoFijo": string,
    "tipoAfiliado": string
  },
  "cargas": IDependantIoma[]
}

interface IDependantIoma {
  "ApellidoNombre": string,
  "CUIL": string,
  "CodigoBaja": string,
  "FechaCese": string,
  "FechaIngreso": string,
  "FechaNacimiento": string,
  "IdAfi": number,
  "Localidad": string,
  "LocalidadDescripcion": string,
  "Mail": string,
  "NumeroAfiliado": string,
  "NumeroCredencial": string,
  "NumeroDocumento": number,
  "Partido": string,
  "PartidoDescripcion": string,
  "Periodo": string,
  "Sexo": number,
  "TipoAfiliatorio": string,
  "TipoDocumento": number,
  "TipoDocumentoDescripcion": string,
  "anexoAfiliatorio": string,
  "descripcionOrigenAfiliatorio": string,
  "domicilio": string,
  "entidadAfiliatorio": string,
  "origenAfiliatorio": string,
  "telefonoCelular": string,
  "telefonoFijo": string,
  "tipoAfiliado": string
}


export interface IPatientIomaGetAfiSex {
  "CodigoMensaje": string,
  "Mensaje": string,
  "NroSolicitud": number,
  "NroTransaccion": number,
  "ApellidoNombre": string,
  "CUIL": string,
  "CodigoBaja": string,
  "FechaCese": string,
  "FechaIngreso": string,
  "FechaNacimiento": string,
  "IdAfi": number ,
  "Localidad": string,
  "LocalidadDescripcion": string,
  "Mail": string,
  "NumeroAfiliado": string,
  "NumeroCredencial": string,
  "NumeroDocumento": number ,
  "Partido": string,
  "PartidoDescripcion": string,
  "Periodo": string,
  "Sexo": number,
  "TipoAfiliatorio": string,
  "TipoDocumento": number,
  "TipoDocumentoDescripcion": string ,
  "anexoAfiliatorio"?: string,
  "descripcionOrigenAfiliatorio"?: string,
  "domicilio"?: string,
  "entidadAfiliatorio"?: string,
  "origenAfiliatorio"?: string,
  "telefonoCelular"?: string,
  "telefonoFijo"?: string,
  "tipoAfiliado"?: string
}

export interface IAffiliatedValidation{
  "codigoMensaje": string;
  "mensaje": string;
  "nroSolicitud": number | null;
  "nroTransaccion": number;
  "numeroAfiliado": string;
  "apellidoNombre": string;
  "cuil": string;
  "numeroDocumento": number;
  "tipoDocumento": number;
  "tipoDocumentoDescripcion": string;
  "sexo": number;
  "fechaNacimiento": string;
  "periodo": string;
  "fechaIngreso": string;
  "fechaCese": string | null;
  "codigoBaja": string | null;
  "tipoAfiliatorio": string;
  "localidad": string;
  "localidadDescripcion": string;
  "partido": string;
  "partidoDescripcion": string;
  "token"?: number;
}

export interface IIOMARedisData extends IAffiliatedValidation{
  coverage: string,
  patientUid: string
}

export interface IIomaValidationError{
  nroSolicitud?: string | null;
  nroTransaccion?: number;
  codigoMensaje: string;
  mensaje: string;
}

export interface IIomaNomenclator{
    id: number,
    codigo: string,
    descripcion: string,
    tipo: string,
    baja: boolean,
    pupEntidadId: number
}

/* EMERGENCIAS */

export interface IBeneficiaries{
  affiliateNumber?: string
  corporate: string,
  type: string,
  taxTreatment: string,
  plan:string
}

export interface IEmerResponse{
  affiliateNumber?: string
  lastUpdate: Date
  plan:string,
  taxTreatment: string,
}

export interface IPfaPatient {
  birth: string,
  cellphone: string,
  credential: string,
  credentialCountry: string,
  credentialType: string,
  cuil: string,
  firstName: string,
  home: IPfaHome,
  lastName: string,
  mail: string,
  medicalInsurance: IPfaMedicalInsurance,
  secondFirstName: string,
  secondLastName: string,
  sex: "WOMEN" | "MAN" | "INDEFINITE",
  telephone: string
}
