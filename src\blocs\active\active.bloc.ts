import { Injectable } from "@nestjs/common"
import { getActiveService, updateActiveCall } from "./useCases"
import { IActiveService } from "@umahealth/entities"

@Injectable()
export class ActiveBloc {
  async getActiveService(uid: string) {
    return await getActiveService(uid)
  }
  async updateActiveService(uid: string,data: Partial<IActiveService>):Promise<boolean>{
    return await updateActiveCall(uid, data)
  }
}

