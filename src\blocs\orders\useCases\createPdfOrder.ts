import { I<PERSON>rde<PERSON> } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { createOrderTemplate } from "../templates/orderTemplate"
import * as PdfPrinter from "pdfmake/build/pdfmake"
import * as vfsFonts from "pdfmake/build/vfs_fonts"
import { TDocumentDefinitions } from "pdfmake/interfaces"
import * as fs from "fs"
import { waitForFileExistence } from "src/utils/waitForFileExistence"
import { uploadLocalFileStorage } from "src/utils/files"

/**
 * This function create and upload the order pdf
 * @param order the order
 * @param file_path destination storage path for the pdf
 * @param chosenName if the patient has a chosenName defined
 * @returns url of the pdf created
 */

export async function createPdfAndUploadToStorage(order: IOrder<Timestamp>, file_path: string, chosenName?: string) {
  let docDefinition: TDocumentDefinitions
  // localPath temporal storage
  const localPath = `./uploads/${order.order_id}.pdf`
  try {
    // creation orderTemplate
    docDefinition = await createOrderTemplate(order, chosenName)

  } catch (err) {
    throw new InternalServerErrorException(`[createPdfOrderUseCase] => error creating order from template: ${err}`, err)
  }
  // creation localPdf
  const Roboto = {
    normal: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Regular.ttf" ], "base64"),
    bold: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Medium.ttf" ], "base64"),
    italics: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Italic.ttf" ], "base64"),
    bolditalics: Buffer.from(
      vfsFonts.pdfMake.vfs[ "Roboto-MediumItalic.ttf" ],
      "base64"
    ),
  }
  try {
    const pdfDoc = PdfPrinter.createPdf(docDefinition, {}, { Roboto }).getStream()

    pdfDoc.pipe(fs.createWriteStream(localPath))
    pdfDoc.end()

    await waitForFileExistence(localPath)
    // upload pdf file
    return await uploadLocalFileStorage(localPath as string, file_path)
  } catch (err) {
    throw new InternalServerErrorException(`[createPdfOrderUseCase] => failed to generate pdf. ${err}`, err)
  }
}