import { PubSub } from "@google-cloud/pubsub"
import { Logger } from "@nestjs/common"


export const sendWelcomeEmail = async (account: {fullname: string, email: string, pass: string }) => {

  const pubSubClient = new PubSub()
  const dataBuffer = Buffer.from(JSON.stringify({...account}))

  try {
    await pubSubClient
      .topic("nom035-user-created")
      .publishMessage({data: dataBuffer})
  } catch (error) {
    Logger.log(`[ Nom035 | sendRegistrationEmail ] Error sending email: ${error}`)
  }


}

export const sendUserAlreadyExistsEmail = async (account: {fullname: string, email: string }) => {

  const pubSubClient = new PubSub()
  const dataBuffer = Buffer.from(JSON.stringify({...account}))

  try {
    await pubSubClient
      .topic("nom035-user-already-exists")
      .publishMessage({data: dataBuffer})

  } catch (error) {
    Logger.log(`[ Nom035 | sendUserAlreadyExistsEmail ] Error sending email: ${error}`)
  }

}
