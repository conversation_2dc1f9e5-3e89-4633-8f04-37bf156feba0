import { IMedicalRecord, IProvider } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { Content, TDocumentDefinitions } from "pdfmake/interfaces"
import * as moment from "moment"
import logoUma64 from "src/assets/logoUmaBase64"
import { IProviderWithMatricula } from "src/blocs/health-providers/entities/IProviderWithMatricula"

export async function createCertificateTemplate(
  medicalRecordData: IMedicalRecord<Timestamp>,
  provider: IProviderWithMatricula<Timestamp>
): Promise<TDocumentDefinitions> {
  const parsedDate = moment(medicalRecordData.timestamps?.dt_close?.toDate())
    .tz("America/Argentina/Buenos_Aires")
    .format("DD/MM/YYYY")

  const intensityMap: { [key: string]: string } = {
    low: "baja",
    medium: "media",
    high: "alta"
  }

  const content: Content[] = [
    {
      image: "logo",
      width: 100,
      alignment: "center",
      margin: [0, 20, 0, 20] as [number, number, number, number]
    },
    {
      text: "Certificado de Apto Físico Digital",
      style: "header"
    },
    {
      text: [
        "Dejo constancia que el paciente ",
        { text: medicalRecordData.patient.fullname.toUpperCase(), bold: true },
        " con DNI ",
        { text: medicalRecordData.patient.dni, bold: true },
        " fue valorado en el día de la fecha presentando:"
      ],
      margin: [0, 20, 0, 20] as [number, number, number, number]
    },
    {
      ul: Object.entries(medicalRecordData.studies || {})
        .filter(([_, value]) => value)
        .map(([key, value]) => ({
          text: `${value.studyName} (${moment(value.date).format("DD/MM/YYYY")})`
        })),
      margin: [20, 0, 20, 20] as [number, number, number, number]
    },
    {
      text: [
        { text: medicalRecordData.fitnessCertificateDetails.diagnosis.hasContraindications ? "Presentando" : "No presentando", bold: true },
        " contraindicaciones para realizar actividad física."
      ],
      margin: [0, 0, 0, 20] as [number, number, number, number]
    },
    {
      text: [
        { text: "Observaciones o recomendaciones: ", bold: true },
        medicalRecordData.fitnessCertificateDetails.observations || "[Sin observaciones]"
      ],
      margin: [0, 0, 0, 20]
    },
    {
      text: [
        "Certifico que ",
        { text: medicalRecordData.fitnessCertificateDetails.canDoActivity ? "se encuentra" : "no se encuentra", bold: true },
        " en condiciones de salud aptas para la práctica de actividad física de intensidad",
        medicalRecordData.fitnessCertificateDetails.canDoActivity && medicalRecordData.fitnessCertificateDetails.diagnosis?.intensity
          ? ` ${intensityMap[medicalRecordData.fitnessCertificateDetails.diagnosis.intensity]}.`
          : "."
      ],
      margin: [0, 0, 0, 20] as [number, number, number, number]
    },
    {
      text: [
        "Para ser presentado ante quien corresponda, se extiende el presente certificado."
      ],
      margin: [0, 0, 0, 20] as [number, number, number, number]
    },
    ...(medicalRecordData.fitnessCertificateDetails.canDoActivity ? [
      {
        text: [
          "Este certificado es válido exclusivamente para actividades recreativas y educativas declaradas. No aplica para deportes extremos, actividades de alto riesgo o gimnasia de alto rendimiento."
        ],
        margin: [0, 0, 0, 20] as [number, number, number, number]
      },
      {
        text: [
          "Validez: ",
          { text: `${medicalRecordData.fitnessCertificateDetails.diagnosis?.validityPeriod || ""} meses`, bold: true }
        ],
        margin: [0, 0, 0, 30] as [number, number, number, number]
      }
    ] : []),
    {
      text: [
        "Fecha de emisión: ",
        { text: parsedDate, bold: true }
      ],
      margin: [0, 0, 0, 10] as [number, number, number, number]
    },
    {
      stack: [
        {
          text: `Dr./Dra. ${provider.fullname}`,
          bold: true
        },
        {
          text: `M.N.: ${provider.matricula}`,
          bold: true
        }
      ],
      alignment: "right"
    }
  ]

  return {
    content,
    styles: {
      header: {
        fontSize: 22,
        bold: true,
        alignment: "center",
        color: "#0a6dd7",
        margin: [0, 0, 0, 20] as [number, number, number, number]
      },
      sectionHeader: {
        fontSize: 14,
        bold: true,
        color: "#0a6dd7"
      },
      result: {
        fontSize: 14,
        bold: true,
        alignment: "center",
        margin: [0, 0, 0, 20] as [number, number, number, number]
      }
    },
    defaultStyle: {
      fontSize: 12,
      lineHeight: 1.5
    },
    images: {
      logo: logoUma64
    },
    pageSize: "A4",
    pageMargins: [40, 40, 40, 40] as [number, number, number, number]
  }
}

