import * as moment from "moment"
import { IEditUserBody } from "src/portal-app/nom035/user/user.entities"
import { AirtableUserProps } from "../interfaces"
import { FieldSet } from "airtable"
import { v4 } from "uuid"

export function adaptEditUserToAirtableFormat(user?: Partial<IEditUserBody> ) {

  const airtableUser: Partial<AirtableUserProps> = {}

  if (user?.profile?.phone) airtableUser.telefono = user.profile.phone
  if (user?.profile?.maritalStatus) airtableUser["estado civil"] = user.profile.maritalStatus

  if (user?.position?.educationLevel) airtableUser["nivel de estudios (completos)"] = user.position.educationLevel
  if (user?.position?.contractType) airtableUser["tipo de contratacion"] = user.position.contractType
  if (user?.position?.workingSchedule) airtableUser["tipo de jornada"] = user.position.workingSchedule
  if (user?.position?.rotationalShifts) airtableUser["rotacion turno"] = user.position.rotationalShifts ? "Si" : "No"
  if (user?.position?.hiringDate) airtableUser["fecha ingreso empresa"] =  moment(user.position.hiringDate, "YYYY-MM-DD").startOf("day").toISOString()
  if (user?.position?.positionDate) airtableUser["fecha inicio posicion actual"] = moment(user.position.positionDate, "YYYY-MM-DD").startOf("day").toISOString()
  if (user?.position?.area) airtableUser.area = user.position.area
  if (user?.position?.seniority) airtableUser.seniority = user.position.seniority
  if (user?.position?.position) airtableUser.puesto = user.position.position
  if (user?.position?.division) airtableUser.puesto = user.position.division


  return airtableUser
}
export function ageConversion(birthdate: Date) {
  const ageDifMs = Date.now() - new Date(birthdate).getTime()
  const ageDate = new Date(ageDifMs)
  return Math.abs(ageDate.getUTCFullYear() - 1970)
}

export function areaFilter(corporateId: string, user: FieldSet, area: string){
  if(corporateId === "VIVO") return user["c trabajo"] === area

  return user.area === area
}

export const generatePassword = () => {
  const uuidPassword = v4()
  return "U" + uuidPassword
}

export const getUsersWithFormByDate = (
  usersArray: FieldSet[],
  responses: FieldSet[],
  form: string,
  date?: string
): FieldSet[] => {

  const users = usersArray.filter((user) => {
    const userForm = user[form]
    return Array.isArray(userForm) && userForm.length > 0
  })

  if (!usersArray.length || !responses.length) {
    return []
  }

  if (!date) {
    return users
  }

  const lastFormByUser = new Map<string, string>()

  for (const response of responses) {
    const {"año mes": formDate, id, nomina} = response

    const userId = Array.isArray(nomina) && nomina[0]
    const formId = id as string

    // Si el formulario (C1/C2/C3) coincide con la fecha y es más reciente que el almacenado, actualizamos el map
    if (formDate && userId && formDate.toString().startsWith(date)) {
      if (!lastFormByUser.has(userId) || formDate.toString() > lastFormByUser.get(userId)) {
        lastFormByUser.set(userId, formId)
      }
    }
  }

  const usersWithFormByDate = users.reduce((acc, user) => {
    const userId = user.id as string
    const lastFormId = lastFormByUser.get(userId) || null

    if (lastFormId) {
      acc.push({
        ...user,
        [form]: [lastFormId],
      })
    }

    return acc
  }, [] as FieldSet[])

  return usersWithFormByDate
}
