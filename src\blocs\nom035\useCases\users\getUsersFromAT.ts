import {base} from "src/utils/airtable/airtableConfiguration"

export const getUsersFromAT = async (corporateId: string) => {
  const table = base("nomina")
  const usersRecords = await table.select({
    fields: [
      "nomina", "correo", "genero", "area", "c trabajo", "tipo de contratacion",
      "indice", "c1", "año mes (from c1)", "c2", "c3", "cuestionario obligatorio (from empresa)", "activo",
      "puesto", "division", "INE / FM", "fecha nacimiento", "tipo de jornada", "rotacion turno", "sucursal"
    ],
    filterByFormula: `{empresa}="${corporateId}"`
  }).all()

  const users = usersRecords.map(user => {
    user.fields.id = user.id
    return user.fields
  })

  return users
}
