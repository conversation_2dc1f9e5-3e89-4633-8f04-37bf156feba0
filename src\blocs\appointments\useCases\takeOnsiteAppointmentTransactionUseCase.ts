import { Transaction, Timestamp, DocumentReference } from "@google-cloud/firestore"
import { IOnSiteAppointment } from "@umahealth/entities"
import { IMakeOnsiteFarmatodoAppointmentData } from "src/patient-app/onsite/onsite.interface"

export const takeOnsiteAppointmentTransactionUseCase = async (
  transactionObj: Transaction,
  lockedAppointment: IOnSiteAppointment<Timestamp>,
  appointmentReference: DocumentReference,
  appointmentData: IMakeOnsiteFarmatodoAppointmentData
) => {
  const assignation: Partial<IOnSiteAppointment<Timestamp>> = {
    adminActions: [
      ...(lockedAppointment.adminActions ?? []),
      appointmentData.body.action,
    ],
    appointment_data: {
      motivos_de_consulta: appointmentData.body.motivos_de_consulta || ""
    },
    state: "ASSIGN",
    timestamps: {
      ...lockedAppointment.timestamps,
      dt_booking: Timestamp.fromDate(new Date())
    },
    patient: {
      corporate: appointmentData.patient.corporate_norm,
      country: appointmentData.patient.country,
      dni: appointmentData.patient.dni,
      dob: appointmentData.patient.dob || "",
      email: appointmentData.patient.email,
      fullname: appointmentData.patient.fullname,
      geo: appointmentData.body.geo,
      sex: appointmentData.patient.sex,
      uid: appointmentData.body.uid,
      uid_dependant: appointmentData.body.dependantUid || false,
      ws: appointmentData.patient.ws,
    },
    payment_data: appointmentData.payment_data,
    practices: appointmentData.body.practices,
  }

  transactionObj.update(appointmentReference, { ...assignation })
}