export { closeMedicalRecord } from "./close"
export { addLabel } from "./addLabel"
export { deleteLabel } from "./deleteLabel"
export { CloseMedicalRecordDueCancelAppointmentUseCase } from "./closeDueCancelAppointment"
export { updateMedicalRecord } from "./update"
export { updatePartialMedicalRecordUseCase } from "./updatePartialMedicalRecordUseCase"
export { createPdfConstancy } from "./createPdfConstancy"
export { closeMedicalRecordOnsiteAppointmentUseCase } from "./closeMedicalrecordUseCase"
export { getMrByIdUseCase } from "./getMrByIdUseCase"
export { createOnsiteUseCase } from "./createOnsiteRequestUseCase"
export { getAllMedicalRecordsByUid } from "./getAllForPatient"
export { getLastForDependantUseCase } from "./getLastForDependantUseCase"
export { updateMotiveUseCase } from "./updateMotive"
export { createFarmatodoMedicalRecordUseCase } from "./farmatodo/createFarmatodoMedicalRecordUseCase"
export { patientConfirmCloseMr } from "./patientConfirmCloseUseCase"
export { CancelMedicalRecordUseCase } from "./cancelMedicalRecordUseCase"
export { CreateFitnessCertificateUseCase } from "./createFitnessCertificateUseCase"
export { createPdfResume } from "./createPdfResume"
export { CreateGuardiaMedicalRecordNewUseCase } from "./createGuardiaMedicalRecordUseCase"
export { CreateMedicalRecordUseCase } from "./createMedicalRecordUseCase"