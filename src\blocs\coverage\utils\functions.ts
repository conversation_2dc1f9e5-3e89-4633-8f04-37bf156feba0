import { gender, ILicense } from "@umahealth/entities"
import { PostIomaPatientWithCargas } from "../apiValidations/dependants/IOMA"

const isLocalhost = ():boolean => {
  return process.env.NODE_ENV === "development"
}

export function getCurrentLicense<T>(licenses: ILicense<T>[]): ILicense<T> {
  return (
    licenses.find(license => license?.defaultLicense && !!license?.number) ??
    licenses.find(license => license?.active && !!license?.number) ??
    (licenses.length > 0 ? licenses[0] : null)
  )
}

const sexConvertion = (sex: string): number => {
  const sexMap:Record<string, number> = {
    "M": 1,
    "F": 2,
    "X": 3
  }

  return sexMap[sex]
}

const sexConvertionToString = (sex: number): gender => {
  const sexMap:Record<number, gender> = {
    1:"M",
    2:"F",
    3:"X"
  }

  return sexMap[sex]
}

const sexConvertionPfa = (sex: "WOMEN" | "MAN" | "INDEFINITE"): gender => {
  const sexMap:Record<string, gender> = {
    "MAN": "M",
    "WOMEN": "F",
    "INDEFINITE": "X"
  }
  return sexMap[sex]
}

const parseMemberPfa = (affiliate_number: string) => {
  // Remover cualquier barra existente y espacios
  const cleanNumber = affiliate_number.replace(/[\/\s]/g, "")

  // Si tiene exactamente 6 dígitos, agregar "/00"
  if (cleanNumber.length === 6) {
    return `${cleanNumber}/00`
  }

  // Si tiene más de 6 dígitos, tomar los primeros 6 y el resto después de la barra
  if (cleanNumber.length > 6) {
    const firstPart = cleanNumber.substring(0, 6)
    const secondPart = cleanNumber.substring(6)
    return `${firstPart}/${secondPart}`
  }
  return affiliate_number
}

/**
 * Parses a given DNI (Documento Nacional de Identidad) string and returns it formatted as a locale-specific string.
 *
 * @param {string} dni - The DNI string to be parsed.
 * @returns {string} - The parsed DNI formatted as a locale-specific string for 'es-ES'.
 *
 * @example
 * const formattedDni = parseDniPfaRequest("12345678");
 * console.log(formattedDni); // Output: "12.345.678"
 */
const parseDniPfaRequest = (dni: string) => {
  return parseInt(dni, 10).toLocaleString("es-ES")
}

const dniParserPfa = (dni: string): string => {
  // Recibe un dni sin puntos y lo devuelve con puntos 1111111 => 1.111.111
  const dniWithoutDots: string = dni.replace(/[.\s]/g, "")
  const formattedDni = parseInt(dniWithoutDots, 10).toLocaleString("es-AR", {minimumIntegerDigits: 6})
  return formattedDni
}

const iomaErrorMessages: {[key: string]: string} = {
  "2": "Ocurrió un error al conectarse a la Base de Datos",
  "4": "Ocurrió un error afiliado inactivo",
  "5": "Ocurrió un error afiliado inexistente",
  "6": "Ocurrió un error al buscar el afiliado",
  "7": "Usuario o contraseña incorrectos",
  "8": "Ocurrió un error al validar el usuario",
  "9": "Ocurrió un error código de prestador inexistente",
  "10": "Ocurrió un error código de patología inexistente",
  "11": "Ocurrió un error código de Entidad inexistente",
  "13": "Ocurrió un error en inserción prestación",
  "14": "Ocurrió un error en inserción practica",
  "15": "Ocurrió un error credencial pendiente de activación",
  "16": "Ocurrió un error credencial suspendida",
  "17": "Ocurrió un error credencial inhabilitada",
  "18": "Ocurrió un error credencial rechazada",
  "19": "Ocurrió un error credencial inexistente",
  "20": "Ocurrió un error afiliado inexistente",
  "21": "Ocurrió un error cuit de prestador invalido",
  "22": "Ocurrió un error cuit de solicitante invalido",
  "23": "Ocurrió un error cuc tiene que ser distinto de null o vacío",
  "24": "Ocurrió un error la cantidad de prácticas solicitadas debe ser mayor a 0",
  "25": "Ocurrió un error el tipo solicitud no es válido!",
  "40": "Ocurrió un error, no se pudo completar la transacción",
  "41": "Ocurrió un error, el código de actuación no es válido",
  "42": "Ocurrió un error, datos de Efector no válidos",
  "43": "Token inválido",
  "44": "El Token ya fue utilizado"
}

const isIOMA = (corporateId: string): boolean => {
  return ((corporateId === "IOMA") || (corporateId === "IOMA-APP"))
}

const getIOMAAfiNumber = async (dni: string, sex: string | undefined | null): Promise<string> => {
  const affiliateData = await PostIomaPatientWithCargas(JSON.parse(process.env.IOMA_AUTH).Username, JSON.parse(process.env.IOMA_AUTH).Password, dni, sexConvertion(sex))
  return affiliateData.afiliado.NumeroAfiliado
}
export {
  isLocalhost,
  sexConvertion,
  sexConvertionToString,
  iomaErrorMessages,
  isIOMA,
  getIOMAAfiNumber,
  sexConvertionPfa,
  parseMemberPfa,
  dniParserPfa,
  parseDniPfaRequest
}