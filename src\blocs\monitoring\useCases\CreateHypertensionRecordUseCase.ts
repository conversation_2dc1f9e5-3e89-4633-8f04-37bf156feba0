import { IDocumentList, MonitoringRepository } from "@umahealth/repositories"
import { HypertensionRecord, IHypertensionRecord } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { CreateHypertensionRecordBody } from "src/patient-app/monitoring/monitoring.entities"

export async function CreateHypertensionRecordUseCase(reqBody: CreateHypertensionRecordBody, dt_create: Timestamp): Promise<IDocumentList<IHypertensionRecord<Timestamp>>> {

  const data = new HypertensionRecord({
    activity: reqBody.activity,
    monitoring: "hypertension",
    other_activity: "",
    patient: { uid: reqBody.uid },
    result: reqBody.result,
    values: {
      diastolic: reqBody.diastolic,
      systolic: reqBody.systolic,
      measure_spot: reqBody.measure_spot
    },
    timestamp: {
      dt_create: dt_create
    }
  })
  // create record
  return await MonitoringRepository.createHypertensionRecord(reqBody.uid, data)

}
