import { Timestamp } from "@google-cloud/firestore"
import { PatientRepository } from "@umahealth/repositories"

export async function updateHistoryInPatient(text: string, patientUid: string, historyId: string, dt_updated: Timestamp) {
  const updatedHistory = {
    text: text,
    "timestamps.dt_updated": dt_updated
  }
  return await PatientRepository.updateHistory(patientUid, historyId, updatedHistory)

}
