import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { appointmentServices, countries, IAppointment, IOnlineAppointment } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { AppointmentRepository } from "@umahealth/repositories"

export async function cancelSpecialistAppointment(
  service: appointmentServices,
  country: countries,
  assignationId: string,
): Promise<IDocumentList<Partial<IAppointment<Timestamp>>>> {
  const cancelTime = new Date()

  const appointment = await AppointmentRepository.getByAssignationId<IOnlineAppointment<Timestamp>>(service, country, assignationId)
  if (!appointment) {
    throw new NotFoundException(`Appointment not found. assignationId: ${assignationId} service: ${service} country: ${country}.`)
  }
  appointment.state = "FREE"
  appointment.destino_final = "USER CANCEL"
  appointment.cancel_origin = "patient"
  appointment.patient = {}
  appointment.timestamps.dt_cancel = Timestamp.fromDate(cancelTime)

  return await AppointmentRepository.update(service, country, assignationId, appointment)

}