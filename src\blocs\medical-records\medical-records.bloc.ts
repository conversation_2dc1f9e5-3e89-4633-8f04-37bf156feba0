
import {
  action,
  appointmentServices,
  attCategories,
  countries,
  dependantUid,
  finalDestinations,
  IFitnessCertificateAppointment,
  IHealthInsurance,
  IMedicalRecord,
  IPatient,
  IProvider,
  paymentData
} from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import {
  addLabel,
  closeMedicalRecord,
  deleteLabel,
  updateMedicalRecord,
  createPdfConstancy,
  getMrByIdUseCase,
  createOnsiteUseCase,
  getAllMedicalRecordsByUid,
  updatePartialMedicalRecordUseCase,
  getLastForDependantUseCase,
  updateMotiveUseCase,
  closeMedicalRecordOnsiteAppointmentUseCase,
  createFarmatodoMedicalRecordUseCase,
  patientConfirmCloseMr,
  CloseMedicalRecordDueCancelAppointmentUseCase,
  CancelMedicalRecordUseCase,
  CreateFitnessCertificateUseCase,
  createPdfResume,
  CreateGuardiaMedicalRecordNewUseCase,
  CreateMedicalRecordUseCase
} from "./useCases"
import { Timestamp } from "@google-cloud/firestore"
import { Injectable } from "@nestjs/common"
import { FitnessCertificateDetails, providerSpecialties, Studies } from "@umahealth/entities/src/entities/types"
import { IMakeOnsiteFarmatodoAppointmentData } from "src/patient-app/onsite/onsite.interface"
import { IProviderWithMatricula } from "../health-providers/entities/IProviderWithMatricula"

@Injectable()
export class MedicalRecordsBloc {
  async close(antecedentes: string, assignationId: string, corporate: string, country: countries, dependant_uid: dependantUid, diagnostic: string, dt_cierre: Date, epicrisis: string, final_destination: finalDestinations, n_afiliado: string, notes: string, providerUid: string, rest: string, service: appointmentServices, specialist_referral: string, treatment: string, uid: string, studies?: Studies, affiliateNumber?: string | null, fitnessCertificateDetails?: FitnessCertificateDetails) {
    return await closeMedicalRecord(antecedentes, assignationId, corporate, country, dependant_uid, diagnostic, dt_cierre, epicrisis, final_destination, n_afiliado, notes, providerUid, rest, service, specialist_referral, treatment, uid, studies, affiliateNumber, fitnessCertificateDetails)
  }

  async patientConfirmClose(assignationId: string, uid: string, service: appointmentServices) {
    return await patientConfirmCloseMr(assignationId, uid, service)
  }

  async addLabelToMedicalRecord(uid: string, assignationId: string, labels: string) {
    return await addLabel(uid, assignationId, labels)
  }

  async deleteLabelFromMedicalRecord(uid: string, assignationId: string, labels: string) {
    return await deleteLabel(uid, assignationId, labels)
  }

  async create(assignationId: string, incidente_id: string, attCategory: attCategories, attentionDate: string, attentionTime: string, dependantUid: dependantUid, dt_create: Date, motivosDeConsulta: string, payment_data: paymentData, uid: string, especialidad?: providerSpecialties, providerUid?: string): Promise<boolean | IDocumentList<IMedicalRecord<Timestamp>>> {
    return await CreateMedicalRecordUseCase(assignationId, incidente_id, attCategory, attentionDate, attentionTime, dependantUid, dt_create, motivosDeConsulta, uid, especialidad, payment_data, providerUid)
  }

  async cancel(assignationId: string, dt_cancel: Timestamp, uid: string): Promise<boolean | IDocumentList<Partial<IMedicalRecord<Timestamp>>>> {
    return await CancelMedicalRecordUseCase(assignationId, dt_cancel, uid)
  }

  async closeDueCancelAppointment(assignationId: string, country: countries, dt_cierre: Date, final_destination: finalDestinations, service: appointmentServices, uid: string) {
    return await CloseMedicalRecordDueCancelAppointmentUseCase(assignationId, country, dt_cierre, final_destination, service, uid)
  }

  async update(assignationId: string, country: countries, dependant_uid: dependantUid, dt_updated: Date, providerUid: string, service: appointmentServices, uid: string, diagnostic?: string, epicrisis?: string, final_destination?: finalDestinations, motivosDeConsulta?: string, notes?: string, rest?: string, treatment?: string) {
    return await updateMedicalRecord(assignationId, country, dependant_uid, dt_updated, providerUid, service, uid, diagnostic, epicrisis, final_destination, motivosDeConsulta, notes, rest, treatment)
  }

  async updatePartialMedicalRecordUseCase(assignationId: string, data: Partial<IMedicalRecord<Timestamp>>, uid: string) {
    return await updatePartialMedicalRecordUseCase(assignationId, data, uid)
  }

  async closeMedicalRecordOnsiteAppointment(assignationId: string, country: countries, dt_cierre: Date, final_destination: finalDestinations, service: appointmentServices, uid: string, cancelMotive: string, adminAction: action) {
    return await closeMedicalRecordOnsiteAppointmentUseCase(assignationId, country, dt_cierre, final_destination, service, uid, cancelMotive, adminAction)
  }
  async createPdfConstancy(mr: IMedicalRecord<Timestamp>, file_path: string, chosenName: string) {
    return await createPdfConstancy(mr, file_path, chosenName)
  }

  async createPdfResume(mr: IMedicalRecord<Timestamp>, provider: IProviderWithMatricula<Timestamp>, file_path: string) {
    return await createPdfResume(mr, provider, file_path)
  }

  async getMedicalRecordById(uid: string, assignationId: string) {
    return await getMrByIdUseCase(uid, assignationId)
  }

  async createOnsite(uid: string, assignationId: string, medicalRecord: IMedicalRecord) {
    return await createOnsiteUseCase(uid, assignationId, medicalRecord)
  }

  async getAllByUid(uid: string) {
    return await getAllMedicalRecordsByUid(uid)
  }

  async createGuardiaNew(assignationId: string,
    patientDocument: IPatient<Timestamp>,
    coverage: IHealthInsurance,
    dependantUid: dependantUid,
    dt_create: Date,
    motivosDeConsulta: string,
    uid: string,
    payment_data: paymentData,): Promise<IDocumentList<IMedicalRecord<Timestamp>>> {
    return await CreateGuardiaMedicalRecordNewUseCase(
      assignationId,
      patientDocument,
      coverage,
      dependantUid,
      dt_create,
      motivosDeConsulta,
      uid,
      payment_data,
    )
  }
  async getLastForDependant(uid: string, dependantUid: dependantUid, category: attCategories) {
    return await getLastForDependantUseCase(uid, dependantUid, category)
  }

  async updateMotive(uid: string, assignationId: string, motive: string) {
    return await updateMotiveUseCase(uid, assignationId, motive)
  }

  async createFitnessCertificate(
    patient: IPatient<Timestamp>,
    assignation: IFitnessCertificateAppointment<Timestamp>,
    paymentData: paymentData
  ): Promise<IDocumentList<IMedicalRecord<Timestamp>>> {
    return await CreateFitnessCertificateUseCase(
      assignation,
      patient,
      paymentData
    )
  }

  async createFarmatodo(appointmentData: IMakeOnsiteFarmatodoAppointmentData): Promise<IDocumentList<IMedicalRecord<Timestamp>>> {
    return createFarmatodoMedicalRecordUseCase(appointmentData)
  }
}
