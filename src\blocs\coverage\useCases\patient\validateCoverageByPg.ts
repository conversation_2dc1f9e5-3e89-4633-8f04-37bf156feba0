import axios from "axios"
import { IHealthInsurance, IPatient } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { PatientRepository } from "@umahealth/repositories"
import { NotFoundException } from "@nestjs/common"

export const ValidateCoverageByPgUseCase = async (validatedBy: string, healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> => {

  const padronesUrl = `${process.env.UMA_URL_SHORTENER}/padrones`
  let patient: IPatient<Timestamp>

  if(validatedBy !== "affiliate_number") {
    patient = await PatientRepository.getByUid(healthInsurance.uid)

    if(!patient) {
      throw new NotFoundException(`[${ValidateCoverageByPgUseCase.name}] Patient not found - UID: ${healthInsurance.uid}`)
    }
  }

  const exists = Boolean(await axios.patch(padronesUrl,
    {
      plan_name: healthInsurance.plan,
      personal_document: patient?.dni,
      payroll_identifier: healthInsurance.affiliate_id,
      email: patient?.email
    }))

  return exists
}