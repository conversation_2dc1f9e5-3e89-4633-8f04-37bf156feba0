import { Timestamp } from "@google-cloud/firestore"
import { IProvider } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"

/**
 *
 * @param page number
 * @param size number
 * @returns Retorna un array ordenado según prioridades para el portal.
 * Primero se encuentran los providers con requestValidation = true
 * Después están los verified = true
 * Y después todo el resto
 */
export async function getAllProviders(page?: number, size = 30): Promise<{
  page: number,
  size: number,
  data: IProvider<Timestamp>[],
  pageCount: number
}> {
  const providers = await ProviderRepository.getAllProviders() as IProvider<Timestamp>[]

  // Se ordenan los doctores según la fecha de creación (del mas nuevo al mas viejo)
  const requestValidation = providers
    .filter((provider) => provider?.requestValidation === true && provider?.verified !== true)
    .sort((a, b) => {
      if (!a.timestamps?.dt_create) return 1
      if (!b.timestamps?.dt_create) return -1
      return b.timestamps.dt_create.toDate().getTime() - a.timestamps.dt_create.toDate().getTime()
    })

  const verified = providers.filter((provider) => provider?.verified === true && provider?.requestValidation !== true )
  const others = providers.filter((provider) => (provider?.verified !== true && provider?.requestValidation !== true) || (provider?.verified === true && provider?.requestValidation === true) )

  const totalProviders = ([] as IProvider<Timestamp>[]).concat(requestValidation, verified, others)



  const response: {
    page: number,
    size: number,
    data: IProvider<Timestamp>[],
    pageCount: number
  } = { page: null, size: null, data: [], pageCount: null }

  if(!page){
    return { page: 1, size: totalProviders.length, data: totalProviders, pageCount: 1 }
  }

  response.page = page === 0 ? 1 : page

  const providersByPage: IProvider<Timestamp>[] = []
  if(page - 1 >= 0){
    page = page - 1
  }

  let counter = 0
  let index = size * page
  while(counter < size){
    if(totalProviders.length > index){
      providersByPage.push(totalProviders[index])
    }
    counter += 1
    index += 1
  }

  response.data = providersByPage
  response.size = providersByPage.length
  response.pageCount = parseInt((totalProviders.length / size).toFixed(0))

  return response
}
