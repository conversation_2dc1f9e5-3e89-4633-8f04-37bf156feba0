import { getPfaJWT } from "src/utils/pfa/getPfaJWT"
import { IPfaPatient } from "../interfaces"
import axios from "axios"
import { pfa_patient_by_afi_document, pfa_patient_by_afi_num, pfa_patient_by_document, pfa_validate_affiliate } from "src/utils/pfa/endpoints"
import { InternalServerErrorException, Logger } from "@nestjs/common"
import { parseDniPfaRequest, parseMemberPfa } from "../../utils/functions"
import * as https from "https"
interface IPfaValidation {
  result: boolean
  code: string
}

interface IPfaValidationByDni extends IPfaValidation {
  correctAffiliateNumber?: string
}

const axiosInstance = axios.create({
  httpsAgent: new https.Agent({
    rejectUnauthorized: false
  })
})

export const GetPfaPatientByAfiNum = async (affiliate_number: string): Promise<IPfaPatient[]> => {
  const authJwt = await getPfaJWT()
  const patientResponse = await axiosInstance.post(pfa_patient_by_afi_num, { member: parseMemberPfa(affiliate_number) }, { headers: { "Content-Type":"application/json", "Authorization": authJwt } }).catch(error => {
    Logger.error(`Error getting dependants data from PFA with affiliate_number: ${affiliate_number} error: ${error}`)
    throw new InternalServerErrorException(`Error getting dependants data from PFA with affiliate_numner: ${affiliate_number} - error: ${error}`)
  })
  return patientResponse.data
}

export const GetPfaPatientByDocument = async (dni: string): Promise<IPfaPatient[]> => {
  const authJwt = await getPfaJWT()
  const patientResponse = await axiosInstance.post(pfa_patient_by_document, { document: parseDniPfaRequest(dni) }, { headers: { "Content-Type":"application/json", "Authorization": authJwt } }).catch(error => {
    Logger.error(`Error getting dependants data from PFA with dni: ${dni} error: ${error}`)
    throw new InternalServerErrorException(`Error getting dependants data from PFA with dni: ${dni} - error: ${error}`)
  })

  return patientResponse.data
}

export const GetPfaPatientByDocumentAndAfiNum = async (dni: string, affiliate_number: string): Promise<IPfaPatient[]> => {
  const authJwt = await getPfaJWT()

  const patientResponse = await axiosInstance.post(pfa_patient_by_afi_document, { document: dni, member: parseMemberPfa(affiliate_number) }, { headers: { "Content-Type":"application/json", "Authorization": authJwt } }).catch(error => {
    Logger.error(`Error getting dependants data from PFA with dni: ${dni} error: ${error}`)
    throw new InternalServerErrorException(`Error getting dependants data from PFA with dni: ${dni} - error: ${error}`)
  })
  Logger.log(`[PFA - patientResponse] GetPfaPatientByDocumentAndAfiNum: ${JSON.stringify(patientResponse.data)}`)
  return patientResponse.data
}

export const ValidatePFAPatient = async (dni: string, affiliate_number: string): Promise<IPfaValidation> => {

  if(!dni || !affiliate_number){
    Logger.warn(`[PFA - ValidatePFAPatient] Empty or invalid dni or affiliate_number provided: ${dni} and ${affiliate_number}`)
    return { result: false, code: "INVALID_DNI_OR_AFFILIATE_NUMBER" }
  }

  const authJwt = await getPfaJWT()
  // El affiliate_number decidimos no parsearlo para que en todo caso si hay un error de tipeo,entre al flujo por dni y se pueda corregir en la base de datos
  const validationResult = await axiosInstance.post(pfa_validate_affiliate, { document : parseDniPfaRequest(dni) , member: affiliate_number }, { headers: { "Content-Type":"application/json", "Authorization": authJwt } }).catch(error => {
    Logger.error(`Error validating affiliate data from PFA with dni: ${dni} and affiliate_number: ${affiliate_number} error: ${error}`)
    throw new InternalServerErrorException(`Error validating affiliate data from PFA with dni: ${dni} and affiliate_number: ${affiliate_number} - error: ${error}`)
  })
  Logger.log(`[PFA - validationResult]: Result: ${validationResult.data.result}`)

  return validationResult.data
}

export const ValidatePFAPatientByDni = async (dni: string): Promise<IPfaValidationByDni> => {

  try {
    Logger.log(`[PFA - ValidatePFAPatientByDni] Validating patient with dni: ${dni}`)

    // Validar que el DNI no esté vacío
    if (!dni || dni.trim() === "") {
      Logger.warn(`[PFA - ValidatePFAPatientByDni] Empty or invalid DNI provided: ${dni}`)
      return { result: false, code: "INVALID_DNI_FORMAT" }
    }

    const getPatientByDni = await GetPfaPatientByDocument(dni)

    // Validar que la respuesta de la API sea válida
    if (!getPatientByDni || !Array.isArray(getPatientByDni)) {
      Logger.warn(`[PFA - ValidatePFAPatientByDni] Invalid response format from PFA API for dni: ${dni}`)
      return { result: false, code: "INVALID_API_RESPONSE" }
    }

    // Validar que el array no esté vacío
    if (getPatientByDni.length === 0) {
      Logger.log(`[PFA - ValidatePFAPatientByDni] Patient not found with dni: ${dni}`)
      return { result: false, code: "PATIENT_NOT_FOUND_BY_DNI" }
    }

    const patientData = getPatientByDni[0]

    const affiliateNumber = patientData.medicalInsurance.member
    Logger.log(`[PFA - ValidatePFAPatientByDni] Patient found with dni: ${dni} and affiliate_id: ${affiliateNumber}`)

    const validationResult = await ValidatePFAPatient(dni, affiliateNumber)
    return { ...validationResult, correctAffiliateNumber: affiliateNumber }

  } catch (error) {

    Logger.error(`[PFA - ValidatePFAPatientByDni] Error validating patient with dni: ${dni} error: ${error}`)
    throw new InternalServerErrorException(`Error validating patient with dni: ${dni} - error: ${error}`)
  }

}
