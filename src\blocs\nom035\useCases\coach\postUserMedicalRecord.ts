import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { getUserByUid } from "@umahealth/auth"
import { IPortalUser } from "@umahealth/entities"
import { ICoachMedicalRecordBody } from "src/portal-app/nom035/coach/coach.entities"
import { postMedicalRecord } from "src/utils/airtable"

export const postUserMedicalRecord = async (uid: string, coach: IPortalUser<Timestamp>, medicalRecordData: ICoachMedicalRecordBody) => {
  const user = await getUserByUid(uid)
  if(!user){
    throw new NotFoundException(`[ Nom035 | Responses To Coach ] User with uid: ${uid} not found`)
  }
  return await postMedicalRecord(user.email, coach, medicalRecordData)

}
