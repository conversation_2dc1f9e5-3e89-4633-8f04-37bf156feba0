import { PatientRepository } from "@umahealth/repositories"
import { NotFoundException } from "@nestjs/common"

export async function setInactive(uid: string) {
  const patient = await PatientRepository.getByUid(uid)
  if (!patient) throw new NotFoundException("[ Patient | setInactiveAppointment ] -> patient not found", { description: `uid: ${uid}` })
  return await PatientRepository.update(uid, {
    _start_date: "",
    active_appointment: false,
  })
}
