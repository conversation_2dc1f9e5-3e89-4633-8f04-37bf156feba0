import {Logger} from "@nestjs/common"
import {FieldSet} from "airtable"
import {getUserByEmail} from "@umahealth/auth"
import nom035DTO from "../../models/nom035_userPositionDTO"


export const updateUsersCorporateUid = async (users: FieldSet[]) => {
  try {
    Logger.log(`[${updateUsersCorporateUid.name}] -> Updating uid's`)

    const userPromises = users.map(async (user) => {
      const authUser = await getUserByEmail(user["correo"] as string).catch(() => undefined)
      Logger.log(`[${updateUsersCorporateUid.name}] -> Colaborador: ${authUser?.uid} Email: ${user["correo"]}`)
      if (authUser) {
        await nom035DTO.updateUserUid({uid: authUser.uid, email: authUser.email})
      }
    })

    await Promise.allSettled(userPromises)

    return {
      message: "U<PERSON>'s actualizados exitosamente",
    }

  } catch (error) {
    Logger.error(`[${updateUsersCorporateUid.name}] -> Error al actualizar uid's: ${error.message}`)
    throw new Error(`Error al actualizar uid's: ${error.message}`)
  }
}
