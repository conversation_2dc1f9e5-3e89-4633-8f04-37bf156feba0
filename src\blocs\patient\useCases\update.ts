import { IDocumentList, PatientRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { IPatient } from "@umahealth/entities"
import { cleanupName } from "src/utils/patient/parseNames"

export const update = async (uid: string, data: IPatient<Timestamp>): Promise<IDocumentList<Partial<IPatient<Timestamp>>>> => {
  const patient = await PatientRepository.getByUid(uid)
  if (!patient) throw new NotFoundException(`Patient not found for uid: ${uid}`)
  // Sacamos los espacios extra que pueda haber en el nombre dado
  if (data.fullname) {
    data.fullname = cleanupName(data.fullname)
  } else if (patient.fullname) {
    data.fullname = cleanupName(patient.fullname)
  }
  // Sacamos los espacios extra que pueda haber en el nombre escogido
  if (data.chosenName) {
    data.chosenName = cleanupName(data.chosenName)
  } else if (patient.chosenName) {
    data.chosenName = cleanupName(patient.chosenName)
  }
  const updatePatientDocumentList = await PatientRepository.update(uid, { ...patient, ...data })
  return updatePatientDocumentList
}
