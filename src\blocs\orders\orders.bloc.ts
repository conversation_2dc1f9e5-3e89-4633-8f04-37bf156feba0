import { Timestamp } from "@google-cloud/firestore"
import { Injectable } from "@nestjs/common"
import { countries, IOrder } from "@umahealth/entities"
import { updateOrder, createPdfAndUploadToStorage } from "./useCases"

@Injectable()
export class OrdersBloc {

  async updateOrder(orderId: string, country: countries, data: Partial<IOrder<Timestamp>>) {
    return await updateOrder(orderId, country, data)
  }

  async createPdfOrder(order: IOrder<Timestamp>, file_path: string, chosenName: string) {
    return await createPdfAndUploadToStorage(order, file_path, chosenName)
  }
}
