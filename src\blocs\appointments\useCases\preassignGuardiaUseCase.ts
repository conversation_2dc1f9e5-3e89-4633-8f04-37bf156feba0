import { Timestamp } from "@google-cloud/firestore"
import { Logger } from "@nestjs/common"
import { IAppointment, IPreassignationData } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { MonitoringRepository, ProviderRepository } from "@umahealth/repositories"

export const preassignGuardiaUseCase = async (assignation: IDocumentList<Partial<IAppointment<Timestamp>>>, providerUid: string): Promise<{
  updatedAssignation: IDocumentList<Partial<IAppointment<Timestamp>>>,
  updatedPreassigned: IDocumentList<Partial<IPreassignationData>>,
}> => {
  const provider = await ProviderRepository.getByProviderUid(providerUid)

  const updatedAssignation = assignation
  updatedAssignation.data.fullname = provider.fullname
  updatedAssignation.data.cuit = provider.cuit
  updatedAssignation.data.provider = {
    fullname: provider.fullname,
    cuit: provider.cuit,
    uid: provider.uid,
  }
  updatedAssignation.data.state = "ASSIGN"

  const preassigned = await MonitoringRepository.getPreassignedBeta()
  preassigned.totalAppointments = preassigned.totalAppointments + 1
  const updatedPreassigned = await MonitoringRepository.updatePreassignBeta(preassigned)
  Logger.log(`Appointment preassigned to: ${provider.uid}`)

  return { updatedAssignation, updatedPreassigned }
}