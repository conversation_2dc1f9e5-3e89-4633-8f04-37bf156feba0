import { Logger } from "@nestjs/common"
import { CheckOneField } from "@umahealth/entities"
import { PatientRepository } from "@umahealth/repositories"

/**
 * Funcion para checkear si existe otro usuario (distinto al uid que le pasamos)
 * con un determinado valor en un determinado campo
 * @param uid uid del usuario que queremos obviar
 * @param field campo que queremos checkear
 * @param value valor que queremos checkear
 * @returns true si encontró un usuario con uid distinta a la recibida por params, de lo contrario false
 */
export const checkFieldUseCase = async (uid: string, data: CheckOneField) => {
  // Buscamos todos los usuarios que tienen el field indicado
  const existingUsers = await PatientRepository.getPatientByField(data)
  if (!existingUsers.length) Logger.log(`[ checkFieldUseCase ] => Users not found with ${JSON.stringify(data)}`)

  // Si existe, borramos del array resultante el usuario que hace la request
  const existingUserIndex = existingUsers.findIndex((user) => user.id === uid)
  if (existingUserIndex >= 0) {
    existingUsers.splice(existingUserIndex, 1)
  }
  Logger.log(`[ checkFieldUseCase ] => Found ${existingUsers.length} users with ${JSON.stringify(data)}`)
  // Si sigue habiendo usuarios en el array retornamos true, de lo contrario false
  return !!existingUsers.length
}