import { BadRequestException, NotFoundException } from "@nestjs/common"
import { deleteUser } from "@umahealth/auth"
import { deleteInFirestore } from "@umahealth/firestore"
import { PatientRepository } from "@umahealth/repositories"

/** metodo para eliminar los usuarios registrados en pruebas de cypress */
export const deleteTestUserUseCase = async (uid: string) => {
  const user = await PatientRepository.getByUid(uid)
  if (!user) {
    throw new NotFoundException(`[ deleteTestUserUseCase ] => User with uid ${uid} not found`)
  }
  if (!user.email.includes("@cypress.com")) {
    throw new BadRequestException(`[ deleteTestUserUseCase ] => User ${uid} does not correspond to cypress test`)
  }

  await deleteUser(uid)
  return await deleteInFirestore(`user/${uid}`)
}