import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getC3Questions } from "../reports/getC3Questions"

export const updateC3QuestionsCache = async (redis: Redis) => {
  Logger.log("[Nom035 | updateC3QuestionsCache] Updating C3 questions")

  const c3Questions = await getC3Questions()

  const redisExpiration = 86400 // 24 hours

  await redis.set("questions:c3", JSON.stringify(c3Questions), "EX", redisExpiration)
}