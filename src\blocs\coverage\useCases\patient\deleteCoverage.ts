import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/storage"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, NotFoundException } from "@nestjs/common"


export const DeleteCoverageUseCase = async (uid: string, coverageId:string): Promise<IDocumentList<IHealthInsurance<Timestamp>>>  => {

  const coverage = await HealthInsuranceRepository.getByName(uid, coverageId)
  if( typeof coverage === "boolean" ) throw new NotFoundException(`[ Coverages | patient | deleteCoverage ] Coverage not found with uid: ${uid} & coverage: ${coverageId}`)

  const document = await HealthInsuranceRepository.delete(uid, coverage)
  if(!document) throw new InternalServerErrorException(`[ Coverages | patient | deleteCoverage ] Error deleting coverage with uid: ${uid} & coverage: ${coverageId}`)
  return document

}
