import { IGuardiaAppointment } from "@umahealth/entities"
import { MonitoringRepository } from "@umahealth/repositories"
import * as moment from "moment"

export async function createAppointmentsGuardiaReportUseCase() {
  const today = moment().format("YYYY-MM-DD")
  // Query de consultas en la última hora
  const appointments: IGuardiaAppointment[] = await MonitoringRepository.getWaitingPatientsAppointments(today)
  // Filtrado de consultas VIP
  const vipAppointments =  appointments.filter(appointment => appointment.vip === true).map(vip => vip.assignation_id)
  // Filtrado de consultas no VIP
  const noVipAppointments =  appointments.filter(appointment => appointment.vip === false).map(noVip => noVip.assignation_id)

  return {
    waiting: {
      vip: vipAppointments,
      noVip: noVipAppointments,
    }
  }
}
