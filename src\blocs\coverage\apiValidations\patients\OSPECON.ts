import axios from "axios"

export async function validateOspeconPatient(dni: string) {
  const {api, user, pwd} = JSON.parse(process.env.OSPECON_AFFILIATE_VALIDATION_AUTH)

  let response
  try {
    response = await axios.get(`${api}?tipo=DN&docu_nro=${dni}`, {auth: {username: user, password: pwd}})
  } catch (error) {
    return false
  }

  if(response.status !== 200) return false
  if(response.data.hasOwnProperty("error")) return false

  const patient = response.data
  if (patient["plan_codi"] === "OSPECON   " && patient["Autorizado"] === true) {
    return true
  }
  return false
}
