import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getC1Questions } from "../reports/getC1Questions"

export const updateC1QuestionsCache = async (redis: Redis) => {
  Logger.log("[Nom035 | updateC1QuestionsCache] Updating C1 questions")

  const c1Questions = await getC1Questions()
  const redisExpiration = 86400 // 24 hours

  await redis.set("questions:c1", JSON.stringify(c1Questions), "EX", redisExpiration)
}