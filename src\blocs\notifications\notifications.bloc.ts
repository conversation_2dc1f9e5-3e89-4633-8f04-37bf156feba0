import { countries, ISpecialtyNotificationEvent, NotificationSend, TSpecialties } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { eventData } from "src/patient-app/pillbox/pillbox.interface"
import { ICreateNotification } from "src/portal-app/notifications/notifications.entities"
import { CreateSpecialistAppointmentNotificationUseCase, cancelNotificationsByAssignationId, cancelNotificationsByUid, deleteNotifications, createNotificationsPillbox, createNotificationsProviderDocuments, updateNotificationsPillbox, createNotification, getNotifications, getUserSpecialtyNotification, getReminderNotifications, updateReminderNotification, createSpecialtyNotificationEventBatch, updateSpecialtyNotificationEventBatch} from "./useCases"
import { sendDoctorAppointmentConfirmationUseCase } from "./useCases/sendSpecialistAppointmentConfirmationEmail"
import { ISendDoctorAppointmentConfirmation } from "./notifications.bloc.interface"

export class NotificationsBloc {
  async CreateSpecialistAppointmentNotification(
    assignationId: string,
    corporate: string,
    country: countries,
    dt_assignation: Timestamp,
    patientFullname: string,
    providerFullname: string,
    uid: string
  ) {
    return await CreateSpecialistAppointmentNotificationUseCase(assignationId, corporate, country, dt_assignation, patientFullname, providerFullname, uid)
  }

  async cancelNotificationsByAssignationId(assignation_id: string, patientUid: string) {
    return await cancelNotificationsByAssignationId(assignation_id, patientUid)
  }

  async cancelNotificationsByUid(uid: string, service: string) {
    return await cancelNotificationsByUid(uid, service)
  }

  async deleteNotifications(notificationId: string, service: string) {
    return await deleteNotifications(notificationId, service)
  }

  async createBatchNotificationsPillbox(campaign: string, country: countries, dt_end: boolean, notificationsData: eventData[], receiverUid: string, uid: string) {
    return await createNotificationsPillbox(campaign, country, dt_end, notificationsData , receiverUid, uid)
  }

  async createBatchNotificationsProviderDocuments(country: countries, uid: string, type: string, notificationsData: eventData[], receiverUid: string) {
    return await createNotificationsProviderDocuments(country, uid, type, notificationsData, receiverUid)
  }

  async updateBatchNotificationsPillbox(campaign: string, country: countries, dt_end: boolean, notificationsData: eventData[], receiverUid: string, uid: string, notificationId: string) {
    return await updateNotificationsPillbox(campaign, country, dt_end, notificationsData , receiverUid, uid, notificationId)
  }

  async createNotificationPatient(notification: ICreateNotification) {
    return await createNotification(notification)
  }

  async getNotificationPatient(uid: string, count: string, status: string) {
    return await getNotifications(uid, count, status)
  }

  async getUserSpecialtyNotification(uid: string, specialty?: TSpecialties) {
    return await getUserSpecialtyNotification(uid, specialty)
  }

  async createSpecialtyNotificationEventBatch(notificationData: ISpecialtyNotificationEvent<Timestamp>) {
    return await createSpecialtyNotificationEventBatch(notificationData)
  }

  async updateSpecialtyNotificationEventBatch(documentId: string, data: Partial<ISpecialtyNotificationEvent<Timestamp>>) {
    return await updateSpecialtyNotificationEventBatch(documentId, data)
  }

  async getReminderNotifications(service: string, country: countries, timezone: string) {
    return await getReminderNotifications(service, country, timezone)
  }

  async updateReminderNotification(service: string, documentId: string, nextSend: string, send: NotificationSend[]) {
    return await updateReminderNotification(service, documentId, nextSend, send)
  }

  async sendDoctorAppointmentConfirmationEmail(props: ISendDoctorAppointmentConfirmation) {
    return sendDoctorAppointmentConfirmationUseCase(props)
  }
}
