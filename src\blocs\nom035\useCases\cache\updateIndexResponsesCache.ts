import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getIndexResponses } from "../reports/getIndexResponses"

export const updateIndexResponsesCache = async (redis: Redis, corporateId: string) => {
  Logger.log(`[Nom035 | updateIndexResponsesCache] Updating index responses for corporate: ${corporateId}`)

  const indexResponses = await getIndexResponses(corporateId)

  const redisKeyPrefix = `corporate:${corporateId}`
  const redisExpiration = 86400 // 24 hours

  await redis.set(`${redisKeyPrefix}:form:index:responses`, JSON.stringify(indexResponses), "EX", redisExpiration)
}