import { AppointmentRepository } from "@umahealth/repositories"
import { dependantUid, IPatient, GuardiaAppointment, paymentData, countries, appointmentPatient, IHealthInsurance } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { scoringSelector } from "../utils/scoring"
import { isPediatric } from "../utils/isPediatric"
import { formatInTimeZone } from "date-fns-tz"
import { IGeo } from "src/patient-app/guardia/guardia.interface"

export const CreateGuardiaAppointmentNewUseCase = async (
  assignationId: string,
  country: countries,
  creationTime: Date,
  dependantUid: dependantUid,
  motivosDeConsulta: string,
  paymentData: paymentData,
  uid: string,
  patientDocument: IPatient<Timestamp>,
  coverageDocument?: IHealthInsurance<Timestamp>,
  geo?: IGeo,
  emergenciaIncidenteId?: string,
  invitationId?: string
) => {
  try {
    const now = new Date()
    const timeZone = "America/Argentina/Buenos_Aires"
    const assignationDocument = new GuardiaAppointment<Timestamp>()
    const score = await scoringSelector(paymentData, patientDocument)
    const patient: appointmentPatient = {
      corporate: coverageDocument.id || `UMA ${country}`,
      country: country,
      dni: patientDocument.dni || "",
      dob: patientDocument.dob || "",
      fullname: patientDocument.fullname || "",
      sex: patientDocument.sex || "",
      uid: uid,
      uid_dependant: dependantUid,
      ws: patientDocument.ws || "",
      pediatric: isPediatric(patientDocument.dob),
      geo: geo || {
        lat: null,
        lon: null,
        declarativeLocation: null
      }
    }
    assignationDocument.appointment_data = {
      motivos_de_consulta: motivosDeConsulta,
      alertas: ""
    }
    assignationDocument.timestamps = {
      dt_create: Timestamp.fromDate(creationTime),
      dt_booking: Timestamp.fromDate(creationTime)
    }


    assignationDocument.assignation_id = assignationId
    assignationDocument.score = score
    assignationDocument.affiliateNumber = coverageDocument.affiliate_id || null
    assignationDocument.affiliateType = coverageDocument.affiliateType || null
    assignationDocument.datetime = formatInTimeZone(now, timeZone, "yyyyMMddHHmm")
    assignationDocument.time = formatInTimeZone(now, timeZone, "HH:mm")
    assignationDocument.date = formatInTimeZone(now, timeZone, "yyyy-MM-dd")
    assignationDocument.fullname = ""
    assignationDocument.especialidad = ""
    assignationDocument.social_work = ""
    assignationDocument.cm = coverageDocument.id || `UMA ${country}`
    assignationDocument.country = country || "AR"
    assignationDocument.cuil = "bag"
    assignationDocument.cuit = ""
    assignationDocument.max_appointments = 1
    assignationDocument.patient = patient
    assignationDocument.payment_data = paymentData
    assignationDocument.state = "ASSIGN"
    assignationDocument.emergenciaIncidenteId = emergenciaIncidenteId || null
    assignationDocument.invitationId = invitationId || null
    return await AppointmentRepository.create("bag", country, assignationId, assignationDocument)
  } catch (err) {
    throw new InternalServerErrorException(`[ Appointments | patient | createGuardia ] => ${err.message}`)
  }
}