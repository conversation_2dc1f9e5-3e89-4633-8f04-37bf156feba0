import { IMedicalRecord } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { Content, TDocumentDefinitions } from "pdfmake/interfaces"
import * as moment from "moment"
import logoUma64 from "src/assets/logoUmaBase64"

export async function createDeclarationTemplate(
  medicalRecordData: IMedicalRecord<Timestamp>
): Promise<TDocumentDefinitions> {
  const parsedDate = moment()
    .tz("America/Argentina/Buenos_Aires")
    .format("DD/MM/YYYY - HH:mm")

  const content: Content[] = [
    {
      image: "logo",
      width: 100,
      alignment: "center",
      margin: [0, 20, 0, 20]
    },
    {
      text: "Declaración Jurada",
      style: "header"
    },
    {
      text: [
        "YO ",
        { text: medicalRecordData.patient.fullname.toUpperCase(), bold: true },
        " identificado/a con DNI ",
        { text: medicalRecordData.patient.dni, bold: true },
        ", declaro haber leído, comprendido y aceptar íntegramente los siguientes términos y condiciones:"
      ],
      margin: [0, 20, 0, 20]
    },
    {
      text: "Declaro que toda la información proporcionada en la planilla de antecedentes es veraz y fehaciente. Asumo plena responsabilidad por las consecuencias derivadas de cualquier error, omisión o falsedad en los datos consignados.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Comprendo que el servicio médico no incluye el llenado de planillas o formularios para instituciones educativas.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Acepto que la consulta médica digital podrá presentar variaciones en los tiempos de respuesta. Entiendo que la atención no será necesariamente inmediata y estará sujeta a la disponibilidad de los profesionales médicos.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Me comprometo a adjuntar los estudios médicos única y exclusivamente en formato PDF, cumpliendo con las siguientes condiciones: la documentación será totalmente legible; no contendrá cortes ni fragmentaciones y contará con resolución óptima para su comprensión.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Asumo plena responsabilidad de realizar la correcta carga de los estudios médicos y de garantizar la integridad y legibilidad de la documentación.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Estoy al tanto de que se enviarán notificaciones de recordatorio para la carga de estudios médicos. Comprendo que en caso de vencimiento de los plazos para la presentación de documentación: la solicitud quedará automáticamente invalidada; los importes abonados no serán reembolsables; y deberé iniciar un nuevo trámite.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Acepto que la documentación incompleta o incorrectamente cargada resultará en: rechazo inmediato de la solicitud; pérdida de los importes abonados y necesidad de reiniciar el trámite.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Comprendo que el profesional médico determinará mi aptitud física. En caso de que el médico considere que no soy apto: se me proporcionará una recomendación de seguimiento con un especialista; no se realizarán reintegros económicos; la evaluación se considerará completada. ÜMA no llevará a cabo ningún proceso de seguimiento, dando por concluida la evaluación.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "En caso de ser un menor de edad, confirmo que únicamente utilizaré el servicio con presencia y supervisión de un adulto responsable y que cuento con consentimiento expreso de mi madre, padre o tutor.",
      margin: [0, 0, 0, 15]
    },
    {
      text: "Declaro bajo juramento que la información proporcionada es verdadera y que he leído y comprendido íntegramente cada uno de los términos y condiciones precedentes.",
      margin: [0, 0, 0, 30]
    },
    {
      text: "Acepta en conformidad",
      bold: true,
      margin: [0, 30, 0, 10]
    },
    {
      text: "[X] Declaro haber leído, entendido y proporcionado la información solicitada.",
      margin: [0, 0, 0, 20]
    },
    {
      columns: [
        {
          text: `Fecha: ${parsedDate}`,
          width: "*",
          alignment: "right"
        }
      ]
    }
  ]

  return {
    content,
    styles: {
      header: {
        fontSize: 22,
        bold: true,
        alignment: "center",
        color: "#0a6dd7",
        margin: [0, 0, 0, 30]
      }
    },
    defaultStyle: {
      fontSize: 12,
      lineHeight: 1.5
    },
    images: {
      logo: logoUma64
    },
    pageSize: "A4",
    pageMargins: [40, 40, 40, 40]
  }
}
