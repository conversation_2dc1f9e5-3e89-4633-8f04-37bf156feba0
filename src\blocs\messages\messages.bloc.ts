import { Timestamp } from "@google-cloud/firestore"
import { Injectable, InternalServerErrorException, Logger } from "@nestjs/common"
import { countries, dependantUid, IChatMessages, IChatAttMessages } from "@umahealth/entities"
import { IDocumentList, MessagesRepository } from "@umahealth/repositories"
import { getAutomaticMessageUseCase } from "./useCases/getAutomaticMessageUseCase"
import { sendUnreadNotificationUseCase } from "./useCases/sendUnreadNotificationUseCase"
import { getMessagesByAssignationIdUseCase } from "./useCases/getMessagesByAssignationIdUseCase"

@Injectable()
export class MessagesBloc {

  async saveMessage(rol: string, text: string, uid: string, assignationId: string, providerUid: string, country: countries, dependant_uid: dependantUid, type = "text"): Promise<IDocumentList<IChatMessages<Timestamp>>> {
    Logger.log(`[ Messages | save ] => Saving new message to uid: ${uid}`)
    const messagesSaved = await MessagesRepository.saveMessages(rol, text, uid, assignationId, providerUid, country, dependant_uid, type)
    if(!messagesSaved) throw new InternalServerErrorException(`[ Messages | save ] => Error saving new message to uid: ${uid} and data: ${JSON.stringify({rol, uid, assignationId, providerUid, country, dependant_uid, type})}`)

    return messagesSaved
  }

  async getAutomaticMessage(type: keyof IChatAttMessages){
    return await getAutomaticMessageUseCase(type)
  }

  async getUnreadChats() {
    return await sendUnreadNotificationUseCase()
  }

  async getMessagesByAssignationId(assignationId: string, uid: string) {
    return await getMessagesByAssignationIdUseCase(assignationId, uid)
  }
}
