export interface IMrPreds {
    abort_description: string;
    destino_final: string;
    diagnostico: string;
    epicrisis: string;
    gduh: string;
    motivos_de_consulta: string;
    observaciones: string;
    pre_clasif: string;
}

export interface IMrMr{
  alertas: string[] | null;
  destino_final: string;
  diagnostico: string;
  dt: string | null;
  dt_cierre: string | null;
  epicrisis: string;
  motivos_de_consulta: string;
  observaciones: string | null;
  ordenes: string[]; // Asumiendo que ordenes es un array de objetos de tipo desconocido
  receta: string[]; // Asumiendo que receta es un array de objetos de tipo desconocido
  reposo: string | null;
  receta_ref: string | null;
  tratamiento: string | null;
}

export interface IPatientGeo {
  geohash: string | null;
  lat: string | null;
  lon: string | null;
}