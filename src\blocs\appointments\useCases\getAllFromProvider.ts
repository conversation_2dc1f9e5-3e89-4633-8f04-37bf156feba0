import { NotFoundException } from "@nestjs/common"
import { appointmentServices, appointmentStates, countries } from "@umahealth/entities"
import { AppointmentRepository, ProviderRepository } from "@umahealth/repositories"

export async function getAppointmentsByProviderUid(providerUid: string, country: countries, services: appointmentServices[], states: appointmentStates[]) {
  const appointments = []
  const provider = await ProviderRepository.getByProviderUid(providerUid)
  if (!provider) throw new NotFoundException(`[ face-detection ] => Could not find provider ${providerUid}`)

  const cuit = provider.cuit

  for (const service of services) {
    const appts = await AppointmentRepository.getAllByState(service, country, states)
    // For loops perform better than filters
    for (const ap of appts) {
      if (ap.cuit === cuit) {
        appointments.push(ap)
      }
    }
  }

  return appointments
}
