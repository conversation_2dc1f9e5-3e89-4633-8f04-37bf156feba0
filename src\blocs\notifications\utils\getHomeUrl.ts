import { Logger } from "@nestjs/common"
import { countries } from "@umahealth/entities"

export const getHomeUrl = (country: countries, uid: string) => {
  switch (country) {
  case "AR":
    return process.env.UMA_PATIENT_APP_AR_URL
  case "MX":
    return process.env.UMA_PATIENT_APP_MX_URL
  default:
    Logger.log(`[ ${getHomeUrl.name} ] => Country ${country} not supported for dynamicLink generation - uid: ${uid}`)
    return ""
  }
}