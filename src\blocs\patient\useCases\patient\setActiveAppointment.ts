import { callToken, IPatient } from "@umahealth/entities"
import { PatientRepository } from "@umahealth/repositories"
import { convertDateToTimezoneString } from "@umahealth/time"

export async function setActive(assignationId: string, call: callToken, date: Date, uid: string) {
  const stringDate = convertDateToTimezoneString(date, "YYYY-MM-DD HH:mm:ss")

  const patient: Partial<IPatient> = {
    _start_date: `${call.session}///${call.token}///${assignationId}///${stringDate}`,
    active_appointment: true
  }
  return await PatientRepository.update(uid, { ...patient })
}
