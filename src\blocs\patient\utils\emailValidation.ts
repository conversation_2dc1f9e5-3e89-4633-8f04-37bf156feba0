/**
 * Regex para identificar emails numéricos que deben omitir validación de DNI
 * Patrón: números@números.com (ej: <EMAIL>)
 * Se usa para dejar registrar nuevas cuentas en usuarios previos al 2021
 */
export const numericEmailRegex = /^\d+@\d+\.com$/i


export const shouldSkipDniValidation = (dataEmail?: string, patientEmail?: string): boolean => {
  return (dataEmail && numericEmailRegex.test(dataEmail)) ||
         (patientEmail && numericEmailRegex.test(patientEmail))
}

/**
 * @param {string} email - El email a verificar
 * @returns {boolean} - true si el email es numérico, false en caso contrario
 * El objetivo de este filtro es evitar que se envíen emails a emails numéricos, ya que estos no son válidos y generan una alarmante tasa de rebote con Doppler .
 */
export const isNumericEmail = (email: string): boolean => {
  // Estos dominios no se consideran emails numéricos, ya que son dominios de correo electrónico válidos, tomados del top 20 de dominios mas usados en uma
  const dominios = [
    "gmail.com",
    "hotmail.com",
    "abc.gob.ar",
    "yahoo.com.ar",
    "hotmail.com.ar",
    "live.com.ar",
    "outlook.com",
    "bue.edu.ar",
    "yahoo.com",
    "live.com",
    "hotmail.es",
    "outlook.es",
    "outlook.com.ar",
    "icloud.com",
    "agro.uba.ar",
    "yahoo.es",
    "mvl.edu.ar",
    "inti.gob.ar",
    "emergencias.com.ar",
    "derecho.uba.ar"
  ]

  if (!email) return false
  const dominio = email.split("@")[1]?.toLowerCase()

  if (dominios.includes(dominio)) return false
  return /^[0-9]+@[a-zA-Z0-9]+\.com$/.test(email)
}