import { base } from "src/utils/airtable/airtableConfiguration"

export const getIndexQuestions = async () => {
  const table = base("indice preguntas")
  const indexQuestions = await table.select({
    fields: ["num preg", "pregunta", "subdimension", "dimension", "max puntaje"]
  }).all()
  const questions = indexQuestions.map(record => {
    record.fields.id = record.id
    return record.fields
  })
  return questions
}