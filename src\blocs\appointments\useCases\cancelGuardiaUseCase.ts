import { AppointmentRepository } from "@umahealth/repositories"
import { IGuardiaAppointment, appointmentStates, countries } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { Logger } from "@nestjs/common"

export const cancelGuardiaAppointmentUseCase = async (country: countries, assignationId: string, state: appointmentStates) => {
  const currentAppointment: IGuardiaAppointment = await AppointmentRepository.getByAssignationId("bag", country, assignationId)
  if (!currentAppointment) {
    Logger.warn(`[ appointments | cancelGuardiaAppointmentUseCase ] => No assignation found for bag ${country} appointment ${assignationId}`)
    return null
  }
  return await AppointmentRepository.update("bag", country, assignationId, {
    state,
    timestamps: {
      ...currentAppointment.timestamps,
      dt_cancel: Timestamp.now()
    }
  })
}
