import { PubSub } from "@google-cloud/pubsub"
import { Logger } from "@nestjs/common"
import { ICacheType } from "../../interfaces"

export const sendUpdateCacheMessage = async (corporateId: string, type: ICacheType) => {
  try{
    Logger.log(`[Nom035 | sendUpdateCacheMessage] Publishing message to update cache topic. Cache type: ${type}, Corporate: ${corporateId}`)
    const pubSubClient = new PubSub()
    await pubSubClient.topic("nom035-update-cache-topic")
      .publishMessage({ json: {
        corporateId,
        type
      }
      })
  } catch (error) {
    Logger.error(`[Nom035 | sendUpdateCacheMessage] Error publishing message to topic. Cache type: ${type}, Corporate: ${corporateId}: ${error}`)
  }
}