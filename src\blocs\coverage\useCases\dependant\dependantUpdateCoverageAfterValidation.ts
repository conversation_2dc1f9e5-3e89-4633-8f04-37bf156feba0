import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import * as moment from "moment"
import { Timestamp } from "@google-cloud/firestore"
import { IValidation } from "../../coverage.bloc.interfaces"
import { InternalServerErrorException } from "@nestjs/common"

export const DependantUpdateCoverageAfterValidationUseCase = async (uid: string, coverage: IHealthInsurance<Timestamp>, status: IValidation): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> => {

  if(!status.passValidation){
    coverage["timestamps"]["dt_expire"] = Timestamp.fromDate(moment().utc().add(1,"month").toDate())
  }

  coverage["timestamps"]["dt_last_validation"] = Timestamp.fromDate(moment().utc().toDate())
  coverage["active"] = status.valid

  // Si no existe primary, seteo false como parámetro custom para que no fallen muchos otros flujos.
  // Si existe primary y está como true -> Si la OS es válida, lo mantengo como true, sino lo paso a false.
  if(coverage["primary"] === undefined || (coverage["primary"] && !status.valid)){
    coverage["primary"] = false
  }

  const document = await HealthInsuranceRepository.dependantUpdate(uid, coverage.id, {...coverage, ...status.updatedData})
  if(!document) throw new InternalServerErrorException(`[ Coverages | dependant | updateAfterValidation ] => Error updating coverage with uid: ${uid} and coverage: ${JSON.stringify(coverage)}`)
  return document
}

