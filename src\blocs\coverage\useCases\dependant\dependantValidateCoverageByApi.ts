import { DependantRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { emergenciasSalesforceApi } from "../../apiValidations/patients/EMERGENCIAS"
import { Logger, NotFoundException } from "@nestjs/common"
import { Timestamp } from "@google-cloud/firestore"
import { validateOspeconDependant } from "../../apiValidations/dependants/OSPECON"
import { validateOsmeconDependant } from "../../apiValidations/dependants/OSMECON"
import { ValidatePFADependant } from "../../apiValidations/dependants/PFA"
import {runIomaValidation } from "src/blocs/patient/useCases/revalidateIOMAPatient"
import { IValidateCoverageByApi } from "../../coverage.bloc.interfaces"
import { emerApiDefault } from "../../apiValidations/dependants/EMERGENCIAS"
import { validateUdemPatient } from "../../apiValidations/patients/UDEM"

export const DependantValidateCoverageByApiUseCase = async (corporate: IHealthInsurance<Timestamp>, uid: string): Promise<IValidateCoverageByApi> => {

  const dependant = await DependantRepository.getByUidFromDependant(uid)
  if(!dependant) throw new NotFoundException(`[ Coverages | dependant | validateByAPI ] Dependant not found wit uid: ${uid}`)
  let response: boolean
  let updatedData: Partial<IHealthInsurance<Timestamp>>

  switch(corporate.id.toUpperCase()){
  case "EMERGENCIAS":{
    const dependantCoverages = await emergenciasSalesforceApi(dependant.dni, corporate)
    response = !!dependantCoverages[corporate.id]
    if (dependantCoverages[corporate.id]) {
      updatedData = {
        affiliate_id: dependantCoverages[corporate.id].affiliateNumber || corporate.affiliate_id,
        plan: dependantCoverages[corporate.id].plan || corporate.plan,
        taxTreatment: dependantCoverages[corporate.id].taxTreatment
      }
    }
    break
  }
  case "IOMA": {
    response = await runIomaValidation(corporate, dependant, true)
    break
  }
  case "IOMA-APP": {
    response = await runIomaValidation(corporate, dependant, true)
    break
  }
  case "FATSA": {
    response = true
    break
  }
  case "OSPECON": {
    response = await validateOspeconDependant(dependant.dni)
    break
  }
  case "OSMECON": {
    response = await validateOsmeconDependant(dependant.dni)
    break
  }
  case "POLICIA FEDERAL": {
    const validationResult = await ValidatePFADependant(dependant.dni, corporate.affiliate_id)
    response = validationResult.result
    break
  }
  case "UDEM": {
    Logger.log(`[${DependantValidateCoverageByApiUseCase.name}] -> Validating UDEM dependant with email ${dependant.email}...`)
    response = await validateUdemPatient(dependant.dni)
    break
  }

  default:{
    const dependantCoverages = await emerApiDefault(dependant.dni)
    if(dependantCoverages[corporate.id]){
      response = true
    }else{
      response = false
    }
    break
  }
  }

  return {
    valid: response,
    updatedData,
  }
}
