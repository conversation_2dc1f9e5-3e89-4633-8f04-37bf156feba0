import { Timestamp } from "@google-cloud/firestore"
import { INotification } from "@umahealth/entities"
import { IDocumentList, NotificationRepository } from "@umahealth/repositories"

export async function cancelNotificationsByUid(uid: string, service: string): Promise<IDocumentList<Partial<INotification<Timestamp>>> | boolean> {
  const notifications = await NotificationRepository.getNotificationByUid(uid, service)
  if (notifications.length === 0) return false

  const activeNotification = notifications.find(notification => notification.next_send !== "cancelled")
  if (!activeNotification) return false

  if (activeNotification.id !== null) {
    activeNotification.next_send = "cancelled"
    return await NotificationRepository.updateNotification(activeNotification.id, activeNotification, service)
  }

  return false
}