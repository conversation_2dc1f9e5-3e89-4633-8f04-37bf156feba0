import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getIndexQuestions } from "../reports/getIndexQuestions"

export const updateIndexQuestionsCache = async (redis: Redis) => {
  Logger.log("[Nom035 | updateIndexQuestionsCache] Updating index questions")

  const indexQuestions = await getIndexQuestions()
  const redisExpiration = 86400 // 24 hours

  await redis.set("questions:index", JSON.stringify(indexQuestions), "EX", redisExpiration)
}