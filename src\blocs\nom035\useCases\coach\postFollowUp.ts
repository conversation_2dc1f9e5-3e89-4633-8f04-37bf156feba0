import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import {getUserByUid} from "@umahealth/auth"
import { IPortalUser } from "@umahealth/entities"
import { IAddFollowUpBody } from "src/portal-app/nom035/coach/coach.entities"
import { addFollowUp } from "src/utils/airtable"


export const postFollowUp = async (uid: string, coach: IPortalUser<Timestamp>,  data: IAddFollowUpBody) => {
  const user = await getUserByUid(uid)
  if(!user){
    throw new NotFoundException(`[ Nom035 | Add follow-up ] User with uid: ${uid} not found`)
  }
  return await addFollowUp(user.email, coach, data)
}
