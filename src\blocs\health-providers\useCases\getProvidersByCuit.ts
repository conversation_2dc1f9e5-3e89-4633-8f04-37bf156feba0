import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { IProvider } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"
import { IProviderWithMatricula } from "../entities/IProviderWithMatricula"
import { getCurrentLicense } from "src/blocs/coverage/utils/functions"
export async function getProvidersByCuit(
  cuit: string
): Promise<{ data: IProviderWithMatricula<Timestamp>[] }> {
  try {
    const providers = (await ProviderRepository.getAllProviders()) as IProvider<Timestamp>[]
    // Filtrar proveedores por CUIT, excluyendo aquellos sin CUIT o cuyo CUIT no coincide
    const filteredProviders = providers.filter(
      (provider): provider is IProvider<Timestamp> =>
        typeof provider === "object" &&
        provider !== null &&
        typeof provider.uid === "string" &&
        provider.cuit && provider.cuit.toString() === cuit
    )

    // Transformar a IProviderWithMatricula
    const data: IProviderWithMatricula<Timestamp>[] = []
    for (const provider of filteredProviders) {
      const licenses = await ProviderRepository.getLicenses(provider.uid)
      const currentLicense = getCurrentLicense(licenses)
      if (currentLicense) {
        data.push({
          ...provider,
          matricula: currentLicense.number.toString(),
        })
      }
    }

    return {
      data: data
    }
  } catch (error) {
    throw new NotFoundException(`Error al obtener provider por CUIT: ${error}`)
  }
}