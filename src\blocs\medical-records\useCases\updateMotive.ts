import { NotFoundException } from "@nestjs/common"
import { MedicalRecordRepository } from "@umahealth/repositories"

export async function updateMotiveUseCase(patientUid: string, assignationId: string, motive: string) {
  const mr = await MedicalRecordRepository.getByAssignationId(patientUid, assignationId)
  if (!mr) {
    throw new NotFoundException(`[MedicalRecordsBloc | Update motive] => mr ${assignationId} for patient ${patientUid} not found`)
  }
  mr.mr.motivos_de_consulta = motive
  return await MedicalRecordRepository.update(patientUid, assignationId, mr)
}
