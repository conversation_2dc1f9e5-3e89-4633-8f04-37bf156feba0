import axios from "axios"
import * as moment from "moment"
import * as qs from "qs"
import {BadRequestException, InternalServerErrorException, Logger} from "@nestjs/common"
import { IEmergenciasCredentials } from "../../coverage.bloc.interfaces"
import { IEmerResponse, IBeneficiaries } from "../interfaces"
import { selectCoveragePlan } from "../utils/selectCoveragePlan"
import { IHealthInsurance } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"

const getToken = async (credentials: IEmergenciasCredentials) => {
  const data = qs.stringify(credentials)
  const headers = { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" }
  const result = await axios.post( credentials.tokenUrl, data, {headers})
  return `Bearer ${result["data"]["access_token"]}`
}

const GetBeneficiaries = async (dni: string, corporate = false, allData = false) => {
  const credentials: IEmergenciasCredentials = JSON.parse(process.env.EMERGENCIAS_PADRONES_API_CREDENTIALS)
  try {
    if(!dni || dni === "") throw new BadRequestException("Missing field: dni on Emergencias API")
    const token = await getToken(credentials)
    let url = `${credentials.coverageUrl}/${dni}`
    if(corporate !== false) url = `${url}/${corporate}`
    const result = await axios.get(url, { headers: { "Authorization": token }, timeout: 5000 }).catch(error => Logger.error(`[ Coverages | dependant | EmergenciasAPI | GetBeneficiaries ] => ${error}`))
    const resp: IBeneficiaries[] = []
    if(!result) return resp
    if(allData) return result.data
    if(result?.data?.length) {
      for (const element of result.data) {
        if(element["status"] != "Activo") continue
        resp.push({
          affiliateNumber: element?.externalMembershipID,
          corporate: element["healthInsuranceID"].toUpperCase(),
          type: "padron",
          taxTreatment: element["taxTreatment"],
          plan: element["plans"][0] || ""
        })
      }
    }
    return resp
  } catch(err) {
    throw new InternalServerErrorException(`[ Coverages | dependant | EmergenciasAPI ] => DNI: ${dni} Error: ${err.message}`)
  }
}

export const emerApiDefault = async (dni: string, patientCoverage?: IHealthInsurance<Timestamp>) => {
  try {
    const response: Record<string, IEmerResponse> = {}
    const emerRes: IBeneficiaries[] = await GetBeneficiaries(dni)
    if(!emerRes.length) return {}
    // Si recibe patientCoverage, seteamos el plan que coincida con esa coverage ya cargada
    if (patientCoverage) {
      response[patientCoverage.id] = selectCoveragePlan(emerRes, patientCoverage)
      return response
    }
    // De lo contrario, seteamos el primer plan encontrado en la API
    response[emerRes[0]?.corporate?.toUpperCase().trim()] = {
      affiliateNumber: emerRes[0]?.affiliateNumber,
      plan: emerRes[0]?.plan,
      lastUpdate: moment().utc().toDate(),
      taxTreatment: emerRes[0]?.taxTreatment,
    }
    return response
  } catch (err) {
    throw new InternalServerErrorException(`[ Coverages | dependant | EmergenciasAPI ] => DNI: ${dni} - err: ${err.message || err}}`)
  }
}
