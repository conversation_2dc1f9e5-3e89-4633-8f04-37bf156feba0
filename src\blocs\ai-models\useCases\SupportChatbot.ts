import axios from "axios"
import { CreateZendeskTicketProps, CreateZendeskTicketRequest, CreateZendeskTicketResponse, SupportChatbotRequest, SupportChatbotResponse } from "../interfaces/supportChatbot"

const apiUrl = `${process.env.UMA_AI_URL}/chatbot_uma`

export async function sendChatbotMessage(text: string, conversation_id: string) {

  const headers = {
    "Content-type": "application/json",
  }

  const body: SupportChatbotRequest = {
    step: "first",
    conversation_id,
    data: { text },
  }

  const response = await axios.post<SupportChatbotResponse>(
    apiUrl,
    body,
    { headers }
  )

  return response.data.output
}

export async function createZendeskTicket({ uid, conversation_id, descripcion, titulo, email, nombre_completo, dni, obra_social, telefono }: CreateZendeskTicketProps, conversation: string): Promise<string> {

  const headers = {
    "Content-type": "application/json",
  }

  const body: CreateZendeskTicketRequest = {
    step: "second",
    conversation_id: conversation_id.toString(),
    data: {
      uid,
      descripcion: `Descripción: ${descripcion}\n\nConversación:\n${conversation}`,
      titulo,
      email,
      nombre_completo,
      dni,
      obra_social,
      telefono,
    },
  }

  const response = await axios.post<CreateZendeskTicketResponse>(
    apiUrl,
    body,
    { headers }
  )

  return response.data.output
}
