import { countries } from "@umahealth/entities"
import { NotificationRepository } from "@umahealth/repositories"
import * as moment from "moment"

export async function getReminderNotifications(service: string, country: countries, timezone: string) {
  const currentTime = moment().tz(timezone).format("YYYYMMDDHHmm")
  return await NotificationRepository.getReminderNotifications(service, country, currentTime)
}
