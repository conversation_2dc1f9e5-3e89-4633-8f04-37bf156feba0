import { Logger } from "@nestjs/common"
import { FieldSet } from "airtable"
import { getUsersWithFormByDate } from "../../utils/functions"
import { C3SpeedometerStatistics } from "src/utils/airtable/statistics"
import { IFormFilters, INom035Form } from "src/portal-app/nom035/statistics/statistics.entities"

type CategoryBase = "cat1" | "cat2" | "cat3" | "cat4" | "cat5";

interface CategoryScores {
  cat1Total: number;
  cat2Total: number;
  cat3Total: number;
  cat4Total: number;
  cat5Total: number;
}

interface ProcessedResults {
  maxScore: number;
  totalScore: number;
  categoryScores: CategoryScores;
  redAlerts: number;
}

const processUserResponses = (users: FieldSet[], c3Responses: FieldSet[]): ProcessedResults => {
  const results = {
    maxScore: 0,
    totalScore: 0,
    redAlerts: 0,
    categoryScores: {
      cat1Total: 0,
      cat2Total: 0,
      cat3Total: 0,
      cat4Total: 0,
      cat5Total: 0
    }
  }

  let maxScoresFilled = false

  users.forEach((user) => {
    const c3FormCompleted = user.c3 as string[] || []
    if (!c3FormCompleted.length) {return}

    const lastId = c3FormCompleted[c3FormCompleted.length - 1]
    const record = c3Responses.find((record) => record.id === lastId)

    if (!maxScoresFilled) {
      (["cat1", "cat2", "cat3", "cat4", "cat5"] as CategoryBase[]).forEach((cat) => {
        // Cada categoria tiene un valor maximo, `${cat} / max`tiene un formato numero/numero, ejemplo: 15/20,
        // entonces el REGEX toma el valor maximo de cada categoria (en 5 / 20 seria el valor 20) luego sumamos el valor maximo de todas las categorias
        // y lo multiplicamos por la cantidad de respuestas, eso es el valor maximo posible para el c3 de la empresa
        const maxMatch = record[`${cat} / max`]?.toString().match(/\d+\/(\d+)/)
        if (maxMatch) {
          results.maxScore = results.maxScore + Number(maxMatch[1])
        }
      })
      maxScoresFilled = true
    }

    // record["c dim total"] es la suma de las categorias de cada user, es decir el valor final de c3 a nivel colaborador
    // totalScore es la suma del valor de c3 de todos los empleados de la compañia
    results.totalScore = results.totalScore + Number(record["c dim total"])

    if (record["resultado cuestionario"] === "Muy alto") {
      results.redAlerts = results.redAlerts + 1
    }
  })

  return results
}

const calculateRiskyCategories = (categoryAverages: number[]): string[] => {
  const categories = ["cat1", "cat2", "cat3", "cat4", "cat5"]
  return categories.filter((_, index) => categoryAverages[index] > 49)
}

const getCategoryResult = (finalResult: number, categoriesLimits: Map<number, string>): string => {
  for (const [limit, category] of categoriesLimits.entries()) {
    if (finalResult <= limit) {
      return category
    }
  }
  return "Muy alto"
}

export const getC3CategoriesStatisticsToSpeedometerGraph = (
  usersArray: FieldSet[],
  c3Responses: FieldSet[],
  filters?: IFormFilters
): C3SpeedometerStatistics => {
  Logger.log("[ Nom035 | Download Report] Getting C3 categories statistics")

  const users = getUsersWithFormByDate(usersArray, c3Responses, INom035Form.c3, filters?.date)
  const totalUsers = usersArray.length
  const totalResponses = users.length

  const thresholdAmount = Math.floor(totalUsers * 0.1)

  const emptyResponse = {
    enoughResponses: false,
    score: 0,
    answeredForms: 0,
    participationPercentage: 0,
    redAlerts: 0,
    riskyCategories: 0,
    generalPercentage: 0,
    categoriesLimits: new Map(),
    categoryResult: ""
  }

  if ((totalResponses < thresholdAmount && !filters?.area && !filters?.branch) || totalResponses === 0) {
    return emptyResponse
  }

  const results = processUserResponses(users, c3Responses)

  const categoryAverages = Object.values(results.categoryScores)
    .map((total) => total / totalResponses)
  const riskyCategories = calculateRiskyCategories(categoryAverages)

  // usamos la suma del valor de c3 de todos los empleados (totalScore) con el maximo puntaje posible (maxScore)
  // y la cantidad de colaboradores que respondieron c3 (participations) para tener el % de riesgo final a nivel empresa
  const finalResult = Number(
    ((results.totalScore / (results.maxScore * totalResponses)) * 100).toFixed(2)
  )
  const participationPercentage = Number(
    ((totalResponses / totalUsers) * 100).toFixed(2)
  )
  const score = Math.round(results.totalScore / totalUsers)
  const categoriesLimits = new Map([
    [17, "Nulo"],
    [26, "Bajo"],
    [34, "Medio"],
    [49, "Alto"],
    [100, "Muy alto"]
  ])

  return {
    enoughResponses: true,
    score,
    answeredForms: totalResponses,
    participationPercentage: participationPercentage,
    redAlerts: results.redAlerts,
    riskyCategories: riskyCategories.length,
    generalPercentage: finalResult,
    categoryResult: getCategoryResult(finalResult, categoriesLimits)
  }
}
