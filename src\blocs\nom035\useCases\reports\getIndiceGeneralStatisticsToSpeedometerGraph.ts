import { FieldSet } from "airtable"
import { dimensionsMaxValues, indiceRecommendations } from "src/utils/airtable/statistics"
import { getUsersWithFormByDate } from "../../utils/functions"
import { IFormFilters, INom035Form } from "src/portal-app/nom035/statistics/statistics.entities"

export const getIndiceGeneralStatisticsToSpeedometerGraph = (usersArray: FieldSet[], indexResponses: FieldSet[], filters?: IFormFilters) => {
  const totalDimensionsScore = {
    "fisica": 0,
    "emocional": 0,
    "social": 0
  }

  let redAlerts = 0
  let participations = 0
  let formsCount = 0
  let totalScore = 0

  const users = getUsersWithFormByDate(usersArray, indexResponses, INom035Form.index, filters?.date)

  const treshold_amount = Math.floor(usersArray.length * 0.1)

  const count = users.length

  if ((count < treshold_amount && !filters?.area && !filters?.branch) || count === 0){
    return { enoughResponses: false, totalPercentage: 0, labelResult: "", redAlerts: 0, answeredForms: 0, recommendation: "" }
  }

  users.map(user => {
    const indiceFormCompleted = user.indice as string[]

    if(indiceFormCompleted && indiceFormCompleted.length){
      participations += 1
      const lastForm = indiceFormCompleted[indiceFormCompleted.length - 1]
      formsCount ++

      const record = indexResponses.find(response => response.id === lastForm)
      totalScore += +record["puntaje indice"]
      totalDimensionsScore["fisica"] += +record["puntaje dim fisica"]
      totalDimensionsScore["emocional"] += +record["puntaje dim emocional"]
      totalDimensionsScore["social"] += +record["puntaje dim social"]
    }
  })

  const totalPercentage = +(totalScore / (150 * formsCount) * 100).toFixed(2)
  const labelResult = totalPercentage < 18 ? "Bienestar Óptimo" : totalPercentage < 35 ? "Bienestar Moderado" : totalPercentage < 60 ? "Salud Psicosocial Afectada" : "Salud Psicosocial en Riesgo"

  Object.keys(totalDimensionsScore).map((item: keyof typeof totalDimensionsScore) => {

    const limits = dimensionsMaxValues[item].limitsInPercentage
    let found = false
    Object.keys(limits).map((num) => {
      if(!found){
        if(+((totalDimensionsScore[item] / (dimensionsMaxValues[item].maxScore * formsCount)) * 100).toFixed(2) <= +num){
          const dimensionLimit = dimensionsMaxValues[item as keyof typeof dimensionsMaxValues].limitsInPercentage
          if(dimensionLimit[+num as keyof typeof dimensionLimit] === "Alto"){
            redAlerts++
          }
          found = true
        }
      }
    })
  })

  return { enoughResponses: true, totalPercentage, labelResult, redAlerts, answeredForms: formsCount, recommendation: indiceRecommendations[labelResult]  }
}
