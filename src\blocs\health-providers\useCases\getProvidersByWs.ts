import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { IProvider } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"
import { IProviderWithMatricula } from "../entities/IProviderWithMatricula"
import { getCurrentLicense } from "src/blocs/coverage/utils/functions"

export async function getProvidersByWs(ws: string): Promise<{ data: IProviderWithMatricula<Timestamp>[] }> {
  try {
    const allProviders = await ProviderRepository.getAllProviders() as IProvider<Timestamp>[]
    const filteredProviders = allProviders.filter(
      (provider): provider is IProvider<Timestamp> =>
        typeof provider === "object" &&
        provider !== null &&
        typeof provider.uid === "string" &&
        provider.ws && provider.ws.toString() === ws
    )
    const data: IProviderWithMatricula<Timestamp>[] = []
    for (const provider of filteredProviders) {
      const licenses = await ProviderRepository.getLicenses(provider.uid)
      const currentLicense = getCurrentLicense(licenses)
      if (currentLicense) {
        data.push({
          ...provider,
          matricula: currentLicense.number.toString()
        })
      }
    }
    return {
      data
    }
  } catch (error) {
    throw new NotFoundException(`Error al obtener provider por ws: ${error}`)
  }
}
