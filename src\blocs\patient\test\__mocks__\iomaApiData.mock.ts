import { IPatientIomaGetAfiSex } from "src/blocs/coverage/apiValidations/interfaces"

export const iomaApiDataMock: IPatientIomaGetAfiSex = {
  "ApellidoNombre": "test",
  "CUIL": "test",
  "CodigoBaja": null,
  "FechaCese": "01/01/1900",
  "FechaIngreso": "01/02/2024",
  "FechaNacimiento": "16/04/1958",
  "IdAfi": 0,
  "Localidad": "1",
  "LocalidadDescripcion": "test",
  "Mail": "",
  "NumeroAfiliado": "000000000000",
  "NumeroCredencial": "000000000000",
  "NumeroDocumento": 11111111,
  "Partido": "000",
  "PartidoDescripcion": "test",
  "Periodo": "03/2024",
  "Sexo": 2,
  "TipoAfiliatorio": "O",
  "TipoDocumento": 2,
  "TipoDocumentoDescripcion": "LC o DNI",
  "CodigoMensaje": "00",
  "Mensaje": "OK",
  "NroSolicitud": 0,
  "NroTransaccion": 0
}