import { InternalServerErrorException } from "@nestjs/common"
import { IValidateHealthInsurance } from "@umahealth/entities"
import { HealthInsuranceRepository } from "@umahealth/repositories"
import { ICoverageIncomplete } from "../../coverage.bloc.interfaces"


export const ParamsValidationUseCase = async (uid: string ): Promise<IValidateHealthInsurance> => {

  const incompleteHealthInsurance: IValidateHealthInsurance = {
    coverages: []
  }

  const coverages = await HealthInsuranceRepository.getAllByUid(uid)

  coverages.map(async (item) => {
    const coverageIncomplete: ICoverageIncomplete = {
      id: item.id,
      missingFields: []
    }

    if(item?.affiliate_id === undefined || item?.affiliate_id === "" || item?.affiliate_id === null){
      coverageIncomplete.missingFields.push("affiliate_id")
    }

    if(item.userInput === ""){
      try{
        await HealthInsuranceRepository.updateDocument(uid, item.id, {...item, userInput: item.id})
      }catch(err){
        throw new InternalServerErrorException(`[ Coverages | patient | paramsValidation ] Failed to update userInput value - UID ${uid}`)
      }
    }

    if(coverageIncomplete.missingFields.length !== 0){
      incompleteHealthInsurance.coverages.push( coverageIncomplete )
    }
  })

  return incompleteHealthInsurance

}
