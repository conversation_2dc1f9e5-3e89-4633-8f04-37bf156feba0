import {
  IDocumentList,
  MedicalRecordRepository,
} from "@umahealth/repositories"
import {
  IMedicalRecord, requestTypes,
} from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"

export const CancelMedicalRecordUseCase = async (
  assignationId: string,
  dt_cancel: Timestamp,
  uid: string,
  type?: requestTypes,
  origin?: string
): Promise<IDocumentList<Partial<IMedicalRecord<Timestamp>>>> => {
  const medicalRecordDocument = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  if (!medicalRecordDocument) {
    if (type === "chatAtt") {
      return
    } else {
      throw new NotFoundException(`[ MedicalRecords | cancel ] => couldn't find medical record with uid ${uid} and assignation id ${assignationId}`)
    }
  }
  medicalRecordDocument.mr.destino_final = origin === "portal"? "PORTAL CANCEL" : "USER CANCEL"
  medicalRecordDocument.mr.diagnostico = ""
  medicalRecordDocument.timestamps = {
    ...medicalRecordDocument.timestamps,
    dt_cancel: dt_cancel
  }
  const mrToUpdate = await MedicalRecordRepository.update(uid, assignationId, medicalRecordDocument)
  return mrToUpdate
}
