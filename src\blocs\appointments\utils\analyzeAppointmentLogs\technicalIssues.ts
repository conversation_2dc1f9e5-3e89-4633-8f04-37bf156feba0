import { IPatientLog, IProviderLog } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { EventMap, ITechnicalIssue } from "../../appointments.entities"

const technicalEvents = {
  publisherAccessDenied: "Problema de acceso a cámara/micrófono",
  subscriberVideoDisabled: "Video deshabilitado por problemas de conexión",
  subscriberAudioBlocked: "Audio bloqueado",
  providerPublisherAccessDenied: "Problema de acceso a cámara/micrófono del médico",
  providerSubscriberVideoDisabled: "Video del médico deshabilitado por problemas de conexión",
  providerSubscriberAudioBlocked: "Audio del médico bloqueado",
  OT_INVALID_SESSION_ID: "Error de sesión"
}

/**
 * Analiza los logs para detectar problemas técnicos durante la consulta
 */
export function analyzeTechnicalIssues(
  patientLogs: IPatientLog<Timestamp>[],
  providerLogs: IProviderLog<Timestamp>[]
): ITechnicalIssue[] {
  const issuesMap: EventMap = {}

  // Procesar logs del paciente
  patientLogs.forEach(log => {
    const eventType = log.event as keyof typeof technicalEvents
    if (technicalEvents[eventType]) {
      const key = `patient_${technicalEvents[eventType]}`
      const currentTime = log.timestamps.dt_create.toDate()

      if (!issuesMap[key]) {
        issuesMap[key] = {
          start_time: currentTime,
          end_time: currentTime,
          description: technicalEvents[eventType],
          actor: "patient"
        }
      } else {
        issuesMap[key].end_time = currentTime
      }
    }
  })

  // Procesar logs del proveedor
  providerLogs.forEach(log => {
    const eventType = log.event as keyof typeof technicalEvents
    if (technicalEvents[eventType]) {
      const key = `provider_${technicalEvents[eventType]}`
      const currentTime = log.timestamps.dt_create.toDate()

      if (!issuesMap[key]) {
        issuesMap[key] = {
          start_time: currentTime,
          end_time: currentTime,
          description: technicalEvents[eventType],
          actor: "provider"
        }
      } else {
        issuesMap[key].end_time = currentTime
      }
    }
  })

  return Object.values(issuesMap)
}
