import { availableServices } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"

export async function setAvailableServicesUseCase(uid: string, serviceName: availableServices, active: boolean) {
  const permissions = await ProviderRepository.getServicePermissions(uid)
  let newOrUpdatedPermit = null
  const found = permissions.find(permit => permit.type === serviceName)
  if (found && found !== undefined) {
    found.active = active
    // Asegurarse de que el objeto timestamps exista
    if (!found.timestamps) {
      found.timestamps = {}
    }
    // Ahora podemos estar seguros de que timestamps no es undefined
    found.timestamps.dt_updated = Timestamp.now()
    newOrUpdatedPermit = await ProviderRepository.updateServicePermission(uid, serviceName, found)
  } else {
    // no exise, hay que crearlo
    newOrUpdatedPermit = await ProviderRepository.createServicePermission(uid, serviceName, active)
  }
  return newOrUpdatedPermit
}
