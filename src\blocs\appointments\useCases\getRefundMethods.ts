import { NotFoundException } from "@nestjs/common"
import { RequestRepository } from "@umahealth/repositories"

export const getRefundMethodsUseCase = async (assignationId: string) => {
  const response = []
  const appointment = await RequestRepository.get(assignationId, "online")
  if(!appointment) throw new NotFoundException("[ Appointments | getRefundMethods ] => Assignation not found" )
  switch (appointment?.payment_data?.method.toLowerCase()) {
  case "subscription":
    response.push("Subscription")
    break
  case "mercadopago":
    response.push("MercadoPago", "UmaCreditos")
    break
  case "umacreditos":
    response.push("UmaCreditos")
    break
  }
  return response
}
