import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { appointmentServices, countries, dependantUid, finalDestinations, IMedicalRecord } from "@umahealth/entities"
import { FitnessCertificateDetails, Studies } from "@umahealth/entities/src/entities/types"
import { DependantRepository, IDocumentList, MedicalRecordRepository, PatientRepository, ProviderRepository } from "@umahealth/repositories"

export async function closeMedicalRecord(
  antecedentes: string,
  assignationId: string,
  corporate: string,
  country: countries,
  dependant_uid: dependantUid,
  diagnostic: string,
  dt_cierre: Date,
  epicrisis: string,
  final_destination: finalDestinations,
  n_afiliado: string,
  notes: string,
  providerUid: string,
  rest: string,
  service: appointmentServices,
  specialist_referral: string,
  treatment: string,
  uid: string,
  studies?: Studies,
  affiliateNumber?: string | null,
  fitnessCertificateDetails?: FitnessCertificateDetails
): Promise<IDocumentList<Partial<IMedicalRecord<Timestamp>>>> {

  const medicalRecordDocument = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  let providerDocument = null
  let patientDocument = null
  if (!medicalRecordDocument) throw new NotFoundException(`[ MedicalRecords | close ] => Appointment or medical record not found. Service: ${service} Assignation Id: ${assignationId} Uid: ${uid} Country: ${country}`)

  medicalRecordDocument.mr.destino_final = final_destination
  medicalRecordDocument.mr.diagnostico = diagnostic || ""
  medicalRecordDocument.mr.epicrisis = epicrisis || ""
  medicalRecordDocument.mr.motivos_de_consulta = medicalRecordDocument.mr.motivos_de_consulta || ""
  medicalRecordDocument.mr.observaciones = notes || ""
  medicalRecordDocument.mr.reposo = rest || ""
  medicalRecordDocument.mr.specialist_referral = specialist_referral || ""
  medicalRecordDocument.mr.tratamiento = treatment || ""
  medicalRecordDocument.timestamps.dt_close = Timestamp.fromDate(dt_cierre)


  // Si la consulta es de guardia, tiene info del paciente pero no el provider
  if (service === "bag" || service === "chatAtt" || service === "quickPrescription") {
    providerDocument = await ProviderRepository.getByProviderUid(providerUid)
    medicalRecordDocument.provider.cuit = providerDocument.cuit || ""
    medicalRecordDocument.provider.especialidad = providerDocument.matricula_especialidad || ""
    medicalRecordDocument.provider.fullname = providerDocument.fullname
    medicalRecordDocument.provider.uid = providerUid
    medicalRecordDocument.provider.ws = providerDocument.ws || ""
  }
  if (service === "online") {
    if (dependant_uid && dependant_uid !== "") {
      patientDocument = await DependantRepository.getByUidFromDependant(dependant_uid)
      if (!patientDocument) {
        patientDocument = await DependantRepository.getByUid(uid, dependant_uid)
      }
    } else {
      patientDocument = await PatientRepository.getByUid(uid)
    }
    medicalRecordDocument.patient.address = patientDocument.address || ""
    medicalRecordDocument.patient.antecedentes = antecedentes || ""
    medicalRecordDocument.patient.country = patientDocument.country || country
    medicalRecordDocument.patient.dependant_uid = dependant_uid || false
    medicalRecordDocument.patient.dni = patientDocument.dni || ""
    medicalRecordDocument.patient.dob = patientDocument.dob || ""
    medicalRecordDocument.patient.fullname = patientDocument.fullname || ""
    medicalRecordDocument.patient.n_afiliado = n_afiliado || ""
    medicalRecordDocument.patient.obra_social = corporate || ""
    medicalRecordDocument.patient.sex = patientDocument.sex || ""
    medicalRecordDocument.patient.uid = (dependant_uid ? dependant_uid : uid) || ""
    medicalRecordDocument.patient.ws = patientDocument.ws || ""
  }
  if (studies) {
    medicalRecordDocument.studies = studies
  }
  if (fitnessCertificateDetails) {
    medicalRecordDocument.fitnessCertificateDetails = fitnessCertificateDetails
  }
  medicalRecordDocument.affiliateNumber = affiliateNumber || null

  const mrToUpdate = await MedicalRecordRepository.update(uid, assignationId, medicalRecordDocument)
  if (!mrToUpdate || typeof (mrToUpdate) == "boolean") {
    throw new NotFoundException(`[ MedicalRecords | close ] => Error closing medical record ${assignationId} from user ${uid}`)
  }
  return mrToUpdate
}

