import { IHealthInsurance } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { IBeneficiaries, IEmerResponse } from "../interfaces"
import { Logger } from "@nestjs/common"

/**
 * Si encuenta una coincidencia, selecciona el plan que ya tenga cargado el usuario y reemplaza la data con la respuesta de la API de EMER
 * Si no la encuentra, setea el primer plan devuelto por la API
 * @param emerResponse respuesta completa de la API de EMER
 * @param coverage coverage actual del usuario
 * @returns plan de la API de EMER
 */
export const selectCoveragePlan = (emerResponse: IBeneficiaries[], coverage: IHealthInsurance<Timestamp>): IEmerResponse => {
  let selectedPlan: IEmerResponse
  for (const emerRes of emerResponse) {
    if (emerRes.affiliateNumber === coverage.affiliate_id || emerRes.plan.toUpperCase().trim() === coverage.plan?.toUpperCase().trim()) {
      Logger.log(`[ Coverages | validate | selectCoveragePlan ] => Match found - Selecting plan ${emerRes.plan} with affiliateId ${emerRes.affiliateNumber} for user ${coverage.uid}`)
      selectedPlan = {
        affiliateNumber: emerRes.affiliateNumber,
        plan: emerRes.plan,
        lastUpdate: new Date(new Date().toISOString()),
        taxTreatment: emerRes.taxTreatment,
      }
      return selectedPlan
    } else {
      // Si no encontro una coincidencia, seteamos el primer plan encontrado en la API
      Logger.log(`[ Coverages | validate | selectCoveragePlan ] => Match not found - Selecting plan ${emerResponse[0].plan} with affiliateId ${emerResponse[0].affiliateNumber} for user ${coverage.uid}`)
      selectedPlan = {
        affiliateNumber: emerResponse[0].affiliateNumber,
        plan: emerResponse[0].plan,
        lastUpdate: new Date(new Date().toISOString()),
        taxTreatment: emerResponse[0].taxTreatment,
      }
      return selectedPlan
    }
  }
}
