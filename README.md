Aquí está la traducción al español argentino:

# Megalith

La aplicación monolítica de backend UMA construida con NestJS. Esta aplicación funciona como backend para múltiples aplicaciones de salud de UMA, incluyendo aplicaciones para médicos, pacientes y portal administrativo.

## Descripción General de la Arquitectura

Megalith está estructurada como una aplicación NestJS modular con una arquitectura claramente definida:

- **Apps**: La aplicación está dividida en múltiples sub-aplicaciones:
  - **doctor-app**: APIs para aplicaciones orientadas a médicos
  - **patient-app**: APIs para aplicaciones orientadas a pacientes
  - **portal-app**: APIs para el portal administrativo
  - **public**: APIs públicas que no requieren autenticación

- **Blocs**: Componentes de lógica de negocio organizados por dominio (turnos, pacientes, prestadores, etc.)
  - Cada bloc contiene casos de uso que representan operaciones específicas de negocio
  - Los blocs son consumidos por los controladores en las respectivas aplicaciones

- **Providers**: Integraciones con servicios externos (Audibaires, OSDE, Preserfar, etc.)

- **Utils**: Utilidades compartidas y funciones auxiliares

## Documentación de la API

La documentación de la API está disponible en https://docs.umasalud.com/ y está organizada por tipo de aplicación:

- Las especificaciones de la API están definidas en los siguientes archivos:
  - `api-spec-doctor.yaml`
  - `api-spec-patient.yaml`
  - `api-spec-portal.yaml`
  - `api-spec-public.yaml`

La documentación de Swagger también está disponible en el endpoint `/api` cuando se ejecuta la aplicación.

## Tecnologías Utilizadas

- **Framework**: NestJS 10.4
- **Base de datos**: PostgreSQL
- **Cache**: Redis
- **Autenticación**: Firebase
- **Documentación de API**: Swagger/OpenAPI
- **Testing**: Jest
- **Logs**: Pino
- **Integración en la nube**: Google Cloud Platform

## 🚀 Primeros Pasos

### Para Nuevos Desarrolladores

Si eres nuevo en el proyecto, sigue esta secuencia de documentación:

1. **📖 [ONBOARDING.md](./ONBOARDING.md)** - Guía completa de onboarding
2. **🛠️ [SETUP_GUIDE.md](./SETUP_GUIDE.md)** - Configuración detallada del entorno
3. **🔄 [DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)** - Flujo de desarrollo y mejores prácticas
4. **🎯 [EXAMPLES.md](./EXAMPLES.md)** - Ejemplos prácticos de implementación

### Instalación Rápida

```bash
# 1. Clonar e instalar dependencias
git clone [URL_DEL_REPO]
cd BE-megalith-main
npm install

# 2. Configurar base de datos (túnel SSH)
gcloud compute ssh sql-bastion-host --tunnel-through-iap --zone=us-central1-b --ssh-flag="-fN -L 5432:localhost:5432" --project=uma-development-ar

# 3. Crear archivo de secretos
touch secret_creds.json
# (Pedir credenciales al equipo)

# 4. Ejecutar aplicación
npm run start:dev
```

La aplicación estará disponible en [http://localhost:8081](http://localhost:8081)

### Verificación Rápida

```bash
# Verificar que todo funciona
curl http://localhost:8081/api  # Debe mostrar Swagger
npm run test                    # Ejecutar tests
npm run lint                    # Verificar código
```

## Desarrollo

### Scripts Disponibles

- `npm run start`: Iniciar el servidor con variables de entorno
- `npm run start:dev`: Iniciar el servidor en modo desarrollo con recarga automática
- `npm run build`: Compilar la aplicación
- `npm run lint`: Revisar el código
- `npm run lint:fix`: Arreglar problemas de linting
- `npm run test`: Ejecutar tests
- `npm run test:cov`: Ejecutar tests con cobertura
- `npm run sql-proxy`: Ejecutar proxy SQL para desarrollo local

### Flujo de Git

Seguí este flujo de trabajo para el desarrollo:

1. Creá una rama de feature desde `main`
2. Creá un PR desde tu rama a `beta` (se despliega en desarrollo) para desarrollo y pruebas
3. Creá un PR desde tu rama a `next` (se despliega en staging) para pruebas de integración
4. Creá un PR desde `next` a `main` (se despliega en producción) para despliegue en producción

Notas importantes:
- ¡Nunca crees PRs desde `beta` a `next` o desde `beta` a `main`!
- Cada lunes, `beta` es sobrescrito con el contenido de `next`

### Changeset y Actualización de Versiones

Megalito utiliza el sistema de changeset para controlar las versiones y automatizar los despliegues. Para que los PRs a main se desplieguen correctamente, es necesario crear un changeset que documente los cambios realizados.

#### Creación de un Changeset

1. **Instala la CLI de changesets** (si no la tienes instalada globalmente):
```bash
npm install -g @changesets/cli
```

2. **Genera un nuevo changeset** en la raíz del proyecto:
```bash
yarn changeset
```
o
```bash
npx changeset
```

3. **Sigue el proceso interactivo** que te solicitará:
   - Seleccionar el tipo de cambio (patch, minor, major) según la convención de versionado semántico
   - Escribir una descripción breve pero informativa del cambio realizado

4. **Incluye el archivo generado** en la carpeta `.changeset` en tu commit

#### Tipos de Cambios

- **patch**: Correcciones de errores y cambios menores que no afectan la funcionalidad existente (ejemplo: 1.0.0 → 1.0.1)
- **minor**: Adición de nuevas funcionalidades de forma compatible (ejemplo: 1.0.0 → 1.1.0)
- **major**: Cambios que rompen la compatibilidad con versiones anteriores (ejemplo: 1.0.0 → 2.0.0)

#### Proceso de Despliegue con Changeset

1. Crea tu rama de feature y realiza tus cambios
2. Genera el changeset como se indicó anteriormente
3. Asegúrate de incluir el archivo generado en tu commit
4. Crea el PR a la rama correspondiente (según el Git Flow definido)
5. Al fusionar a main, el sistema de CI/CD detectará el changeset y ejecutará el despliegue automáticamente

El archivo `.changeset/config.json` ya está configurado correctamente y no necesita modificaciones para el uso diario.

## Problemas Frecuentes

- **ECONNREFUSED en el puerto 5432**: Verificá que la conexión SQL esté funcionando
- **ECONNREFUSED en el puerto 6379**: Instalá Redis (`sudo apt install redis`)

## Variables de Entorno

Las variables de entorno se cargan a través del script `load_server.sh` al iniciar la aplicación. Por razones de seguridad, se eliminan cuando el proceso del servidor finaliza.

Cuando hagas cambios en las variables de entorno, necesitás reiniciar el servidor para cargar los nuevos valores.

## Estructura del Proyecto

  - `/src`: Código fuente
  - `/app.module.ts`: Módulo principal de la aplicación
  - `/main.ts`: Punto de entrada de la aplicación
  - `/blocs`: Componentes de lógica de negocio
  - `/doctor-app`: Controladores y servicios de API para médicos
  - `/patient-app`: Controladores y servicios de API para pacientes
  - `/portal-app`: Controladores y servicios de API para portal administrativo
  - `/public`: APIs públicas
  - `/providers`: Integraciones con servicios externos
  - `/utils`: Utilidades compartidas
  - `/test`: Tests end-to-end

### Notas
- Hay 1 sólo comando para levantar el server en local, es npm run start. Este comando lo que hace es primero correr el script que carga todas las variables de entorno y después corre nest start.
- No se puede correr con prestart pq nest sino no lee las var de entorno y rompe.
- Al cortar el proceso del server (CTRL + C), las variables de entorno se eliminan por motivos de seguridad. Por eso es necesario cortar e iniciar de nuevo cuando se hacen cambios
- La rama se llama task/secretScript, si tienen alguna rama en uso se pueden clonar estos cambios, y sino ya se está pasando a los 3 ambientes para cuando creen una rama nueva.
- Hay un archivo llamado secret_creds.json. Ahí se deben escribir todos los secrets que se usan, si no se escriben en ese archivo, no van a ser leidos.
