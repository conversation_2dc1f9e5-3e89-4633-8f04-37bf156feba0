import { base } from "src/utils/airtable/airtableConfiguration"

export const getCorporates = async () => {
  const table = base("empresa")
  const records = await table.select({
    fields: ["id", "empresa"],
    filterByFormula: "AND({contrato} = 'Si', {cantidad activos} > 0)",
  }).all()

  const corporates = records.map(record => {
    record.fields.id = record.id
    return record.fields
  })

  return corporates
}