import { NotFoundException } from "@nestjs/common"
import { ProviderRepository } from "@umahealth/repositories"
import { IProviderWithMatricula } from "../entities/IProviderWithMatricula"
import { Timestamp } from "@google-cloud/firestore"
import { getCurrentLicense } from "src/blocs/coverage/utils/functions"

export const getByUid = async (providerId: string) : Promise<IProviderWithMatricula<Timestamp>> => {
  const healthProvider = await ProviderRepository.getByProviderUid(providerId)
  if (!healthProvider) throw new NotFoundException(`Could not find provider with id ${providerId}`)
  const licenses = await ProviderRepository.getLicenses(healthProvider.uid)
  const currentLicense = getCurrentLicense(licenses)
  if (!currentLicense) throw new NotFoundException(`No license found for provider with id ${providerId}`)
  const providerWithMatricula: IProviderWithMatricula<Timestamp> = {
    ...healthProvider,
    matricula: currentLicense.number.toString()
  }
  return providerWithMatricula
}
