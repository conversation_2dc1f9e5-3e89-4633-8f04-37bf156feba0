name: Docker Build on Release

on:
  release:
    types:
      - published

concurrency:
  group: cloud-deploy-release_megalith-svc
  cancel-in-progress: false

jobs:
  build-prd:
    uses: umahealth/ci-workflows/.github/workflows/docker-build.yaml@main
    with:
      image-name: megalith
      docker-tag: ${{ github.ref_name }}
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}

  deploy-release:
    needs: build-prd
    uses: umahealth/ci-workflows/.github/workflows/cloud-deploy-release.yaml@main
    with:
      image-name: megalith
      release-version: ${{ github.ref_name }}
      delivery-pipeline: megalith-service-pipeline
      github-release-url: ${{ github.event.release.html_url }}
    secrets:
      github-token: ${{ secrets.NPM_READ_TOKEN }}
      
  # slack-notification-prd-ar:
  #   needs: build-prd
  #   uses: umahealth/ci-workflows/.github/workflows/send-slack-block.yaml@main
  #   with:
  #     channel-id: C03RE892B5H
  #     message-header: "megalith - New Version: ${{ github.event.release.tag_name }}"
  #     message-body: "${{ github.event.release.body }}"
  #   secrets:
  #     slack-token: ${{ secrets.SLACK_BOT_TOKEN }}