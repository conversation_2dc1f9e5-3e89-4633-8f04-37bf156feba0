import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { IPatient } from "@umahealth/entities"
import { PatientRepository } from "@umahealth/repositories"

export const GetPatientByPersonalDataUseCase = async (dataType: "dni" | "ws" | "email", value: string): Promise<IPatient<Timestamp>[]> => {
  let patientDocs = []
  switch (dataType) {
  case "dni": {
    patientDocs = await PatientRepository.getPatientByDni(value)
    break
  }
  case "ws": {
    patientDocs = await PatientRepository.getPatientByWs(value)
    break
  }
  case "email": {
    patientDocs = await PatientRepository.getPatientByEmail(value)
    break
  }

  default: {
    throw new NotFoundException(`Could not find patient by ${dataType}`)
    break
  }
  }
  return patientDocs
}
