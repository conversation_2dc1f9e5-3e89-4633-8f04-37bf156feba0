import { countries, IOnDemandAppointment } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { AppointmentRepository, IDocumentList } from "@umahealth/repositories"
import { InternalServerErrorException, Logger } from "@nestjs/common"

export const closeOnDemandUseCase = async (
  assignationId: string,
  country: countries,
  appointment: Partial<IOnDemandAppointment<Timestamp>>
): Promise<IDocumentList<Partial<IOnDemandAppointment<Timestamp>>>> => {
  const fullAssignation = await AppointmentRepository.getByAssignationId<IOnDemandAppointment<Timestamp>>("onDemand", country, assignationId)
  appointment.state = "DONE"
  appointment.timestamps = {
    ...fullAssignation.timestamps,
    dt_close: Timestamp.now()
  }

  const updatedOnDemandAppointment = await AppointmentRepository.update<IOnDemandAppointment<Timestamp>>("onDemand", country, assignationId, appointment)

  if (!updatedOnDemandAppointment) {
    Logger.error(`[ ${closeOnDemandUseCase.name} ] => Error updating onDemand appointment ${assignationId}`)
    throw new InternalServerErrorException(`[ ${closeOnDemandUseCase.name} ] => Error updating onDemand appointment ${assignationId}`)
  }
  return updatedOnDemandAppointment
}