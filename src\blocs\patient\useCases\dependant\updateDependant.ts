import { IPatient } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/storage"
import { BadRequestException, NotFoundException, UnauthorizedException } from "@nestjs/common"
import { Timestamp } from "@google-cloud/firestore"
import { DependantRepository, HealthInsuranceRepository } from "@umahealth/repositories"
import { IPortalLocals } from "src/utils/middleware/portalAuthorizedCorporatesMiddleware"


export const UpdateDependantUseCase = async (uid: string, owner_uid: string, data: Partial<IPatient<Timestamp>>, locals?: IPortalLocals): Promise<IDocumentList<Partial<IPatient<Timestamp>>>[]> => {
  const documentArray: IDocumentList<Partial<IPatient<Timestamp>>>[] = []
  let dependant: IPatient<Timestamp> = await DependantRepository.getByUidFromDependant(uid)
  if (!dependant) {
    dependant = await DependantRepository.getByUid(owner_uid, uid)
    if (!dependant) throw new NotFoundException(`[ Patient | updateDependant ] => Dependant not found - UID ${uid} OwnerUID ${owner_uid}`)
  }
  let sameCorpo = false

  if (locals) {
    const activeCorporate = await HealthInsuranceRepository.getDepentantPrimary(uid)
    if (!activeCorporate) throw new BadRequestException(`[updateDependantUseCase] => active corporate has not been found | uid ${uid}`)

    if(!locals.corporatesIdsAndNames) throw new BadRequestException("[updateDependantUseCase] => res.locals is empty")

    if(locals.corporatesIds.includes("UMA") || locals.superadmin) sameCorpo = true
    else {
      locals.corporatesIdsAndNames.forEach(corpo => {
        if (corpo.id === activeCorporate.id || corpo.value === activeCorporate.id) sameCorpo = true
      })
    }

    if (sameCorpo) {
      documentArray.push(await DependantRepository.newPathUpdate(uid, { ...dependant, ...data }))
      // Updateamos este documento temporalmente hasta que se deje de utilizar la ruta vieja de dependants
      if (dependant.core_id !== null) {
        const temporalDocument = await DependantRepository.update(dependant.core_id, uid, { ...dependant, ...data })
        documentArray.push(temporalDocument)
      }
    } else throw new UnauthorizedException(`[updatePatientUseCase] => patient ${uid} does not belong to your corporate`)
  } else {
    documentArray.push(await DependantRepository.newPathUpdate(uid, { ...dependant, ...data }))
    // Updateamos este documento temporalmente hasta que se deje de utilizar la ruta vieja de dependants
    if (dependant.core_id !== null) {
      const temporalDocument = await DependantRepository.update(dependant.core_id, uid, { ...dependant, ...data })
      documentArray.push(temporalDocument)
    }
  }

  return documentArray
}

