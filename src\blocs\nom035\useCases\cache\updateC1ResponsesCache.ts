import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getC1Responses } from "../reports/getC1Responses"

export const updateC1ResponsesCache = async (redis: Redis, corporateId: string) => {
  Logger.log(`[Nom035 | updateC1ResponsesCache] Updating C1 responses for corporate: ${corporateId}`)

  const c1Responses = await getC1Responses(corporateId)

  const redisKeyPrefix = `corporate:${corporateId}`
  const redisExpiration = 86400 // 24 hours

  await redis.set(`${redisKeyPrefix}:form:c1:responses`, JSON.stringify(c1Responses), "EX", redisExpiration)
}