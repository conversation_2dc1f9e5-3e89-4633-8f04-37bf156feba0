import { Timestamp } from "@google-cloud/firestore"
import { countries, IOrder } from "@umahealth/entities"
import { IDocumentList, OrdersRepository } from "@umahealth/repositories"


export const updateOrder = async (orderId: string, country: countries, data: Partial<IOrder<Timestamp>>): Promise<IDocumentList<Partial<IOrder<Timestamp>>>> => {
  return await OrdersRepository.update(orderId, country, data)
}
