import { Timestamp } from "@google-cloud/firestore"
import { INotification } from "@umahealth/entities"
import { IDocumentList, NotificationRepository } from "@umahealth/repositories"

export async function cancelNotificationsByAssignationId(assignation_id: string, patientUid: string): Promise<IDocumentList<Partial<INotification<Timestamp>>>[] | boolean> {
  const notifications = await NotificationRepository.getNotificationByAssignationId(assignation_id, "appointments")
  const notificationDocuments: IDocumentList<Partial<INotification<Timestamp>>>[] = []
  if (notifications.length === 0) return false

  const activeNotification = notifications.filter(notification => notification.uidReceiver === patientUid)
  if (!activeNotification.length) return false
  activeNotification.forEach(async notification =>{
    if (["cancelled", "no_reminders_left"].includes(notification.next_send)) return false
    notification.next_send = "cancelled"
    notificationDocuments.push(await NotificationRepository.updateNotification(notification.id, notification, "appointments"))
  })
  return notificationDocuments
}
