import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { appointmentServices, IMedicalRecord } from "@umahealth/entities"
import { IDocumentList, MedicalRecordRepository } from "@umahealth/repositories"

export async function patientConfirmCloseMr(
  assignationId: string,
  uid: string,
  service: appointmentServices,
): Promise<IDocumentList<Partial<IMedicalRecord<Timestamp>>>> {
  const dt_cierre = Timestamp.now()
  const medicalRecordDocument = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  if(!medicalRecordDocument) throw new NotFoundException(`[ MedicalRecords | close ] => Medical record not found. Service: ${service} Assignation Id: ${assignationId} Uid: ${uid}`)
  const {timestamps} = medicalRecordDocument
  medicalRecordDocument.timestamps = {
    ...timestamps,
    dt_close: Timestamp.fromDate(dt_cierre.toDate())
  }
  const mrToUpdate = await MedicalRecordRepository.update(uid, assignationId, medicalRecordDocument)
  if (!mrToUpdate || typeof (mrToUpdate) == "boolean") {
    throw new NotFoundException(`[ MedicalRecords | close ] => Error closing medical record ${assignationId} from user ${uid}`)
  }
  return mrToUpdate
}

