import { MedicalStudyData, MedicalStudyType } from "src/patient-app/medicalStudy/medicalStudy.interface"
import { deleteMedicalStudyUseCase } from "./useCases/deleteMedicalStudyUseCase"
import { createMedicalStudyUseCase } from "./useCases/createMedicalStudy"
import { IMedicalStudy } from "@umahealth/entities"
import { Injectable } from "@nestjs/common"

@Injectable()
export class MedicalStudiesBloc {
  async create(uid: string, newData: MedicalStudyData, path: string, type: MedicalStudyType) {
    return createMedicalStudyUseCase(uid, newData, path, type)
  }

  async delete(uid: string, id: string) {
    return deleteMedicalStudyUseCase(uid, id)
  }
}
