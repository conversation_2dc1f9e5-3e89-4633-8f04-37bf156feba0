import { InternalServerErrorException, Logger } from "@nestjs/common"
import axios, { AxiosError } from "axios"
import { IAffiliatedValidation, IIomaValidationError, IPatientIomaGetAfiSex } from "../interfaces"
import { iomaErrorMessages, sexConvertion } from "../../utils/functions"
import { getIomaJWT } from "src/utils/getIomaJWT"

// Parse a date string in format DD/MM/YYYY and check if it's before today
export const isFechaCeseBefore = ( fechaCese: string | null | undefined): boolean => {
  if (!fechaCese) return false

  // Parse the date from DD/MM/YYYY format
  const [day, month, year] = fechaCese.split("/").map(Number)
  const fechaCeseDate = new Date(year, month - 1, day) // month is 0-indexed in JS Date

  // Compare with today
  const today = new Date()
  today.setHours(0, 0, 0, 0) // Set to start of day for fair comparison

  return fechaCeseDate < today
}

// Check if a date string in format DD/MM/YYYY is within the specified number of days from today
export const isFechaIngresoClose = (fechaIngreso: string | null | undefined, daysThreshold: number): boolean => {
  if (!fechaIngreso) return false

  // Parse the date from DD/MM/YYYY format
  const [day, month, year] = fechaIngreso.split("/").map(Number)
  const fechaIngresoDate = new Date(year, month - 1, day) // month is 0-indexed in JS Date

  // Compare with today
  const today = new Date()
  today.setHours(0, 0, 0, 0) // Set to start of day for fair comparison

  // Calculate difference in days
  const diffTime = Math.abs(today.getTime() - fechaIngresoDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays <= daysThreshold
}


export const getIomaPatient = async (Username: string, Password:string, dni: string, sexo: number, affiliate_id?: string): Promise<IPatientIomaGetAfiSex> => {

  if(sexo === undefined || sexo === null){
    Logger.log(`Trying to find patient with DNI: ${dni} and SEX: F`)

    const sexResponseF = await axios.get(`https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDoc?Usuario=${Username}&Password=${Password}&NumeroDocumento=${dni}&Sexo=${2}`, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni: ${dni} - sexo: ${sexo}`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })

    if(sexResponseF.data.CodigoMensaje !== "5"){
      return sexResponseF.data
    }

    const sexResponseM = await axios.get(`https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDoc?Usuario=${Username}&Password=${Password}&NumeroDocumento=${dni}&Sexo=${1}`, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni: ${dni} - sexo: ${sexo}`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })

    if(sexResponseM.data.CodigoMensaje !== "5"){
      return sexResponseM.data
    }

    const sexResponseX = await axios.get(`https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDoc?Usuario=${Username}&Password=${Password}&NumeroDocumento=${dni}&Sexo=${3}`, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni: ${dni} - sexo: ${sexo}`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })

    return sexResponseX.data

  }else {
    const res = await axios.get(`https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDoc?Usuario=${Username}&Password=${Password}&NumeroDocumento=${dni}&Sexo=${sexo}`, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni: ${dni} - sexo: ${sexo}`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })
    if(res.data.CodigoMensaje !== "5" || !affiliate_id){
      return res.data
    }
    return await getIomaPatientByNumAfi(Username, Password, affiliate_id)
  }
}

export const getIomaPatientByNumAfi = async (Username: string, Password:string, NumeroAfiliado:string): Promise<IPatientIomaGetAfiSex> => {


  const res = await axios.get(`https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiNroAfi?Usuario=${Username}&Password=${Password}&NumeroAfiliado=${NumeroAfiliado}`, { headers: { "Content-Type":"application/json" }}).catch(error => {
    Logger.error(`Error getting patient data from IOMA with numAfi: ${NumeroAfiliado}`)
    throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
  })

  return res.data
}

export const iomaCoverageValidation = async (dni:string, sex: string, token: number): Promise<IAffiliatedValidation> => {
  const jwt = await getIomaJWT()

  const requestBody = {
    sexo: sexConvertion(sex),
    documento: dni,
    token
  }

  const handleError = (err: AxiosError<IIomaValidationError>) => {
    const{codigoMensaje} = err.response.data
    if(iomaErrorMessages[codigoMensaje]){
      Logger.error(`ERROR IOMA COVERAGE VALIDATION: ${iomaErrorMessages[codigoMensaje] }, TOKEN: ${token} DNI: ${dni}` )
      throw new InternalServerErrorException(`ERROR IOMA COVERAGE VALIDATION: ${iomaErrorMessages[codigoMensaje]} - error: ${err.message}`)
    }
    Logger.error(`Unknown error on validation patient Token from IOMA with DNI and Token. DNI: ${dni} TOKEN: ${token}. Error: ${err}`)
    throw new InternalServerErrorException(`Error on validation patient Token from IOMA API - DNI: ${dni} TOKEN: ${token} - error: ${err}`)
  }

  try {
    const res = await axios.post(
      "https://sistemas.ioma.gba.gov.ar/consumostest/api/Validaciones", requestBody, { headers: { "Content-Type":"application/json", "Authorization": jwt }}
    )
    return res.data
  } catch (error) {
    handleError(error)
  }
}
