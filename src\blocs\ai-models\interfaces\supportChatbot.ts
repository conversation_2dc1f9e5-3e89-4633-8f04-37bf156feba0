export type Step = "first" | "second";

export interface BaseRequest<T> {
	step: Step;
	conversation_id: string;
	data: T;
}

export interface SupportChatbotProps {
	text: string;
	conversation_id: string;
}

export interface SupportChatbotBody {
	text: string;
}

export type SupportChatbotRequest = BaseRequest<SupportChatbotBody>;

export interface CreateZendeskTicketProps {
	uid: string
	conversation_id: number
	descripcion: string;
	titulo: string;
	email: string;
	nombre_completo: string;
	dni: string;
	obra_social: string;
	telefono: string;
}

export interface CreateZendeskTicketBody {
	uid: string
	descripcion: string;
	titulo: string;
	email: string;
	nombre_completo: string;
	dni: string;
	obra_social: string;
	telefono: string;
}

export type CreateZendeskTicketRequest = BaseRequest<CreateZendeskTicketBody>;

export interface SupportChatbotResponse {
	output: string;
}

export interface CreateZendeskTicketResponse {
	output: string;
	// Puedes agregar más propiedades según la respuesta del servidor
}
