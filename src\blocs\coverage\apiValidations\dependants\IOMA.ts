import { InternalServerErrorException, Logger } from "@nestjs/common"
import axios from "axios"
import { IPatientIoma } from "../interfaces"

export const PostIomaPatientWithCargas = async (Username: string, Password:string, NumeroDocumento: string, Sexo: number ): Promise<IPatientIoma> => {
  const body = {
    Usuario: Username,
    Password,
    NumeroDocumento,
    Sexo
  }

  if(Sexo === undefined || Sexo === null){
    Logger.log(`Trying to find patient with DNI: ${NumeroDocumento} and SEX: F`)
    const bodyF = {
      Usuario: Username,
      Password,
      NumeroDocumento,
      Sexo: 2
    }

    const sexResponseF = await axios.post("https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDocWithCargas", bodyF, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni ${NumeroDocumento} and sexo 2`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })

    if(sexResponseF.data.CodigoMensaje !== "5"){
      return sexResponseF.data
    }

    Logger.log(`Trying to find patient with DNI: ${NumeroDocumento} and SEX: M`)

    const bodyM = {
      Usuario: Username,
      Password,
      NumeroDocumento,
      Sexo: 1
    }

    const sexResponseM = await axios.post("https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDocWithCargas", bodyM, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni ${NumeroDocumento} and sexo 1`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })

    if(sexResponseM.data.CodigoMensaje !== "5"){
      return sexResponseM.data
    }

    Logger.log(`Trying to find patient with DNI: ${NumeroDocumento} and SEX: X`)

    const bodyX = {
      Usuario: Username,
      Password,
      NumeroDocumento,
      Sexo: 3
    }

    const sexResponseX = await axios.post("https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDocWithCargas", bodyX, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni ${NumeroDocumento} and sexo 3`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })

    return sexResponseX.data

  }else {

    const res = await axios.post("https://api.ioma.gba.gob.ar/RegistroPrestacional/Servicio.svc/json/getAfiSexDocWithCargas", body, { headers: { "Content-Type":"application/json" }}).catch(error => {
      Logger.error(`Error getting patient data from IOMA with dni ${NumeroDocumento} and sexo ${Sexo}`)
      throw new InternalServerErrorException(`Error getting patient data from API - error: ${error}`)
    })

    return res.data
  }

}
