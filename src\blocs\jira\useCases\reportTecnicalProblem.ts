import { Logger } from "@nestjs/common"
import axios from "axios"
import { ITecnicalProblem, technicalIssueEnum } from "src/doctor-app/online/appointments.interfaces"




interface IJiraCredentials {
  path: string
  email: string
  token: string
}

export const reportTechnicalProblems = async (problems: ITecnicalProblem, assignationId: string, providerUid: string) => {
  const jiraCredentials: IJiraCredentials = JSON.parse(process.env.JIRA_CREDENTIALS)
  problems.tags.map( async (tag: keyof typeof technicalIssueEnum) => {
    if (technicalIssueEnum[tag]) {
      try {
        const res = await axios.post(`${jiraCredentials.path}/rest/api/3/issue/${technicalIssueEnum[tag]}/comment`,
          tag !== "other" ? {
            "body": {
              "type": "doc",
              "version": 1,
              "content": [
                {
                  "type": "paragraph",
                  "content": [
                    {
                      "type": "text",
                      "text": `Reporte de problema técnico \nConsulta: ${assignationId} \nDoctor: ${providerUid} \nProblema: ${tag}`
                    }
                  ]
                }
              ]
            }
          } : {
            "body": {
              "type": "doc",
              "version": 1,
              "content": [
                {
                  "type": "paragraph",
                  "content": [
                    {
                      "type": "text",
                      "text": `Reporte de problema técnico \nConsulta: ${assignationId} \nDoctor: ${providerUid} \nProblema: ${problems?.otherIssue || ""}`
                    }
                  ]
                }
              ]
            }
          },
          {
            headers: {
              "Content-Type": "application/json",
            },
            auth: {
              username: jiraCredentials.email,
              password: jiraCredentials.token,
            },
          }
        )
        Logger.log(`[reportTechnicalProblems] => Technical problem reported: ${technicalIssueEnum[tag]}`)
        return res.data
      } catch (error) {
        Logger.error(`[reportTechnicalProblems] => Error reporting technical problem: ${JSON.stringify(error)}`)
        return { sent: false, error: JSON.stringify(error) }
      }
    }
  })
}
