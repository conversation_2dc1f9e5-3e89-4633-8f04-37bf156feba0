import { IUpdateNewActiveServices } from "../../patient.bloc.interface"
import { PatientRepository } from "@umahealth/repositories"
import { call } from "@umahealth/entities"

export const updateNewActiveServicesUseCase = async(data: IUpdateNewActiveServices, type: string) => {
  const {
    activeUid,
    assignationId,
    isDependant,
    room,
    token,
    cuit,
    appointmentPath,
    uid
  } = data
  const newCallData: call = {
    calling: true,
    assignation_id: assignationId,
    assignationPath: appointmentPath,
    cuit,
    dependant: isDependant ? activeUid : null,
    type,
    activeUid,
    room,
    token,
  }
  const response = await PatientRepository.createCall(uid, newCallData)
  return response
}
