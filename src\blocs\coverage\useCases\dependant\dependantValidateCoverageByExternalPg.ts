import {  IHealthInsurance } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { Externals } from "@umahealth/sql-models"

export const DependantValidateCoverageByExternalPgUseCase = async (healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> => {
  try{

    const data = await Externals.findOne({ where: { affiliateNumber: healthInsurance.affiliate_id, active: true}, raw: true})
    if(data){
      return true
    }else{
      return false
    }

  } catch (err) {
    throw new InternalServerErrorException(`[ Coverages | dependant | validateByExternalPg ] => Error validating coverage by externalPg with coverage: ${JSON.stringify(healthInsurance)}`)
  }
}
