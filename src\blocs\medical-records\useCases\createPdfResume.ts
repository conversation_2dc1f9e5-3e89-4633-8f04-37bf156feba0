import { InternalServerErrorException } from "@nestjs/common"
import { IMedicalRecord } from "@umahealth/entities"
import { TDocumentDefinitions } from "pdfmake/interfaces"
import { createResumeTemplate } from "../templates/resumeTemplate"
import { Timestamp } from "@google-cloud/firestore"
import * as vfsFonts from "pdfmake/build/vfs_fonts"
import * as PdfPrinter from "pdfmake/build/pdfmake"
import * as fs from "fs"
import { waitForFileExistence } from "src/utils/waitForFileExistence"
import { uploadLocalFileStorage } from "src/utils/files"
import { IProviderWithMatricula } from "src/blocs/health-providers/entities/IProviderWithMatricula"

export async function createPdfResume(mr: IMedicalRecord<Timestamp>,provider:IProviderWithMatricula<Timestamp>, file_path: string){
  let docDefinition: TDocumentDefinitions
  // localPath temporal storage
  const localPath = `./uploads/${mr.id}.pdf`
  try {
    // creation resumeTemplate
    docDefinition = await createResumeTemplate(mr, provider)
  } catch (err) {
    throw new InternalServerErrorException(`[createPdfResumeUseCase] => error creating Resume from template: ${err}`, err)
  }

  // set fonts
  const Roboto = {
    normal: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Regular.ttf" ], "base64"),
    bold: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Medium.ttf" ], "base64"),
    italics: Buffer.from(vfsFonts.pdfMake.vfs[ "Roboto-Italic.ttf" ], "base64"),
    bolditalics: Buffer.from(
      vfsFonts.pdfMake.vfs[ "Roboto-MediumItalic.ttf" ],
      "base64"
    ),
  }
  // creation localPdf
  try {
    const pdfDoc = PdfPrinter.createPdf(docDefinition, {}, { Roboto }).getStream()
    const createPdfPromise = await new Promise<Buffer>((resolve, reject) => {
      try {
        const chunks: Uint8Array[] = []
        pdfDoc.on("data", (chunk: Uint8Array) => chunks.push(chunk))
        pdfDoc.on("end", () => resolve(Buffer.concat(chunks)))
        pdfDoc.pipe(fs.createWriteStream(localPath))
        pdfDoc.end()
      } catch (err) {
        reject(err)
      }
    })

    await waitForFileExistence(localPath)
    // upload pdf file
    const storageUrl = await uploadLocalFileStorage(localPath, file_path)

    return { pdf: createPdfPromise, url: storageUrl }
  } catch (err) {
    throw new InternalServerErrorException(`[createPdfResumeUseCase] => failed to generate pdf. ${err}`, err)
  }
}