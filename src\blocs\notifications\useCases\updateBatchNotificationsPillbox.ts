import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common"
import { INotification, NotificationSend, countries } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { ParametersRepository } from "@umahealth/repositories"
import * as moment from "moment"
import { eventData } from "src/patient-app/pillbox/pillbox.interface"

export async function updateNotificationsPillbox(
  campaign: string,
  country: countries,
  dt_end: boolean,
  notificationsData: eventData[],
  receiverUid: string,
  uid: string,
  notificationId : string
): Promise<IDocumentList<INotification<Timestamp>>> {

  const templateDocument = await ParametersRepository.getPillboxCampaigns()
  if (!templateDocument) throw new NotFoundException("[ Notifications | updateNotificationsPillbox ] => Pillbox campaign not found.")

  const allNotifications: NotificationSend[] = []
  const notificationDates = []

  for (const notification of notificationsData) {
    const { date, template, type, params } = notification
    switch (type) {

    case ("sms"): {
      let body = templateDocument["pillboxNotification"]["FCM_sms"]
      if (params && params.length) {
        for (const key in params) {
          body = body.replace(`{{${key}}}`, params[key])
        }
      }
      allNotifications.push({
        date,
        sent: false,
        template,
        body,
        type
      })
      notificationDates.push(Number(date))
      break
    }
    case ("email"): {
      let body = templateDocument["pillboxNotification"]["FCM_email"]
      if (params && params.length) {
        for (const key in params) {
          body = body.replace(`{{${key}}}`, params[key])
        }
      }
      allNotifications.push(
        {
          date,
          sent: false,
          template,
          body,
          type
        })
      notificationDates.push(Number(date))
      break
    }
    default: {
      Logger.error(`[ Notifications | updateNotificationsPillbox ] => ${type} notification has not been implemented yet`)
      break
    }
    }
  }

  const notificationsBatch = {
    assignation_id: "",
    uidReceiver: receiverUid,
    uid: uid,
    send: allNotifications,
    country: country,
    campaign,
    next_send: Math.min(...notificationDates.filter(el => Number(el) > Number(moment().tz("America/Argentina/Buenos_Aires").format("YYYYMMDDHHmm")))).toString(),
    forever: dt_end,
  }

  return {
    path: `events/notifications/${campaign}/${notificationId}`,
    data: notificationsBatch,
    method: "update"
  }
}