import { GenerateContentRequest, GenerateContentResult, GenerativeModelPreview } from "@google-cloud/vertexai"
import { Logger } from "@nestjs/common"

/**
 * Wait before retrying
 * @param i actual attempt
 * @param retries max attempts
 * @param delay waiting time between attempts in ms
 * @returns promise void
 */
const wait = async (i: number, retries: number, delay: number) => {
  if (i < retries) {
    await new Promise(resolve => setTimeout(resolve, delay))
  }
}

export async function retryRequest<T> (
  model: GenerativeModelPreview,
  method: "generateContent" | "generateContentStream" | "startChat" | "countTokens",
  req: GenerateContentRequest,
  retries = 3,
  delay = 1000,
): Promise<T> {
  let err
  for (let i = 1; i <= retries; i++) {
    try {
      const response = await model[method](req) as T

      if (method === "generateContent") {
        const generateContentResponse = (response as GenerateContentResult).response.candidates[0]
        if (!generateContentResponse.content.parts?.length) {
          err = `Vertex did not provide a text response. FinishReason: ${generateContentResponse.finishReason}.`
          Logger.error(`Attempt ${i} failed. FinishReason: ${generateContentResponse.finishReason}.`)
          await wait(i, retries, delay)
          continue
        }
      }

      return response
    } catch (error) {
      err = error
      Logger.error(`Attempt ${i} failed: ${error.message}`)
      await wait(i, retries, delay)
    }
  }
  throw new Error(`Request failed after ${retries} retries. Err: ${err}`)
}