import { IDocumentList, MedicalRecordRepository } from "@umahealth/repositories"
import { IMedicalRecord, IPatient, paymentData, IFitnessCertificateAppointment, MedicalRecord } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { convertDateToTimezoneString } from "@umahealth/time"
import { toZonedTime } from "date-fns-tz"
import { parse } from "date-fns"

export const CreateFitnessCertificateUseCase = async (
  assignation: IFitnessCertificateAppointment<Timestamp>,
  patient: IPatient<Timestamp>,
  paymentData: paymentData
): Promise<IDocumentList<IMedicalRecord<Timestamp>>> => {
  try {
    const medicalRecord = new MedicalRecord<Timestamp>()

    medicalRecord.affiliateNumber = assignation.affiliateNumber || null
    medicalRecord.affiliateType = assignation.affiliateType || null
    medicalRecord.assignation_id = assignation.assignation_id
    medicalRecord.att_category = "MI_ESPECIALISTA"
    medicalRecord.complain = null
    medicalRecord.complain_text = null
    medicalRecord.corporate = assignation.patient?.corporate || "UMA AR"
    medicalRecord.country = patient.country || assignation.country || "AR"
    medicalRecord.created_dt = convertDateToTimezoneString(assignation.timestamps.dt_create.toDate(), "YYYY-MM-DD HH:mm:ss")
    medicalRecord.dt_cierre = null
    medicalRecord.especialidad = "aptofisico"
    medicalRecord.event_type = "online"
    medicalRecord.geo = {
      geohash: patient.geohash || null,
      lat: patient.lat || null,
      lon: patient.lon || null
    }
    medicalRecord.incidente_id = assignation.assignation_id
    medicalRecord.mr_preds = {
      abort_description: null,
      destino_final: "",
      diagnostico: "",
      epicrisis: "",
      gduh: null,
      motivos_de_consulta: assignation.motivos_de_consulta || "",
      observaciones: null,
      pre_clasif: ""
    }
    medicalRecord.mr = {
      alertas: null,
      destino_final: "",
      diagnostico: "",
      dt: null,
      dt_cierre: null,
      epicrisis: "",
      motivos_de_consulta: assignation.motivos_de_consulta || "",
      observaciones: null,
      ordenes: [],
      receta: [],
      reposo: null,
      receta_ref: null,
      tratamiento: null,
      specialist_referral: null
    }
    medicalRecord.patient = {
      address: patient.address || "",
      antecedentes: "",
      corporate_norm: assignation.patient?.corporate || "UMA AR",
      country: patient.country || assignation.country || "AR",
      dependant_uid: typeof assignation.dependantUid === "string" ? assignation.dependantUid : null,
      dni: patient.dni || "",
      dob: patient.dob || "",
      fullname: patient.fullname || "",
      n_afiliado: "",
      obra_social: assignation.patient?.corporate || "UMA AR",
      sex: patient.sex || "",
      uid: patient.id || patient.uid,
      ws: patient.ws || ""
    }
    medicalRecord.payment_data = paymentData
    medicalRecord.provider = {
      cuit: assignation.doctor?.cuit || "",
      especialidad: assignation.especialidad,
      fullname: assignation.doctor?.fullname || "",
      uid: assignation.doctor?.uid,
      ws: assignation.doctor?.ws || ""
    }
    medicalRecord.timestamps = {
      dt_assignation: assignation.timestamps.dt_assignation || Timestamp.fromDate(toZonedTime(parse(`${assignation.attentionDate} ${assignation.attentionTime}`, "yyyy-MM-dd HH:mm", new Date()), "America/Argentina/Buenos_Aires")),
      dt_create: Timestamp.fromDate(new Date()),
      dt_booking: Timestamp.fromDate(new Date())
    }

    // Campos específicos del Apto Físico
    medicalRecord.familyConditions = assignation.familyConditions || []
    medicalRecord.personalConditions = assignation.personalConditions || []
    medicalRecord.studies = {
      electrocardiogram: assignation.studies?.electrocardiogram || null,
      ...(assignation.studies?.echocardiogram && {
        echocardiogram: assignation.studies.echocardiogram
      }),
      ...(assignation.studies?.ergometry && {
        ergometry: assignation.studies.ergometry
      }),
      ...(assignation.studies?.laboratory && {
        laboratory: assignation.studies.laboratory
      }),
      ...(assignation.studies?.vaccination && {
        vaccination: assignation.studies.vaccination
      })
    }

    return await MedicalRecordRepository.create(
      assignation.uid,
      assignation.assignation_id,
      medicalRecord
    )
  } catch (err) {
    throw new InternalServerErrorException(`[ MedicalRecords | createFitnessCertificate ] => ${err.message}`)
  }
}
