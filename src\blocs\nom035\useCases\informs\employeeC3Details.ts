import { NotFoundException } from "@nestjs/common"
import { getUserByUid } from "@umahealth/auth"
import { FieldSet } from "airtable"
import { QuestionItem } from "src/utils/airtable/coach"
import { organizeC3Details } from "src/utils/airtable/utils/functions"

export const employeeC3Details = async (c3Questions: FieldSet[], c3Responses: FieldSet[], uid: string, email?: string) => {
  if(!email) {
    const userInDB = await getUserByUid(uid)
    if(!userInDB){
      throw new NotFoundException(`[ Nom035 | employeeC3Details ] User with uid: ${uid} not found`)
    }
    email = userInDB.email
  }

  const c3Form = c3Responses.find(response => (response["correo (from nomina)"] as string[])[0] === email)

  if(!c3Form){
    return { formCompleted: false, details: []}
  }

  const questions = organizeC3Details(c3Questions as QuestionItem[], c3Form)
  return { formCompleted: true, details:questions }

}
