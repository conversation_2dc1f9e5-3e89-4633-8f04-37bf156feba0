import { IMedicalRecord, IProvider } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { Content, TDocumentDefinitions } from "pdfmake/interfaces"
import * as moment from "moment"
import logoUma64 from "src/assets/logoUmaBase64"
import { IProviderWithMatricula } from "src/blocs/health-providers/entities/IProviderWithMatricula"

export async function createSummaryTemplate(
  medicalRecordData: IMedicalRecord<Timestamp>,
  provider: IProviderWithMatricula<Timestamp>
): Promise<TDocumentDefinitions> {
  const parsedDate = moment(medicalRecordData.timestamps?.dt_close?.toDate())
    .tz("America/Argentina/Buenos_Aires")
    .format("DD/MM/YYYY")

  const content: Content[] = [
    {
      image: "logo",
      width: 100,
      alignment: "center",
      margin: [0, 20, 0, 20]
    },
    {
      text: "Ficha Médica",
      style: "header"
    },
    {
      text: "Datos del Paciente",
      style: "sectionHeader",
      margin: [0, 20, 0, 10]
    },
    {
      table: {
        widths: ["40%", "60%"],
        body: [
          ["Nombre y apellido:", { text: medicalRecordData.patient.fullname, bold: true }],
          ["Fecha de nacimiento:", { text: moment(medicalRecordData.patient.dob).format("DD/MM/YYYY"), bold: true }],
          ["DNI:", { text: medicalRecordData.patient.dni, bold: true }],
          ["Teléfono de contacto:", { text: medicalRecordData.patient.ws || "-", bold: true }],
        ]
      },
      layout: "lightHorizontalLines",
      margin: [0, 0, 0, 20]
    },
    {
      text: "Antecedentes Familiares",
      style: "sectionHeader",
      margin: [0, 0, 0, 10]
    },
    {
      ul: (() => {
        if (!medicalRecordData.familyConditions?.length) {
          return ["No se registran antecedentes familiares"]
        }

        if (medicalRecordData.familyConditions.length === 1 &&
          medicalRecordData.familyConditions[0] === "Ninguno de los anteriores") {
          return ["Paciente indica que no posee antecedentes familiares"]
        }

        return medicalRecordData.familyConditions
      })(),
      margin: [20, 0, 20, 20]
    },
    {
      text: "Antecedentes Personales",
      style: "sectionHeader",
      margin: [0, 0, 0, 10]
    },
    {
      ul: (() => {
        if (!medicalRecordData.personalConditions?.length) {
          return ["No se registran antecedentes personales"]
        }

        if (medicalRecordData.personalConditions.length === 1 &&
          medicalRecordData.personalConditions[0] === "Ninguno de los anteriores") {
          return ["Paciente indica que no posee antecedentes personales"]
        }

        return medicalRecordData.personalConditions
      })(),
      margin: [20, 0, 20, 20]
    },
    {
      text: "Estudios Médicos Presentados",
      style: "sectionHeader",
      margin: [0, 0, 0, 10]
    },
    {
      ul: Object.entries(medicalRecordData.studies || {})
        .filter(([_, value]) => value)
        .map(([key, value]) => ({
          text: `${value.studyName} (${moment(value.date).format("DD/MM/YYYY")})`
        })),
      margin: [20, 0, 20, 20]
    },
    {
      text: "Evaluación del Profesional de la Salud (Virtual)",
      style: "sectionHeader",
      margin: [0, 20, 0, 10]
    },
    {
      text: "El profesional realizará una consulta virtual, revisará la documentación médica presentada y evaluará la información proporcionada por el usuario.",
      margin: [0, 0, 0, 20]
    },
    {
      text: "Decisión final",
      style: "sectionHeader",
      margin: [0, 0, 0, 10]
    },
    {
      columns: [
        { text: "Apto físico otorgado:", width: "auto" },
        {
          text: medicalRecordData.fitnessCertificateDetails.canDoActivity
            ? "Sí [X]  No [ ]"
            : "Sí [ ]  No [X]",
          bold: true,
          width: "auto"
        }
      ],
      margin: [0, 0, 0, 10]
    },
    {
      text: [
        { text: "Observaciones o recomendaciones: ", bold: true },
        medicalRecordData.fitnessCertificateDetails.observations || "[Sin observaciones]"
      ],
      margin: [0, 0, 0, 20]
    },
    {
      text: "Declaración del Solicitante o Responsable",
      style: "sectionHeader",
      margin: [0, 0, 0, 10]
    },
    {
      text: "Declaro que:",
      margin: [0, 0, 0, 10]
    },
    {
      ul: [
        "La información proporcionada es verídica y completa.",
        "He presentado toda la documentación médica requerida.",
        "Comprendo que este certificado no es válido para actividades de alto riesgo.",
        "Acepto que la decisión del profesional de salud será definitiva."
      ],
      margin: [20, 0, 20, 20]
    },
    ...(medicalRecordData.fitnessCertificateDetails.canDoActivity ? [
      {
        ul: [
          "Este apto físico es válido exclusivamente para actividades recreativas y educativas.",
          "No es válido para deportes extremos, gimnasia de alto rendimiento u otras actividades de riesgo elevado.",
          "Es responsabilidad del solicitante informar cambios significativos en su estado de salud."
        ],
        margin: [20, 0, 20, 30] as [number, number, number, number]
      },
      {
        text: "Validez",
        style: "sectionHeader",
        margin: [0, 0, 0, 10] as [number, number, number, number]
      },
      {
        text: [
          "Este certificado tiene una validez de ",
          {
            text: `${medicalRecordData.fitnessCertificateDetails.diagnosis.validityPeriod} meses a partir de la fecha de emisión.`
          }
        ],
        margin: [0, 0, 0, 10] as [number, number, number, number]
      }
    ] : []),
    {
      text: [
        "Fecha de emisión: ",
        { text: parsedDate, bold: true }
      ],
      margin: [0, 0, 0, 10] as [number, number, number, number]
    },
    {
      stack: [
        {
          text: `Dr./Dra. ${provider.fullname}`,
          bold: true
        },
        {
          text: `M.N.: ${provider.matricula}`,
          bold: true
        }
      ],
      alignment: "right"
    }
  ]

  return {
    content,
    styles: {
      header: {
        fontSize: 20,
        bold: true,
        alignment: "center",
        margin: [0, 0, 0, 20],
        color: "#4D38D7"
      },
      sectionHeader: {
        fontSize: 14,
        bold: true,
        margin: [0, 15, 0, 10],
        color: "#4D38D7"
      },
      tableHeader: {
        fontSize: 12,
        bold: true,
        color: "#4D38D7",
        margin: [0, 5]
      },
      list: {
        margin: [20, 0, 20, 10]
      },
      observations: {
        fontSize: 12,
        margin: [0, 5, 0, 20],
        italics: true
      },
      doctor: {
        fontSize: 12,
        margin: [0, 2, 0, 0]
      }
    },
    defaultStyle: {
      fontSize: 12,
      lineHeight: 1.3
    },
    images: {
      logo: logoUma64
    },
    pageSize: "A4",
    pageMargins: [40, 40, 40, 40]
  }
}

