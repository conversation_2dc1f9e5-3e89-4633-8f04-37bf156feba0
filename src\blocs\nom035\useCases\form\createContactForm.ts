import { IAirtableContactData, IContactForm } from "src/public/nom/entities"
import { saveContactInfo } from "../contact/saveContactInfo"

export const createContactForm = async (data: IContactForm) => {
  const phone = data.ws?.replace(/[^0-9]/g, "") || ""
  const countryMap = {
    "AR": "ARG",
    "MX": "MEX"
  }
  const atCountry = countryMap[data.country as keyof typeof countryMap] || "MEX"

  const airtableData: IAirtableContactData = {
    "comentarios": data.message,
    "contacto empresa": data.name,
    "correo empresa": data.email,
    "empresa": data.corporate?.toUpperCase(),
    "pais": atCountry,
    "producto": data.reason,
    "telefono empresa": phone,
    "tipo de lead": "Pendiente"
  }
  return await saveContactInfo(airtableData)
}
