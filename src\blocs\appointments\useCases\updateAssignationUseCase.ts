import { NotFoundException } from "@nestjs/common"
import { AppointmentRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"
import { appointmentServices, countries, IAppointment } from "@umahealth/entities"

/**
 * Actualiza una consulta de un paciente en la base de datos.
 *
 * @param {appointmentServices} service - El servicio de la cita (ej. "bag", "online").
 * @param {countries} country - El país donde se realiza la consulta.
 * @param {string} assignationId - El identificador de la consulta que se desea actualizar.
 * @param {Partial<IAppointment<Timestamp>>} data - Los datos parciales de la consulta que se desean actualizar.
 * @returns {Promise<void>} - Una promesa que se resuelve cuando la actualización se ha completado.
 * @throws {NotFoundException} - Si no se encuentra la consulta con el `assignationId` proporcionado.
 */
export const updateAssignationUseCase = async (
  service: appointmentServices,
  country: countries,
  assignationId: string,
  data: Partial<IAppointment<Timestamp>>
) => {
  const updatedAssignation = await AppointmentRepository.update(service, country, assignationId, data)

  if (!updatedAssignation) {
    throw new NotFoundException(
      `[ Appointments | Update ] => Could not find assignation ${assignationId} for service ${service}`
    )
  }
  return updatedAssignation
}
