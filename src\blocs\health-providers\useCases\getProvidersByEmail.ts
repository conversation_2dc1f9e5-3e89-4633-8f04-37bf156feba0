import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { IProvider } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"
import { IProviderWithMatricula } from "../entities/IProviderWithMatricula"
import { getCurrentLicense } from "src/blocs/coverage/utils/functions"

export async function getProvidersByEmail(email: string): Promise<{ data: IProviderWithMatricula<Timestamp>[] }> {
  try {
    const providers = await ProviderRepository.getByEmail(email) as IProvider<Timestamp>[]
    const data: IProviderWithMatricula<Timestamp>[] = []
    for (const provider of providers) {
      const licenses = await ProviderRepository.getLicenses(provider.uid)
      const currentLicense = getCurrentLicense(licenses)
      if (currentLicense) {
        data.push({
          ...provider,
          matricula: currentLicense.number.toString()
        })
      }
    }
    return {
      data
    }
  } catch (error) {
    throw new NotFoundException(`Error al obtener provider por email: ${error}`)
  }
}
