import axios from "axios"

export async function complainPredictUseCase(complainText: string) {
  const apiUrl = process.env.UMA_AI_URL
  const headers = {
    "Content-type": "application/json",
    "Accept": "text/plain",
  }
  const url = `${apiUrl}/complaints`
  const body = {
    text: complainText,
  }
  const res = await axios.post<{input: string, output: string}>(url, body, { headers })
  return res.data
}
