import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { appointmentServices, countries, finalDestinations, IAppointment, IMedicalRecord, orderOrPrescription } from "@umahealth/entities"
import { AppointmentRepository, DependantRepository, IDocumentList, MedicalRecordRepository, PatientRepository } from "@umahealth/repositories"

export async function CloseMedicalRecordDueCancelAppointmentUseCase(
  assignationId: string,
  country: countries,
  dt_cancel: Date,
  final_destination: finalDestinations,
  service: appointmentServices,
  uid: string,
): Promise<IDocumentList<Partial<IMedicalRecord<Timestamp>>>> {

  const appointmentDocument = await AppointmentRepository.getByAssignationId<IAppointment>(service, country, assignationId)
  const medicalRecordDocument = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  let patientDocument = null
  const dependant_uid = ""

  if (medicalRecordDocument.mr) {
    medicalRecordDocument.mr.destino_final = final_destination
    medicalRecordDocument.mr.diagnostico = ""
    medicalRecordDocument.mr.epicrisis = ""
    medicalRecordDocument.mr.motivos_de_consulta = appointmentDocument.appointment_data?.motivos_de_consulta || ""
    medicalRecordDocument.mr.observaciones = ""
    medicalRecordDocument.mr.reposo = ""
    medicalRecordDocument.mr.specialist_referral =  ""
    medicalRecordDocument.mr.tratamiento = ""
    medicalRecordDocument.mr.dt_cierre = dt_cancel.toString()
  }
  else {
    medicalRecordDocument.mr =
    {
      destino_final: final_destination,
      diagnostico: "",
      epicrisis: "",
      motivos_de_consulta: appointmentDocument.appointment_data?.motivos_de_consulta || "",
      observaciones: "",
      reposo:  "",
      specialist_referral: "",
      tratamiento: "",
      dt_cierre: dt_cancel.toString() || "",
      alertas: "",
      dt: "",
      ordenes: new Array<orderOrPrescription>,
      receta: [""],
      receta_ref: ""
    }
  }
  medicalRecordDocument.timestamps.dt_cancel = Timestamp.fromDate(dt_cancel)

  // Si la consulta es de guardia, tiene info del paciente pero no el provider
  if (service === "online") {
    if (dependant_uid && dependant_uid !== "") {
      patientDocument = await DependantRepository.getByUidFromDependant(dependant_uid)
      if (!patientDocument) {
        patientDocument = await DependantRepository.getByUid(uid, dependant_uid)
      }
    } else {
      patientDocument = await PatientRepository.getByUid(uid)
    }
    medicalRecordDocument.patient.address = patientDocument.address || ""
    medicalRecordDocument.patient.country = patientDocument.country
    medicalRecordDocument.patient.dependant_uid = dependant_uid
    medicalRecordDocument.patient.dni = patientDocument.dni
    medicalRecordDocument.patient.dob = patientDocument.dob
    medicalRecordDocument.patient.fullname = patientDocument.fullname
    medicalRecordDocument.patient.sex = patientDocument.sex
    medicalRecordDocument.patient.uid = dependant_uid || uid
    medicalRecordDocument.patient.ws = patientDocument.ws
  }

  const mrToUpdate = await MedicalRecordRepository.update(uid, assignationId, medicalRecordDocument)
  if (!mrToUpdate || typeof (mrToUpdate) == "boolean") {
    throw new NotFoundException(`Error closing medical record ${assignationId} from user ${uid}`)
  }
  return mrToUpdate
}
