import { attCategories, MedicalRecord } from "@umahealth/entities";
import { MedicalRecordRepository } from "@umahealth/repositories";
import { getLastForDependantUseCase } from "../useCases";
import { Timestamp } from "@google-cloud/firestore";

jest.mock("@umahealth/repositories");

afterEach(() => {
  jest.clearAllMocks();
})

describe("getLastforDependantUseCase", () => {
  // Arrange for this scope
  const uid = "uid";
  const dependantUid = "dependantUid";
  const category: attCategories = "GUARDIA_RANDOM";
  const now = Timestamp.now();
  const mockMr1: MedicalRecord<Timestamp> = new MedicalRecord();
  mockMr1.att_category = "GUARDIA_RANDOM";
  mockMr1.timestamps = {
    dt_create: now,
  }
  const mockMr2: MedicalRecord<Timestamp> = new MedicalRecord();
  mockMr2.att_category = "GUARDIA_RANDOM";
  mockMr2.timestamps = {
    dt_create: new Timestamp(now.seconds + 3600, now.nanoseconds)
  }
  const mockMr3 = new MedicalRecord();
  mockMr3.att_category = "MI_ESPECIALISTA";

  it ("should retrieve the last medical record according to specified category", async () => {
    // Arrange
    (MedicalRecordRepository.getAllForDependant as jest.Mock).mockResolvedValue([mockMr1, mockMr2, mockMr3]);
    // Act
    const result = await getLastForDependantUseCase(uid, dependantUid, category);
    // Assert
    expect(result).toEqual(mockMr2);
    expect(MedicalRecordRepository.getAllForDependant).toHaveBeenCalledTimes(1);
    expect(MedicalRecordRepository.getAllForDependant).toHaveBeenCalledWith(uid, dependantUid);
  });

  it ("should return null if no mr was found", async () => {
    // Arrange
    (MedicalRecordRepository.getAllForDependant as jest.Mock).mockResolvedValue([]);
    // Act
    const result = await getLastForDependantUseCase(uid, dependantUid, category);
    // Assert
    expect(result).toEqual(null);
    expect(MedicalRecordRepository.getAllForDependant).toHaveBeenCalledTimes(1);
    expect(MedicalRecordRepository.getAllForDependant).toHaveBeenCalledWith(uid, dependantUid);
  });

  it ("should return null if no matching mr was found", async () => {
    // Arrange
    (MedicalRecordRepository.getAllForDependant as jest.Mock).mockResolvedValue([mockMr3]);
    // Act
    const result = await getLastForDependantUseCase(uid, dependantUid, category);
    // Assert
    expect(result).toEqual(null);
    expect(MedicalRecordRepository.getAllForDependant).toHaveBeenCalledTimes(1);
    expect(MedicalRecordRepository.getAllForDependant).toHaveBeenCalledWith(uid, dependantUid);
  });
});