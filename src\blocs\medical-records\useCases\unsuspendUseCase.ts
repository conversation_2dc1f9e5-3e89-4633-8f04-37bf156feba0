import { Timestamp } from "@google-cloud/firestore"
import { MedicalRecordRepository } from "@umahealth/repositories"

export const unsuspendMedicalRecordUseCase = async (assignationId: string, uid: string) => {
  const medicalRecord = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  medicalRecord.mr.destino_final = null
  medicalRecord.mr.dt_cierre = ""
  medicalRecord.timestamps.dt_unsuspend = Timestamp.now()
  return await MedicalRecordRepository.update(uid, assignationId, medicalRecord)
}
