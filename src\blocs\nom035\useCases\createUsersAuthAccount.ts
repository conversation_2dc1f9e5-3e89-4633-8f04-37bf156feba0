import {InternalServerErrorException} from "@nestjs/common"
import {authClient} from "@umahealth/auth"
import {Nom035Repository} from "@umahealth/repositories"
import {INom035_csvBody} from "../interfaces"
import {sendWelcomeEmail} from "../utils/sendWelcomeEmail"
import nom035DTO from "../models/nom035_userPositionDTO"
import {generatePassword} from "../utils/functions"

export const createUsersAuthAccount = async (users: INom035_csvBody[]) => {
  try {
    await Promise.all(
      users.map(async (user) => {
        try {
          const firebaseUser = await Nom035Repository.createUserInAuth({
            email: user.email,
            password: generatePassword(),
            displayName: user.fullname,
          })

          await Promise.all([
            authClient.setCustomUserClaims(firebaseUser.uid, {requirePasswordChange: true}),
            nom035DTO.updateUserUid({uid: firebaseUser.uid, id: user.id as number}),
            sendWelcomeEmail({
              email: user.email,
              fullname: user.fullname,
              pass: user.nationalId.toString()
            })
          ])
        } catch (error) {
          throw new InternalServerErrorException(
            `Error processing user ${user.email}: ${error.message}`
          )
        }
      })
    )
  } catch (error) {
    throw new InternalServerErrorException(
      `Error creating users in Firebase: ${error.message}`
    )
  }
}

