import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { countries, ProviderIncome } from "@umahealth/entities"
import { ProviderIncomesRepository } from "@umahealth/repositories"

export async function updateIncome (country: countries, data: ProviderIncome<Timestamp>, incomeId:string, type:string) {
  const income = await ProviderIncomesRepository.update(country, data, incomeId, type)
  if (!income) throw new InternalServerErrorException(`[ Incomes | updateIncome ] => Could not update income for type ${type}`)
  return income
}