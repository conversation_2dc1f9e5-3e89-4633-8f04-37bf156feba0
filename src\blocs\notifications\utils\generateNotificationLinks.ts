import { createCustomToken } from "@umahealth/auth"
import { getHomeUrl } from "./getHomeUrl"
import { getDynamicLink } from "@umahealth/call-utilities"
import { countries } from "@umahealth/entities"

export const generateNotificationLinks = async (country: countries, assignationId: string, uid: string, isIoma = false) => {
  const customToken = await createCustomToken(uid)
  const dynamicLinkGenUrl = process.env.UMA_URL_SHORTENER
  const homeUrl = getHomeUrl(country, uid)
  let dynamicLink = ""
  let dynamicLinkQueue = ""

  // IOMA specific logic
  const fallbackAction = "[Accedé desde la app de IOMA]"
  const iomaLink = process.env.PROJECT_ID === "uma-development-ar" ? homeUrl : "https://pacientes.ioma.umasalud.com"

  if (homeUrl) {
    const queryParams = `customToken=${customToken}&utm_source=notification&utm_medium=notification&utm_campaign=consulta_especialista`
    const url = `${homeUrl}?${queryParams}`
    const queueUrl = `${homeUrl}/marketplace/queue/${assignationId}?${queryParams}`

    // Generate dynamic links
    dynamicLink = await getDynamicLink(url, dynamicLinkGenUrl)
    dynamicLinkQueue = await getDynamicLink(queueUrl, dynamicLinkGenUrl)

    // IOMA specific queue link
    if (isIoma && customToken) {
      const iomaQueueDynamicLink = await getDynamicLink(
        `${iomaLink}/onlinedoctor/${assignationId}/attention?utm_source=ioma_wpp&utm_medium=ioma_wpp_reminder_1_and_now&utm_campaign=ioma_queue_consulta_especialista&customToken=${customToken}`,
        dynamicLinkGenUrl
      )
      // If IOMA dynamic link failed, use fallback action
      if (iomaQueueDynamicLink) {
        dynamicLinkQueue = iomaQueueDynamicLink
      } else {
        dynamicLinkQueue = fallbackAction
      }
    }

    // Fallbacks for regular links
    if (!dynamicLink) {
      dynamicLink = homeUrl
    }
    if (!dynamicLinkQueue) {
      dynamicLinkQueue = isIoma ? fallbackAction : homeUrl
    }
  }

  return {
    homeUrl,
    dynamicLink,
    dynamicLinkQueue,
  }
}