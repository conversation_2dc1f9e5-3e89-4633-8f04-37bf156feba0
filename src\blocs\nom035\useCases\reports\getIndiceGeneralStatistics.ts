import { FieldSet } from "airtable"
import { dimensionsMaxValues, indiceDimensionsRecommendations } from "src/utils/airtable/statistics"
import { IFormFilters, INom035Form } from "src/portal-app/nom035/statistics/statistics.entities"
import { getUsersWithFormByDate } from "../../utils/functions"

export const getIndiceGeneralStatistics = (usersArray: FieldSet[], indexResponses: FieldSet[], filters: IFormFilters) => {

  const totalDimensionsScore = {
    "fisica": {
      name: "Física",
      value: "fisica",
      totalScore: 0,
      maxScore: dimensionsMaxValues["fisica"].maxScore,
      labelResult: "",
      percentage:0,
      labelLimits: dimensionsMaxValues["fisica"].limitsInPercentage,
      recommendation: "",
      definition: indiceDimensionsRecommendations.fisica.definition
    },
    "emocional": {
      name: "Emocional",
      value: "emocional",
      totalScore: 0,
      maxScore: dimensionsMaxValues["emocional"].maxScore,
      labelResult: "",
      percentage:0,
      labelLimits: dimensionsMaxValues["emocional"].limitsInPercentage,
      recommendation: "",
      definition: indiceDimensionsRecommendations.emocional.definition
    },
    "social": {
      name: "Social",
      value:"social",
      totalScore: 0,
      maxScore: dimensionsMaxValues["social"].maxScore,
      labelResult: "",
      percentage:0,
      labelLimits: dimensionsMaxValues["social"].limitsInPercentage,
      recommendation: "",
      definition: indiceDimensionsRecommendations.social.definition
    }
  }

  let formsCount = 0
  const treshold_amount = Math.floor(usersArray.length * 0.1)


  const users = getUsersWithFormByDate(usersArray, indexResponses, INom035Form.index, filters?.date)
  const totalResponses = users.length

  if((totalResponses < treshold_amount && !filters?.area && !filters?.branch)|| totalResponses === 0){
    return { enoughResponses: false, participationPercentage: 0 }
  }

  users.map(async (user) => {
    const indiceFormCompleted = user.indice as string[]

    if(indiceFormCompleted && indiceFormCompleted.length){
      const lastForm = indiceFormCompleted[indiceFormCompleted.length - 1]
      formsCount ++
      const record = indexResponses.find(response => response.id === lastForm)

      totalDimensionsScore["fisica"].totalScore += +record["puntaje dim fisica"]
      totalDimensionsScore["emocional"].totalScore += +record["puntaje dim emocional"]
      totalDimensionsScore["social"].totalScore += +record["puntaje dim social"]
    }
  })

  // const totalPercentage = +(totalScore / (150 * formsCount) * 100).toFixed(2)
  // const labelResult = totalPercentage < 18 ? "Bienestar Óptimo" : totalPercentage < 35 ? "Bienestar Moderado" : totalPercentage < 60 ? "Salud Psicosocial Afectada" : "Salud Psicosocial en Riesgo"
  Object.keys(totalDimensionsScore).map((item: keyof typeof totalDimensionsScore) => {
    const percentage = +((totalDimensionsScore[item].totalScore / (totalDimensionsScore[item].maxScore * formsCount)) * 100).toFixed(2)
    let found = false
    Object.keys(totalDimensionsScore[item].labelLimits).map((num) => {
      if(!found){
        if(percentage <= +num){
          const dimensionLimit = totalDimensionsScore[item as keyof typeof totalDimensionsScore].labelLimits
          totalDimensionsScore[item].labelResult = dimensionLimit[+num as keyof typeof dimensionLimit]
          if(dimensionLimit[+num as keyof typeof dimensionLimit] === "Medio" || dimensionLimit[+num as keyof typeof dimensionLimit] === "Alto"){
            totalDimensionsScore[item].recommendation = indiceDimensionsRecommendations[item].recommendation
          }
          totalDimensionsScore[item].percentage = percentage
          found = true
        }
      }
    })
  })

  return {enoughResponses: true, dimensions: Object.keys(totalDimensionsScore).map((item: keyof typeof totalDimensionsScore) => totalDimensionsScore[item]) }
}
