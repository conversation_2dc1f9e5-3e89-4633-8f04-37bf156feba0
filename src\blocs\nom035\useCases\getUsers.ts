import { INom035UserFilters, INom035UserWithFormStatus } from "../interfaces"
import nom035DTO from "../models/nom035_userPositionDTO"
import { getFormStatusByEmails } from "./informs/getFormStatusByEmails"
import { FieldSet } from "airtable"


export const getUsers = async (usersArray: FieldSet[], corporateId: string, filters?: INom035UserFilters) => {
  const users = await nom035DTO.getUsers(corporateId, filters) as unknown as INom035UserWithFormStatus[]

  if(!users) return []
  const emails = users.map((user) => user.email)
  const formStatus = getFormStatusByEmails(usersArray, emails)

  emails.map((email) => {
    const id = users.findIndex((user) => user.email === email)

    users[id].dataValues.formStatus = {
      c1: formStatus[email]?.forms?.find(form => form.name === "c1")?.completed || false,
      c2: formStatus[email]?.forms?.find(form => form.name === "c2")?.completed || false,
      c3: formStatus[email]?.forms?.find(form => form.name === "c3")?.completed || false,
    },
    users[id].dataValues.hasInforms = formStatus[email]?.hasInforms || false

  })

  return users
}



