import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"


export const DependantUpdateCoverageUseCase = async (uid: string, coverageId:string, data:IHealthInsurance<Timestamp> ): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>>>> => {
  try {
    const document = await HealthInsuranceRepository.dependantUpdate(uid, coverageId, data)
    return document
  } catch (err) {
    throw new InternalServerErrorException(`[ Coverages | dependant | update ] Error updating document with uid: ${uid} and data: ${JSON.stringify(data)}`)
  }
}

