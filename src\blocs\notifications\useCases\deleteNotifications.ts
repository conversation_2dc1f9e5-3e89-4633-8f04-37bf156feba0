import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { INotification } from "@umahealth/entities"
import { IDocumentList, NotificationRepository } from "@umahealth/repositories"

export async function deleteNotifications(notification_id: string, service:string): Promise<IDocumentList<Partial<INotification<Timestamp>>>> {
  const notification = await NotificationRepository.getById(notification_id, service)
  if (notification) {
    notification.next_send = "cancelled"
    notification.send = []
    return await NotificationRepository.updateNotification(notification_id, notification, service)
  } else {
    throw new NotFoundException(`[ Notifications | deleteNotifications ] => Could not find notification with id ${notification_id} in ${service}`)
  }
}
