import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common"
import { PatientRepository } from "@umahealth/repositories"
import { chat } from "@umahealth/entities"

export async function updateChatAtt(uid: string, chat: chat<Timestamp>) {
  const updatedChatAtt = await PatientRepository.updateChatAtt(uid, chat)
  if(!updatedChatAtt) throw new InternalServerErrorException(`[ Patient | updateChatAtt ] => Error updating chat attention to uid: ${uid}`)

  return updatedChatAtt
}
