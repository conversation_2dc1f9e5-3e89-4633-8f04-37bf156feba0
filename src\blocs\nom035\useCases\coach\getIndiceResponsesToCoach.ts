import { NotFoundException } from "@nestjs/common"
import { getUserByUid } from "@umahealth/auth"
import { getCoachIndiceResponses } from "src/utils/airtable"


export const getIndiceResponsesToCoach = async (uid: string) => {
  const user = await getUserByUid(uid)
  if(!user){
    throw new NotFoundException(`[ Nom035 | Responses To Coach ] User with uid: ${uid} not found`)
  }
  return await getCoachIndiceResponses(user.email)

}
