import { NotFoundException } from "@nestjs/common"
import { FieldSet } from "airtable"
import { organizeC3QuestionsAndAnswers } from "src/utils/airtable/utils/functions"
import { QuestionItem } from "src/utils/airtable/coach"
import * as moment from "moment"
import { getUserByUid } from "@umahealth/auth"

export const employeeC3Answers = async (c3Questions: FieldSet[], c3Responses: FieldSet[], uid: string, email?: string) => {
  if(!email) {
    const user = await getUserByUid(uid)
    if(!user){
      throw new NotFoundException(`[ Nom035 | employeeC3Answers ] User with uid: ${uid} not found`)
    }
    email = user.email
  }

  const c3Form = c3Responses.find(response => (response["correo (from nomina)"] as string[])[0] === email)

  if(!c3Form){
    return { formCompleted: false, answeredDate: null, questions: []}
  }

  const questions = organizeC3QuestionsAndAnswers(c3Questions as QuestionItem[], c3Form)
  const answeredDate = moment(c3Form.created as unknown as Date).locale("es").format("D [de] MMMM [de] YYYY")
  return { formCompleted: true, answeredDate, questions: questions.sort((a,b) => a.questionNumber - b.questionNumber )}

}
