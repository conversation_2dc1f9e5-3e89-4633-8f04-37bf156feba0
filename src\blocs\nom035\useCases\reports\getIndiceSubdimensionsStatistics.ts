import { FieldSet } from "airtable"
import { dimensionsMaxValues } from "src/utils/airtable/statistics"
import { IFormFilters, INom035Form } from "src/portal-app/nom035/statistics/statistics.entities"
import { getUsersWithFormByDate } from "../../utils/functions"

export const getIndiceSubdimensionsStatistics = (usersArray: FieldSet[], indexResponses: FieldSet[], indexQuestions: FieldSet[], dimension: string, filters?: IFormFilters) => {
  let formsCount = 0

  const treshold_amount = Math.floor(usersArray.length * 0.1)

  const users = getUsersWithFormByDate(usersArray, indexResponses, INom035Form.index, filters?.date)
  const count = users.filter(user => user.indice && (user.indice as string[]).length !== 0).length

  if ((count < treshold_amount && !filters?.area && !filters?.branch) || !count){
    return { enoughResponses: false, participationPercentage: 0 }
  }

  const results = indexQuestions.filter(question => question.dimension === dimension)
  const subdimensions: {
    dimension: string,
    fullString: string,
    maxScore: number,
    maxScorePercollaborator: number,
    totalScore: number,
    totalPercentage: number,
    labelResult: string,
    labelLimits?: { [string: string]: string }
  }[] = []
  results.map((item) => {
    const index = subdimensions.findIndex((subdimension) => subdimension.fullString === item.subdimension)
    if(index === -1){
      subdimensions.push({
        dimension: (item["dimension"] as string).toLowerCase().trim(),
        fullString: item["subdimension"] as string,
        maxScorePercollaborator: +item["max puntaje"],
        maxScore: +item["max puntaje"] * count,
        totalScore:0,
        totalPercentage:0,
        labelResult:""
      })
    }else {
      subdimensions[index].maxScorePercollaborator += +item["max puntaje"]
      subdimensions[index].maxScore += +item["max puntaje"] * count
    }
  })

  users.map(async (user) => {
    const indiceFormCompleted = user["indice"] as string[]
    if(indiceFormCompleted && indiceFormCompleted.length){
      const latestForm = indiceFormCompleted[indiceFormCompleted.length - 1]
      formsCount ++
      const record = indexResponses.find(response => response.id === latestForm)
      subdimensions.map((item) => {
        if(item.fullString.toLowerCase() === "actividad fisica"){
          item.totalScore += +record["puntaje act fisica"]
        }else{
          if(item.fullString.toLowerCase() === "seguridad/miedo"){
            item.totalScore += +record["puntaje seguridad miedo"]
          }else{
            item.totalScore += +record[`puntaje ${item.fullString.toLowerCase()}`]
          }
        }
      })
    }
  })

  subdimensions.map((dim) => {
    dim.totalPercentage = (+((dim.totalScore / dim.maxScore) * 100).toFixed(2) || 0)

    let found = false


    const subdimensions = dimensionsMaxValues[dim.dimension as keyof typeof dimensionsMaxValues].subdimensions
    Object.keys(subdimensions[dim.fullString.toLowerCase() as keyof typeof subdimensions]).map((num) => {
      if(!found){
        if(dim.totalPercentage <= +num + 0.01){

          const limits = subdimensions[dim.fullString.toLowerCase() as keyof typeof subdimensions]
          dim.labelResult = limits[+num as keyof typeof limits]

          const limitsInPercentage: {[string:string]: string} = {}
          Object.keys(limits).map((key) => {
            limitsInPercentage[+key] = limits[+key as keyof typeof limits]
          })
          dim.labelLimits = limitsInPercentage
          found = true
        }
      }
    })

    switch(dim.fullString.toLowerCase()){
    case "estres":
      dim.fullString = "Estrés"
      break
    case "alimentacion":
      dim.fullString = "Alimentación"
      break
    case "actividad fisica":
      dim.fullString = "Actividad física"
      break
    default:
      break
    }
  })

  return { enoughResponses: true, subdimensions }
}
