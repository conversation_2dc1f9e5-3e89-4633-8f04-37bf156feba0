import axios from "axios"
import { DengueChatbotRequest, DengueChatbotResponse } from "../interfaces/dengueChatbot"

const apiUrl = `${process.env.UMA_AI_URL}/dengue_chatbot`

export async function dengueSendChatbotMessage(text: string, conversation_id: string) {

  const headers = {
    "Content-type": "application/json",
  }

  const body: DengueChatbotRequest = {
    conversation_id,
    data: { text },
  }

  const response = await axios.post<DengueChatbotResponse>(
    apiUrl,
    body,
    { headers }
  )

  return response.data.output
}
