name: Release

on:
  push:
    branches:
      - main
    paths:
      - src/**
      - package-lock.json
      - tsconfig.json
      - tsconfig.build.json
      - Dockerfile
      - .github/workflows/new-release.yaml
      - .changeset/*.md

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    uses: umahealth/ci-workflows/.github/workflows/github-release-changeset-npm.yaml@main
    secrets:
      github-release-token: ${{ secrets.UMA_GITHUB_PUBLISH_RELEASE_TOKEN }}