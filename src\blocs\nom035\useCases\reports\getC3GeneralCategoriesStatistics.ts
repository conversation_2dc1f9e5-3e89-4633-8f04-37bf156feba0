import { FieldSet } from "airtable"
import { CategoriesC3GeneralWithScores, c3CategoriesRecommendations } from "src/utils/airtable/statistics"
import { getUsersWithFormByDate } from "../../utils/functions"
import { Logger } from "@nestjs/common"
import { IFormFilters } from "src/portal-app/nom035/statistics/statistics.entities"

type CategoryKey = keyof CategoriesC3GeneralWithScores;

export const getC3GeneralCategoriesStatistics = (usersArray: FieldSet[], c3Responses: FieldSet[], filters?: IFormFilters) => {
  Logger.log("[ Nom035 | Download Report] Getting C3 categories statistics")

  const users = filters?.date ? getUsersWithFormByDate(usersArray, c3Responses, "c3", filters?.date) : usersArray.filter(user => user.c3 && (user.c3 as string[]).length !== 0)
  const totalResponses = users.length

  const thresholdAmount = Math.floor(usersArray.length * 0.1)

  if ((totalResponses < thresholdAmount && !filters?.area && filters?.branch) || totalResponses === 0) {
    return { enoughResponses: false, categories: initializeCategories() }
  }

  const categories = processResponses(users, c3Responses, totalResponses)
  calculateResults(categories, totalResponses)

  return { enoughResponses: true, categories }
}

function initializeCategories(): CategoriesC3GeneralWithScores {
  const categoryData = {
    "Factores propios de la actividad": { limits: { 15: "Nulo", 30: "Bajo", 45: "Medio", 60: "Alto", 100: "Muy alto" }, highRiskThreshold: 60, definition: "Los factores propios de la actividad son las características específicas de una tarea laboral que afectan su ejecución, como su complejidad y requisitos de habilidades." },
    "Ambiente de trabajo": { limits: { 25: "Nulo", 45: "Bajo", 55: "Medio", 70: "Alto", 100: "Muy alto" }, highRiskThreshold: 14, definition: "El ambiente de trabajo se refiere al entorno físico y social en el cual los empleados desempeñan sus tareas laborales, influyendo en su bienestar, productividad y satisfacción laboral." },
    "Organizacion del tiempo de trabajo": { limits: { 21: "Nulo", 29: "Bajo", 42: "Medio", 54: "Alto", 100: "Muy alto" }, highRiskThreshold: 13, definition: "La organización del tiempo de trabajo se trata de planificar y gestionar eficientemente las horas laborales." },
    "Liderazgo y relaciones en el trabajo": { limits: { 21: "Nulo", 29: "Bajo", 42: "Medio", 54: "Alto", 100: "Muy alto" }, highRiskThreshold: 58, definition: "El liderazgo y las relaciones en el trabajo se centran en cómo los líderes dirigen y fomentan relaciones efectivas en el entorno laboral para alcanzar metas y promover un ambiente de trabajo positivo." },
    "Entorno organizacional": { limits: { 25: "Nulo", 35: "Bajo", 45: "Medio", 58: "Alto", 100: "Muy alto" }, highRiskThreshold: 23, definition: "El entorno organizacional abarca la cultura, estructura y dinámicas internas de una organización que impactan en el comportamiento de los empleados." }
  }

  return Object.entries(categoryData).reduce((acc, [key, value]) => {
    acc[key as CategoryKey] = {
      totalScore: 0,
      maxScore: 0,
      score: 0,
      highRisk: 0,
      labelResult: "",
      percentage: 0,
      labelLimits: value.limits,
      recommendation: "",
      definition: value.definition,
      highRiskThreshold: value.highRiskThreshold
    }
    return acc
  }, {} as CategoriesC3GeneralWithScores)
}

function processResponses(users: FieldSet[], c3Responses: FieldSet[], count: number): CategoriesC3GeneralWithScores {
  const categories = initializeCategories()
  const categoryMapping: Record<string, CategoryKey> = {
    "cat1": "Ambiente de trabajo",
    "cat2": "Factores propios de la actividad",
    "cat3": "Organizacion del tiempo de trabajo",
    "cat4": "Liderazgo y relaciones en el trabajo",
    "cat5": "Entorno organizacional"
  }

  users.forEach(user => {
    const c3FormCompleted = user.c3 as string[] || []
    if (!c3FormCompleted.length) { return }

    const lastId = c3FormCompleted[c3FormCompleted.length - 1]
    const record = c3Responses.find(record => record.id === lastId)

    Object.entries(categoryMapping).forEach(([catKey, catName]) => {
      if (categories[catName].maxScore === 0) {
        categories[catName].maxScore = extractMaxValue(record[`${catKey} / max`].toString()) * count
      }
      const score = Number(record[`${catKey} ${catName}`])
      categories[catName].totalScore = categories[catName].totalScore + score
      if (score >= categories[catName].highRiskThreshold) {
        categories[catName].highRisk++
      }
    })
  })

  return categories
}

function extractMaxValue(str: string): number {
  const match = str.match(/\d{1,2}\/(\d{2,3})/)
  return match ? Number(match[1]) : 0
}

function calculateResults(categories: CategoriesC3GeneralWithScores, count: number): void {
  Object.entries(categories).forEach(([key, category]) => {
    category.percentage = +(category.totalScore / category.maxScore * 100).toFixed(2)
    category.score = +((category.percentage * category.maxScore / count) / 100).toFixed(2)

    for (const [limit, label] of Object.entries(category.labelLimits)) {
      if (category.percentage <= Number(limit)) {
        category.labelResult = label
        category.recommendation = c3CategoriesRecommendations[label as keyof typeof c3CategoriesRecommendations]
        break
      }
    }
  })
}
