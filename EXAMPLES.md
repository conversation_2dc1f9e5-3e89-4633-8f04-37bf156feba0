# 🎯 Ejemplos Prácticos de Implementación

Esta guía contiene ejemplos completos de implementación siguiendo los patrones establecidos en Megalith.

## 📋 Tabla de Contenidos

1. [Ejemplo 1: Nuevo Caso de Uso](#ejemplo-1-nuevo-caso-de-uso)
2. [Ejemplo 2: Nuevo Controlador](#ejemplo-2-nuevo-controlador)
3. [Ejemplo 3: Integración Externa](#ejemplo-3-integración-externa)
4. [Ejemplo 4: Testing Completo](#ejemplo-4-testing-completo)
5. [Ejemplo 5: Manejo de Eventos](#ejemplo-5-manejo-de-eventos)

## 🔧 Ejemplo 1: Nuevo Caso de Uso

### Escenario: Implementar "Reagendar Cita Médica"

#### 1. Definir Interfaces

```typescript
// src/blocs/appointments/interfaces/reschedule.interfaces.ts
export interface IRescheduleAppointmentRequest {
  appointmentId: string
  newScheduledAt: Date
  reason: string
  requestedBy: 'patient' | 'doctor' | 'admin'
}

export interface IRescheduleAppointmentResponse {
  appointment: IAppointment
  previousScheduledAt: Date
  rescheduleReason: string
  rescheduleHistory: IRescheduleHistory[]
}

export interface IRescheduleHistory {
  previousDate: Date
  newDate: Date
  reason: string
  requestedBy: string
  timestamp: Date
}
```

#### 2. Crear Validaciones

```typescript
// src/blocs/appointments/entities/reschedule.entities.ts
import * as Joi from 'joi'

export const rescheduleAppointmentSchema = Joi.object({
  appointmentId: Joi.string().uuid().required(),
  newScheduledAt: Joi.date().iso().min('now').required(),
  reason: Joi.string().min(10).max(500).required(),
  requestedBy: Joi.string().valid('patient', 'doctor', 'admin').required()
})
```

#### 3. Implementar Caso de Uso

```typescript
// src/blocs/appointments/useCases/rescheduleAppointmentUseCase.ts
import { BadRequestException, NotFoundException, ConflictException } from "@nestjs/common"
import { AppointmentRepository } from "@umahealth/repositories"
import { IAppointment, appointmentStates } from "@umahealth/entities"
import { IRescheduleAppointmentRequest, IRescheduleAppointmentResponse } from "../interfaces"

export async function rescheduleAppointmentUseCase(
  request: IRescheduleAppointmentRequest
): Promise<IRescheduleAppointmentResponse> {
  
  // 1. Validaciones de entrada
  if (!request.appointmentId || !request.newScheduledAt || !request.reason) {
    throw new BadRequestException('Datos requeridos faltantes')
  }

  // 2. Obtener la cita actual
  const appointment = await AppointmentRepository.getById(request.appointmentId)
  if (!appointment) {
    throw new NotFoundException('Cita no encontrada')
  }

  // 3. Validaciones de negocio
  if (appointment.state === appointmentStates.CANCELLED) {
    throw new BadRequestException('No se puede reagendar una cita cancelada')
  }

  if (appointment.state === appointmentStates.COMPLETED) {
    throw new BadRequestException('No se puede reagendar una cita completada')
  }

  // 4. Validar que la nueva fecha no sea la misma
  if (appointment.scheduledAt.getTime() === request.newScheduledAt.getTime()) {
    throw new BadRequestException('La nueva fecha debe ser diferente a la actual')
  }

  // 5. Validar disponibilidad del proveedor
  const isProviderAvailable = await checkProviderAvailability(
    appointment.providerId,
    request.newScheduledAt
  )
  
  if (!isProviderAvailable) {
    throw new ConflictException('El proveedor no está disponible en el horario solicitado')
  }

  // 6. Validar restricciones de reagendamiento
  const rescheduleCount = appointment.rescheduleHistory?.length || 0
  if (rescheduleCount >= 3) {
    throw new BadRequestException('Se ha alcanzado el límite máximo de reagendamientos')
  }

  // 7. Crear historial de reagendamiento
  const rescheduleEntry: IRescheduleHistory = {
    previousDate: appointment.scheduledAt,
    newDate: request.newScheduledAt,
    reason: request.reason,
    requestedBy: request.requestedBy,
    timestamp: new Date()
  }

  // 8. Actualizar la cita
  const updatedAppointment = await AppointmentRepository.update(request.appointmentId, {
    scheduledAt: request.newScheduledAt,
    rescheduleHistory: [...(appointment.rescheduleHistory || []), rescheduleEntry],
    lastModifiedAt: new Date(),
    lastModifiedBy: request.requestedBy
  })

  // 9. Emitir eventos de dominio
  await EventEmitter.emit('appointment.rescheduled', {
    appointmentId: request.appointmentId,
    patientId: appointment.patientId,
    providerId: appointment.providerId,
    previousDate: appointment.scheduledAt,
    newDate: request.newScheduledAt,
    reason: request.reason,
    requestedBy: request.requestedBy
  })

  // 10. Retornar respuesta
  return {
    appointment: updatedAppointment,
    previousScheduledAt: appointment.scheduledAt,
    rescheduleReason: request.reason,
    rescheduleHistory: updatedAppointment.rescheduleHistory || []
  }
}

// Función auxiliar para validar disponibilidad
async function checkProviderAvailability(
  providerId: string,
  scheduledAt: Date
): Promise<boolean> {
  const existingAppointments = await AppointmentRepository.findByProviderAndDate(
    providerId,
    scheduledAt
  )
  
  return existingAppointments.length === 0
}
```

#### 4. Agregar al Bloc

```typescript
// src/blocs/appointments/appointments.bloc.ts
import { rescheduleAppointmentUseCase } from './useCases'

export class AppointmentsBloc {
  // ... métodos existentes

  async rescheduleAppointment(request: IRescheduleAppointmentRequest) {
    return await rescheduleAppointmentUseCase(request)
  }
}
```

#### 5. Crear Controlador

```typescript
// src/patient-app/appointments/appointments.controller.ts
@Controller('appointments')
export class AppointmentsController {
  constructor(private readonly appointmentsBloc: AppointmentsBloc) {}

  @Patch(':id/reschedule')
  @UsePipes(new JoiValidationPipe(rescheduleAppointmentSchema))
  @UseGuards(AuthGuard)
  async rescheduleAppointment(
    @Param('id') appointmentId: string,
    @Body() body: Omit<IRescheduleAppointmentRequest, 'appointmentId'>,
    @Request() req: any
  ) {
    const request: IRescheduleAppointmentRequest = {
      appointmentId,
      ...body,
      requestedBy: 'patient' // Se determina por el contexto del controlador
    }

    return await this.appointmentsBloc.rescheduleAppointment(request)
  }
}
```

## 🎮 Ejemplo 2: Nuevo Controlador

### Escenario: Controlador para Gestión de Horarios de Médicos

#### 1. Crear Entidades

```typescript
// src/doctor-app/schedules/schedules.entities.ts
import * as Joi from 'joi'

export interface ICreateScheduleRequest {
  providerId: string
  dayOfWeek: number // 0-6 (Domingo-Sábado)
  startTime: string // "09:00"
  endTime: string   // "17:00"
  appointmentDuration: number // minutos
  breakTime?: IBreakTime[]
}

export interface IBreakTime {
  startTime: string
  endTime: string
  reason: string
}

export const createScheduleSchema = Joi.object({
  providerId: Joi.string().uuid().required(),
  dayOfWeek: Joi.number().integer().min(0).max(6).required(),
  startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
  endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
  appointmentDuration: Joi.number().integer().min(15).max(120).required(),
  breakTime: Joi.array().items(
    Joi.object({
      startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
      endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
      reason: Joi.string().max(200).required()
    })
  ).optional()
})
```

#### 2. Implementar Controlador

```typescript
// src/doctor-app/schedules/schedules.controller.ts
import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query,
  UseGuards,
  UsePipes,
  Request
} from '@nestjs/common'
import { JoiValidationPipe } from '../../utils/validation'
import { AuthGuard, RoleGuard } from '../../utils/guards'
import { Roles } from '../../utils/decorators'
import { SchedulesBloc } from '../../blocs/schedules/schedules.bloc'

@Controller('schedules')
@UseGuards(AuthGuard, RoleGuard)
@Roles('doctor', 'admin')
export class SchedulesController {
  constructor(private readonly schedulesBloc: SchedulesBloc) {}

  @Post()
  @UsePipes(new JoiValidationPipe(createScheduleSchema))
  async createSchedule(
    @Body() data: ICreateScheduleRequest,
    @Request() req: any
  ) {
    // Validar que el doctor solo puede crear sus propios horarios
    if (req.user.role === 'doctor' && data.providerId !== req.user.providerId) {
      throw new ForbiddenException('No puede crear horarios para otros médicos')
    }

    return await this.schedulesBloc.createSchedule(data)
  }

  @Get('provider/:providerId')
  async getProviderSchedules(
    @Param('providerId') providerId: string,
    @Query('week') week?: string
  ) {
    return await this.schedulesBloc.getProviderSchedules(providerId, week)
  }

  @Put(':id')
  @UsePipes(new JoiValidationPipe(updateScheduleSchema))
  async updateSchedule(
    @Param('id') scheduleId: string,
    @Body() data: Partial<ICreateScheduleRequest>,
    @Request() req: any
  ) {
    return await this.schedulesBloc.updateSchedule(scheduleId, data, req.user)
  }

  @Delete(':id')
  async deleteSchedule(
    @Param('id') scheduleId: string,
    @Request() req: any
  ) {
    return await this.schedulesBloc.deleteSchedule(scheduleId, req.user)
  }

  @Get(':id/available-slots')
  async getAvailableSlots(
    @Param('id') scheduleId: string,
    @Query('date') date: string
  ) {
    return await this.schedulesBloc.getAvailableSlots(scheduleId, new Date(date))
  }
}
```

#### 3. Crear Módulo

```typescript
// src/doctor-app/schedules/schedules.module.ts
import { Module } from '@nestjs/common'
import { SchedulesController } from './schedules.controller'
import { SchedulesBloc } from '../../blocs/schedules/schedules.bloc'

@Module({
  controllers: [SchedulesController],
  providers: [SchedulesBloc],
  exports: [SchedulesBloc]
})
export class SchedulesModule {}
```

## 🔌 Ejemplo 3: Integración Externa

### Escenario: Integración con Sistema de Laboratorio

#### 1. Crear Servicio de Integración

```typescript
// src/providers/laboratory/laboratory.service.ts
import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common'
import axios, { AxiosInstance } from 'axios'

export interface ILabOrder {
  patientId: string
  tests: string[]
  priority: 'normal' | 'urgent'
  notes?: string
}

export interface ILabResult {
  orderId: string
  patientId: string
  results: ITestResult[]
  status: 'pending' | 'completed' | 'cancelled'
}

export interface ITestResult {
  testCode: string
  testName: string
  value: string
  unit: string
  referenceRange: string
  status: 'normal' | 'abnormal' | 'critical'
}

@Injectable()
export class LaboratoryService {
  private readonly logger = new Logger(LaboratoryService.name)
  private readonly client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: process.env.LABORATORY_API_URL,
      timeout: 30000,
      headers: {
        'Authorization': `Bearer ${process.env.LABORATORY_API_KEY}`,
        'Content-Type': 'application/json'
      }
    })

    // Interceptor para logging
    this.client.interceptors.request.use(
      (config) => {
        this.logger.log(`Laboratory API Request: ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        this.logger.error('Laboratory API Request Error:', error)
        return Promise.reject(error)
      }
    )

    this.client.interceptors.response.use(
      (response) => {
        this.logger.log(`Laboratory API Response: ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        this.logger.error('Laboratory API Response Error:', error.response?.data || error.message)
        return Promise.reject(error)
      }
    )
  }

  async createOrder(order: ILabOrder): Promise<string> {
    try {
      const response = await this.client.post('/orders', {
        patient_id: order.patientId,
        tests: order.tests,
        priority: order.priority,
        notes: order.notes,
        created_by: 'uma_health'
      })

      return response.data.order_id
    } catch (error) {
      this.logger.error('Error creating lab order:', error)
      throw new InternalServerErrorException('Error al crear orden de laboratorio')
    }
  }

  async getOrderStatus(orderId: string): Promise<ILabResult> {
    try {
      const response = await this.client.get(`/orders/${orderId}`)
      
      return this.mapExternalToInternal(response.data)
    } catch (error) {
      this.logger.error(`Error getting order status for ${orderId}:`, error)
      throw new InternalServerErrorException('Error al obtener estado de orden')
    }
  }

  async cancelOrder(orderId: string): Promise<void> {
    try {
      await this.client.delete(`/orders/${orderId}`)
    } catch (error) {
      this.logger.error(`Error cancelling order ${orderId}:`, error)
      throw new InternalServerErrorException('Error al cancelar orden')
    }
  }

  private mapExternalToInternal(externalData: any): ILabResult {
    return {
      orderId: externalData.order_id,
      patientId: externalData.patient_id,
      status: this.mapStatus(externalData.status),
      results: externalData.results?.map((result: any) => ({
        testCode: result.test_code,
        testName: result.test_name,
        value: result.value,
        unit: result.unit,
        referenceRange: result.reference_range,
        status: this.mapResultStatus(result.status)
      })) || []
    }
  }

  private mapStatus(externalStatus: string): 'pending' | 'completed' | 'cancelled' {
    const statusMap: Record<string, 'pending' | 'completed' | 'cancelled'> = {
      'in_progress': 'pending',
      'ready': 'completed',
      'cancelled': 'cancelled'
    }
    
    return statusMap[externalStatus] || 'pending'
  }

  private mapResultStatus(externalStatus: string): 'normal' | 'abnormal' | 'critical' {
    const statusMap: Record<string, 'normal' | 'abnormal' | 'critical'> = {
      'within_range': 'normal',
      'out_of_range': 'abnormal',
      'critical_value': 'critical'
    }
    
    return statusMap[externalStatus] || 'normal'
  }
}
```

#### 2. Crear Caso de Uso

```typescript
// src/blocs/laboratory/useCases/createLabOrderUseCase.ts
import { BadRequestException, NotFoundException } from '@nestjs/common'
import { PatientRepository } from '@umahealth/repositories'
import { LaboratoryService } from '../../../providers/laboratory/laboratory.service'

export async function createLabOrderUseCase(
  patientId: string,
  tests: string[],
  priority: 'normal' | 'urgent' = 'normal',
  notes?: string
): Promise<string> {
  
  // 1. Validaciones
  if (!patientId || !tests || tests.length === 0) {
    throw new BadRequestException('Paciente y tests son requeridos')
  }

  // 2. Verificar que el paciente existe
  const patient = await PatientRepository.getById(patientId)
  if (!patient) {
    throw new NotFoundException('Paciente no encontrado')
  }

  // 3. Validar tests disponibles
  const validTests = await getAvailableTests()
  const invalidTests = tests.filter(test => !validTests.includes(test))
  
  if (invalidTests.length > 0) {
    throw new BadRequestException(`Tests no válidos: ${invalidTests.join(', ')}`)
  }

  // 4. Crear orden en sistema externo
  const laboratoryService = new LaboratoryService()
  const orderId = await laboratoryService.createOrder({
    patientId,
    tests,
    priority,
    notes
  })

  // 5. Guardar referencia local
  await LabOrderRepository.create({
    orderId,
    patientId,
    tests,
    priority,
    status: 'pending',
    createdAt: new Date()
  })

  // 6. Emitir evento
  await EventEmitter.emit('lab.order.created', {
    orderId,
    patientId,
    tests,
    priority
  })

  return orderId
}

async function getAvailableTests(): Promise<string[]> {
  // Obtener lista de tests disponibles desde configuración o API
  return [
    'hemograma_completo',
    'glucosa',
    'colesterol_total',
    'trigliceridos',
    'creatinina',
    'urea'
  ]
}
```

## 🧪 Ejemplo 4: Testing Completo

### Test de Caso de Uso

```typescript
// src/blocs/appointments/useCases/__tests__/rescheduleAppointmentUseCase.spec.ts
import { rescheduleAppointmentUseCase } from '../rescheduleAppointmentUseCase'
import { AppointmentRepository } from '@umahealth/repositories'
import { BadRequestException, NotFoundException, ConflictException } from '@nestjs/common'
import { appointmentStates } from '@umahealth/entities'

// Mocks
jest.mock('@umahealth/repositories')
jest.mock('../../../utils/events')

const mockAppointmentRepository = AppointmentRepository as jest.Mocked<typeof AppointmentRepository>

describe('RescheduleAppointmentUseCase', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  const validRequest = {
    appointmentId: 'apt-123',
    newScheduledAt: new Date('2024-12-01T10:00:00Z'),
    reason: 'Conflicto de horario del paciente',
    requestedBy: 'patient' as const
  }

  const mockAppointment = {
    id: 'apt-123',
    patientId: 'patient-456',
    providerId: 'provider-789',
    scheduledAt: new Date('2024-11-30T10:00:00Z'),
    state: appointmentStates.SCHEDULED,
    rescheduleHistory: []
  }

  describe('when request is valid', () => {
    it('should reschedule appointment successfully', async () => {
      // Arrange
      mockAppointmentRepository.getById.mockResolvedValue(mockAppointment)
      mockAppointmentRepository.findByProviderAndDate.mockResolvedValue([])
      mockAppointmentRepository.update.mockResolvedValue({
        ...mockAppointment,
        scheduledAt: validRequest.newScheduledAt,
        rescheduleHistory: [expect.any(Object)]
      })

      // Act
      const result = await rescheduleAppointmentUseCase(validRequest)

      // Assert
      expect(result.appointment.scheduledAt).toEqual(validRequest.newScheduledAt)
      expect(result.previousScheduledAt).toEqual(mockAppointment.scheduledAt)
      expect(result.rescheduleReason).toBe(validRequest.reason)
      expect(mockAppointmentRepository.update).toHaveBeenCalledWith(
        validRequest.appointmentId,
        expect.objectContaining({
          scheduledAt: validRequest.newScheduledAt,
          rescheduleHistory: expect.arrayContaining([
            expect.objectContaining({
              reason: validRequest.reason,
              requestedBy: validRequest.requestedBy
            })
          ])
        })
      )
    })
  })

  describe('when appointment does not exist', () => {
    it('should throw NotFoundException', async () => {
      // Arrange
      mockAppointmentRepository.getById.mockResolvedValue(null)

      // Act & Assert
      await expect(rescheduleAppointmentUseCase(validRequest))
        .rejects
        .toThrow(NotFoundException)
    })
  })

  describe('when appointment is cancelled', () => {
    it('should throw BadRequestException', async () => {
      // Arrange
      mockAppointmentRepository.getById.mockResolvedValue({
        ...mockAppointment,
        state: appointmentStates.CANCELLED
      })

      // Act & Assert
      await expect(rescheduleAppointmentUseCase(validRequest))
        .rejects
        .toThrow('No se puede reagendar una cita cancelada')
    })
  })

  describe('when provider is not available', () => {
    it('should throw ConflictException', async () => {
      // Arrange
      mockAppointmentRepository.getById.mockResolvedValue(mockAppointment)
      mockAppointmentRepository.findByProviderAndDate.mockResolvedValue([
        { id: 'other-apt', providerId: 'provider-789' }
      ])

      // Act & Assert
      await expect(rescheduleAppointmentUseCase(validRequest))
        .rejects
        .toThrow(ConflictException)
    })
  })

  describe('when reschedule limit is reached', () => {
    it('should throw BadRequestException', async () => {
      // Arrange
      const appointmentWithManyReschedules = {
        ...mockAppointment,
        rescheduleHistory: [
          { reason: 'reason1' },
          { reason: 'reason2' },
          { reason: 'reason3' }
        ]
      }
      mockAppointmentRepository.getById.mockResolvedValue(appointmentWithManyReschedules)

      // Act & Assert
      await expect(rescheduleAppointmentUseCase(validRequest))
        .rejects
        .toThrow('Se ha alcanzado el límite máximo de reagendamientos')
    })
  })
})
```

---

Estos ejemplos muestran la implementación completa siguiendo los patrones DDD establecidos en Megalith. 🚀
