import * as moment from "moment"
import { IOrder } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { TDocumentDefinitions } from "pdfmake/interfaces"
import { imgUrlToBase64 } from "../../../utils/imgToBase64"
import logoUma64 from "../../../assets/logoUmaBase64"
import logoIoma64 from "../../../assets/logoIomaBase64"
import emptyImg64 from "../../../assets/emptyImgBase64"
import { getInitials } from "src/utils/patient/parseNames"

/**
 * This function create the template for the order pdf
 * @param order the order
 * @param chosenName if the patient has a chosenName defined
 * @returns TDocumentDefinitions object with the template and configs for the pdf
 */

export async function createOrderTemplate(order: IOrder<Timestamp>, chosenName?: string) {
  const { patient, provider, items, timestamps, date, diagnosis, indications } = order

  const isIoma = patient.corporate?.toUpperCase().includes("IOMA")

  let parsedDate = ""

  if (timestamps?.dt_create) {
    // SE RESTAN 3 HS POR EL TIMEZONE ARGENTINO, MX TIENE OTRO ARCHIVO
    parsedDate = moment(timestamps?.dt_create?.toDate()).subtract(3, "hours").format("HH:mm DD/MM/YYYY")
  } else {
    parsedDate = date
      ? moment(date, "YYYYMMDDHHmmSS").format("HH:mm DD/MM/YYYY")
      : moment(new Date(), "YYYYMMDDHHmmSS").format("HH:mm DD/MM/YYYY")
  }

  let initialsName = ""
  if (chosenName) {
    initialsName = getInitials(patient.fullname)
  }

  const content = [
    {
      image: "logo",
      width: 100,
      alignment: "center",
      margin: [0, 0, 0, 12],
    },
    {
      text: "Orden de estudios",
      style: "header",
    },
    {
      columns: [
        {
          stack: [
            {
              text: "Datos del paciente",
              style: "subheader",
            },
            {
              columns: [
                {
                  text: "Nombre: ",
                  style: "b",
                  width: "auto",
                },
                {
                  text: chosenName ? initialsName : patient.fullname || "-",
                  width: "auto",
                },
              ],
              columnGap: 4,
            },
            chosenName
              ? {
                columns: [
                  {
                    text: "Nombre elegido: ",
                    style: "b",
                    width: "auto",
                  },
                  {
                    text: chosenName,
                    width: "auto",
                  },
                ],
                columnGap: 4,
              }
              : null,
            {
              columns: [
                {
                  text: "DNI: ",
                  style: "b",
                  width: "auto",
                },
                {
                  text: patient.dni ? patient.dni : "-",
                  width: "auto",
                },
              ],
              columnGap: 4,
            },
            {
              columns: [
                {
                  text: "Obra social: ",
                  style: "b",
                  width: "auto",
                },
                {
                  text: patient.corporate ? patient.corporate : "-",
                  width: "auto",
                },
              ],
              columnGap: 4,
            },
            {
              columns: [
                {
                  text: "Número de afiliado: ",
                  style: "b",
                  width: "auto",
                },
                {
                  text: patient.n_afiliado ? patient.n_afiliado : "-",
                  width: "auto",
                },
              ],
              columnGap: 4,
            },
            {
              columns: [
                {
                  text: "Plan: ",
                  style: "b",
                  width: "auto",
                },
                {
                  text: patient.plan ? patient.plan : "-",
                  width: "auto",
                },
              ],
              columnGap: 4,
            },
            patient.credentialVersion && {
              columns: [
                {
                  text: "Versión de credencial: ",
                  style: "b",
                  width: "auto",
                },
                {
                  text: patient.credentialVersion,
                  width: "auto",
                },
              ],
              columnGap: 4,
            },
          ],
          margin: [70, 0, 0, 0],
          width: "70%",
        },
        {
          stack: [
            {
              text: "Fecha",
              style: "subheader",
            },
            {
              text: parsedDate,
            },
          ],
        },
      ],
    },
    {
      text: "Estudios",
      style: ["subheader", "marginLeft"],
    },
    {
      style: ["marginLeft"],
      ul: items.map((el) => {
        return {
          text: el.nombre,
          bold: true,
        }
      }),
    },
    indications?.length
      ? {
        text: "Indicaciones",
        style: ["subheader", "marginLeft"],
      }
      : null,
    indications?.length
      ? {
        style: ["marginLeft"],
        ul: [
          {
            text: indications,
            bold: true,
          },
        ],
      }
      : null,
    diagnosis?.length
      ? {
        text: "Dx",
        style: ["subheader", "marginLeft"],
      }
      : null,
    diagnosis?.length
      ? {
        style: ["marginLeft"],
        ul: [
          {
            text: diagnosis,
            bold: true,
          },
        ],
      }
      : null,
    {
      text: "Médico",
      style: ["subheader", "marginLeft"],
    },
    {
      style: ["marginLeft"],
      columns: [
        {
          text: "Nombre: ",
          style: "b",
          width: "auto",
        },
        {
          text: provider.fullname ? provider.fullname : "-",
          width: "auto",
        },
      ],
      columnGap: 4,
    },
    {
      style: ["marginLeft"],
      columns: [
        {
          text: "Matrícula: ",
          style: "b",
          width: "auto",
        },
        {
          text: provider.matricula ? provider.matricula : "-",
          width: "auto",
        },
      ],
      columnGap: 4,
    },
    {
      style: ["marginLeft"],
      columns: [
        {
          text: "Firma: ",
          style: "b",
          width: "auto",
        },
        {
          image: "firma",
          width: 150,
          height: 80,
        },
      ],
      columnGap: 4,
    },
  ]

  const footer = {
    table: {
      widths: ["*"],
      body: [
        [
          {
            text: "www.umasalud.com",
            style: "footer",
            fillColor: "#0a6dd7",
          },
        ],
      ],
    },
    layout: "noBorders",
  }

  const styles = {
    header: {
      fontSize: 18,
      color: "#0a6dd7",
      alignment: "center",
      bold: true,
      margin: [0, 0, 0, 10],
    },
    subheader: {
      fontSize: 12,
      color: "#0a6dd7",
    },
    footer: {
      fontSize: 9,
      color: "#fff",
      alignment: "center",
      margin: [0, 8, 0, 7],
    },
    b: {
      bold: true,
    },
    marginLeft: {
      margin: [70, 0, 0, 0],
    },
  }

  const images = {
    logo: isIoma ? logoIoma64 : logoUma64,
    firma: provider.signature ? await imgUrlToBase64(provider.signature) : emptyImg64,
  }

  // Configuración del documento
  const docDefinition = {
    content,
    footer,
    styles,
    defaultStyle: {
      fontSize: 10,
      lineHeight: 1.5,
    },
    images,
    pageSize: "A4",
    pageMargins: [30, 20, 30, 30],
  } as unknown as TDocumentDefinitions

  return docDefinition
}
