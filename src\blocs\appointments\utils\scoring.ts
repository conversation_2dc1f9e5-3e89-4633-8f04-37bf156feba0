import { Logger } from "@nestjs/common"
import { IMedicalRecord, IPatient, effectiveFinalDestinations, notEffectiveFinalDestinations, paymentData } from "@umahealth/entities"
import { MedicalRecordRepository } from "@umahealth/repositories"
import { startOfDay, isSameDay } from "date-fns"

interface ITodayAppointments {
    effective: number
    notEffective: number
}

interface IEffectiveAverage {
    isNewUser: boolean,
    effectivePercentage: number
}

interface IBaseScoringCriteria {
  vip: number,
  subscription: number,
  emergencias: number,
  paid: number,
  newUser: number,
  effectivePercentage: number
}
const baseScoringCriteria = {
  vip: 100,
  subscription: 30,
  emergencias: 20,
  paid: 10,
  newUser: 5,
  effectivePercentage: 5
}

const applyModifiers = (score: number, todayAppointments: ITodayAppointments, paymentData: paymentData) => {
  if ((paymentData.method.toUpperCase() === "UMACREDITOS" && paymentData.method.toUpperCase() === "MERCADOPAGO") || paymentData.id.toUpperCase() === "EMERGENCIAS") {
    if (todayAppointments.notEffective && !todayAppointments.effective) {
      score *= 2
    }
  }
  return score
}

const logWarnings = (score: number, paymentData:paymentData, uid: string) => {
  if (paymentData.method.toUpperCase() === "CORPORATE" && paymentData.id.toUpperCase() !== "EMERGENCIAS" && score > 10) {
    Logger.warn(`[SCORING][ALERTA] Consulta CORPORATE con score mayor a 10 score: ${score} corporate: ${paymentData.id} uid: ${uid}`)
  }

  if ((paymentData.method.toUpperCase() === "UMACREDITOS" && paymentData.method.toUpperCase() === "MERCADOPAGO") && score > 20) {
    Logger.warn(`[SCORING][ALERTA] Consulta PAGA con score mayor a 20 score: ${score} corporate: ${paymentData.id} uid: ${uid}`)
  }

  if (paymentData.id.toUpperCase() === "EMERGENCIAS" && score > 30) {
    Logger.warn(`[SCORING][ALERTA] Consulta EMERGENCIAS con score mayor a 30 score: ${score} corporate: ${paymentData.id} uid: ${uid}`)
  }


  if (paymentData.method.toUpperCase() === "SUBSCRIPTION" && score > 100) {
    Logger.warn(`[SCORING][ALERTA] Consulta SUBSCRIPTION con score mayor a 100 score: ${score} corporate: ${paymentData.id} uid: ${uid}`)
  }
}

const calculateBaseScore = (patient: IPatient<FirebaseFirestore.Timestamp>, payment_data: paymentData, effectiveAverage: IEffectiveAverage, criteria: IBaseScoringCriteria) => {
  let score = 0
  const { id } = patient
  const isPaid = payment_data?.method?.toUpperCase() === "UMACREDITOS" || payment_data?.method?.toUpperCase() === "MERCADOPAGO"  // Paciente pagó la consulta
  const isSubscriptor = payment_data?.method?.toUpperCase() === "SUBSCRIPTION" // Paciente es subscriptor
  const isEmergencias = ["EMERGENCIAS", "PODER JUDICIAL"].includes(payment_data?.id?.toUpperCase()) // Paciente es parte del B2C con emer
  const isPriorizedCorporate = ["OSFATUN"].includes(payment_data?.id?.toUpperCase())

  if (isSubscriptor) {
    score += criteria.subscription
    Logger.log(`[SCORING][SUBSCRIPTION] Paciente es subscriptor, nuevo score: ${score} paymentMethod: ${payment_data?.method?.toUpperCase()} uid: ${id}`)
  }

  if (isEmergencias) {
    score += criteria.emergencias
    Logger.log(`[SCORING][EMERGENCIAS] Paciente es parte del B2C con emer, nuevo score: ${score} paymentMethod: ${payment_data?.method?.toUpperCase()} uid: ${id}`)
  }

  if (isPaid || isPriorizedCorporate) {
    score += criteria.paid
    Logger.log(`[SCORING][PAYMENT] Paciente pago la consulta, nuevo score: ${score} paymentMethod: ${payment_data?.method?.toUpperCase()} uid: ${id}`)
  }

  if (effectiveAverage.isNewUser) {
    score += criteria.newUser
    Logger.log(`[SCORING][NEW_PATIENT] Paciente nuevo, nuevo score: ${score} paymentMethod: ${payment_data?.method?.toUpperCase()} uid: ${id}`)
  }

  if (effectiveAverage.effectivePercentage >= 80) {
    score += criteria.effectivePercentage
    Logger.log(`[SCORING][EFFECTIVENESS] Paciente tiene buen ratio de efectividad, nuevo score: ${score} paymentMethod: ${payment_data?.method?.toUpperCase()} porcentaje: ${effectiveAverage.effectivePercentage} uid: ${id}`)
  }

  return score
}

const getPreviousAppointments = async (patient: IPatient<FirebaseFirestore.Timestamp>) => {
  const records = await MedicalRecordRepository.getAllByUid(patient.uid)
  const effectiveAppointments = records.filter((record) => {
    record?.mr?.destino_final in effectiveFinalDestinations
  })

  const notEffectiveAppointments = records.filter((record) => {
    record?.mr?.destino_final in notEffectiveFinalDestinations
  })

  return { effectiveAppointments, notEffectiveAppointments }
}

const getTodayAppointments = async (effectiveAppointments: IMedicalRecord<FirebaseFirestore.Timestamp>[], notEffectiveAppointments: IMedicalRecord<FirebaseFirestore.Timestamp>[]) => {
  const today = startOfDay(new Date())

  const todayNotEffective = notEffectiveAppointments.filter(doc => {
    const docDate = doc?.timestamps?.dt_close?.toDate()
    return isSameDay(docDate, today) && doc?.payment_data?.price !== 0
  })

  const todayEffective = effectiveAppointments.filter(doc => {
    const docDate = doc?.timestamps?.dt_close?.toDate()
    return isSameDay(docDate, today) && doc?.payment_data?.price !== 0
  })

  return {
    effective: todayEffective.length,
    notEffective: todayNotEffective.length
  }
}

const getEffectiveAverage = async (effectiveAppointments: IMedicalRecord<FirebaseFirestore.Timestamp>[], notEffectiveAppointments: IMedicalRecord<FirebaseFirestore.Timestamp>[]) => {
  const effectiveCount = effectiveAppointments.length
  const notEffectiveCount = notEffectiveAppointments.length
  const totalCount = effectiveCount + notEffectiveCount

  if (totalCount === 0) {
    return {
      isNewUser: true,
      effectivePercentage: 0
    }
  }

  const effectivePercentage = (effectiveCount / totalCount) * 100
  return {
    isNewUser: false,
    effectivePercentage: totalCount > 10 ? effectivePercentage : 0
  }
}

export const scoringSelector = async (paymentData: paymentData, patient: IPatient<FirebaseFirestore.Timestamp>) => {
  const { effectiveAppointments, notEffectiveAppointments } = await getPreviousAppointments(patient)
  const todayAppointmentsData = await getTodayAppointments(effectiveAppointments, notEffectiveAppointments)
  const effectiveAverage = await getEffectiveAverage(effectiveAppointments, notEffectiveAppointments)

  let score = calculateBaseScore(patient, paymentData, effectiveAverage, baseScoringCriteria)
  score = applyModifiers(score, todayAppointmentsData, paymentData)

  logWarnings(score, paymentData, patient.uid)

  return score
}