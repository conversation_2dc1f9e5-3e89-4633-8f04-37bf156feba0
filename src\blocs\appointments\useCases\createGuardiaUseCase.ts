import { IDocumentList, AppointmentRepository, DependantRepository, PatientRepository, ProviderRepository } from "@umahealth/repositories"
import { dependantUid, IPatient, GuardiaAppointment, paymentData, countries, appointmentStates, call, IProvider } from "@umahealth/entities"
import { convertDateToTimezoneString } from "@umahealth/time"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, NotFoundException } from "@nestjs/common"
import { IGeo } from "src/patient-app/guardia/guardia.interface"

/** Cuando el paciente pide una consulta de guardia, se genera la consulta y queda en un pool de donde los medicos van atendiendo */
/**
 * This method is called on the first workflow to take a Guardia appointment, and it creates an assignation document that will be updated later.
 */
export const CreateGuardiaAppointmentUseCase = async (
  assignationId: string,
  country: countries,
  coverageName: string,
  creationTime: Date,
  dependantUid: dependantUid,
  motivosDeConsulta: string,
  paymentData: paymentData,
  uid: string,
  vip: boolean,
  state?: appointmentStates,
  providerUid?: string,
  call?: IDocumentList<call>,
  geo?: IGeo
) => {
  try {
    let patientDocument: IPatient<Timestamp>
    if (dependantUid && typeof (dependantUid) !== "boolean") {
      patientDocument = await DependantRepository.getByUid(uid, dependantUid)
    } else {
      patientDocument = await PatientRepository.getByUid(uid)
    }
    if (!patientDocument) {
      throw new NotFoundException(`Could not find patient ${uid}`)
    }
    if (state === "ATT" && !providerUid){
      throw new NotFoundException("Cannot create an ATT appointment without providerUid")
    }

    let providerDocument: IProvider<Timestamp>
    if (providerUid){
      providerDocument = await ProviderRepository.getByProviderUid(providerUid)
    }

    const appointmentDocument = new GuardiaAppointment<Timestamp>()
    appointmentDocument.appointment_data = {
      motivos_de_consulta: motivosDeConsulta,
      alertas: ""
    }
    appointmentDocument.assignation_id = assignationId
    appointmentDocument.cm = coverageName || `UMA ${country}`
    appointmentDocument.country = country || "AR"
    appointmentDocument.cuil = "bag"
    appointmentDocument.cuit = providerDocument.cuit || "bag"
    appointmentDocument.especialidad = ""
    appointmentDocument.fullname = ""
    appointmentDocument.max_appointments = 1
    appointmentDocument.patient = {
      corporate: coverageName || `UMA ${country}`,
      country: country,
      dni: patientDocument.dni || "",
      dob: patientDocument.dob || "",
      fullname: patientDocument.fullname || "",
      sex: patientDocument.sex || "",
      uid: uid,
      uid_dependant: dependantUid,
      ws: patientDocument.ws || "",
      geo: geo || {
        lat: null,
        lon: null,
        declarativeLocation: null
      }
    }
    appointmentDocument.payment_data = paymentData
    appointmentDocument.social_work = ""
    appointmentDocument.state = state || "ASSIGN"
    appointmentDocument.timestamps = {
      dt_create: Timestamp.fromDate(creationTime),
      dt_start: Timestamp.fromDate(creationTime)
    }
    appointmentDocument.vip = vip || false

    // unicamente para specificAppointmentForPatientAndProvider
    if (state === "ATT" && providerUid) {
      appointmentDocument.provider = appointmentDocument.provider || {}
      appointmentDocument.provider.uid = providerUid
      appointmentDocument.room = call.data.room
      appointmentDocument.token = call.data.token
      appointmentDocument.uid = providerUid
    }

    /* This will be soon deprecated */
    appointmentDocument.date = convertDateToTimezoneString(creationTime, "YYYY-MM-DD")
    appointmentDocument.datetime = convertDateToTimezoneString(creationTime, "YYYYMMDDHHmm")
    appointmentDocument.time = convertDateToTimezoneString(creationTime, "HH:mm")

    return await AppointmentRepository.create("bag", country, assignationId, appointmentDocument)
  } catch (err) {
    throw new InternalServerErrorException(`[ Appointments | patient | createGuardia ] => ${err.message}`)
  }
}
