import {InternalServerErrorException, Logger} from "@nestjs/common"
import {getCompanyIdFromAirtable, saveUserToAirtable} from "src/utils/airtable"
import {INom035UserPositionSQL, INom035UserSQL, INom035_csvBody} from "../interfaces"
import nom035DTO from "../models/nom035_userPositionDTO"
import {sendUpdateCacheMessage} from "./cache/sendUpdateCacheMessage"
import {EventEmitter2} from "@nestjs/event-emitter"
import {v4 as uuid} from "uuid"
import {CreateUsersFromCSVEvent} from "src/portal-app/nom035/user/events/createUsersFromCSV.event"
import * as moment from "moment"
import {Nom035Users} from "@umahealth/sql-models"

export const createUsersByCSV = async (corporateInfo: {id: string; value: string}, usersFromCsv: INom035_csvBody[], eventEmitter: EventEmitter2) => {
  try {
    Logger.log(`[${createUsersByCSV.name}] -> Attempting to create ${usersFromCsv.length} users.`)
    const newUsers = await createUsersInSQL(corporateInfo, usersFromCsv)

    if (!newUsers.length) {
      return {
        createdSuccessfully: 0,
        newUsers: 0,
        repeatedUsers: 0,
      }
    }
    const payload = usersFromCsv.reduce((acc, user) => {
      const matchedUser = (newUsers as Nom035Users[]).find((userSql) => userSql.email === user.email)
      if (matchedUser) {
        acc.push({
          id: Number(matchedUser.id),
          ...user
        })
      }
      return acc
    }, [] satisfies INom035_csvBody[])
    try {
      await createUsersInAirtable(payload, corporateInfo)
    } catch (error) {
      throw new InternalServerErrorException(`[ Nom035 | createUsersInAirtable ] Error on creating user documents in Airtable. Error: ${error.message}`)
    }

    eventEmitter.emit("createUsersFromCSVEvent", new CreateUsersFromCSVEvent(
      payload,
    ))

    await sendUpdateCacheMessage(corporateInfo.value, "users")

    return {
      createdSuccessfully: newUsers,
      newUsers: newUsers.length,
      repeatedUsers: usersFromCsv.length - newUsers.length,
    }
  } catch (error) {
    throw new InternalServerErrorException(`[ Nom035 | Create users ] Error: ${error.message}`)
  }
}

const createUsersInSQL = async (corporateInfo: {id: string; value: string}, usersFromCSV: INom035_csvBody[]) => {
  const usersWithEmail = usersFromCSV.filter((item) => item.email)

  if (!usersWithEmail.length) {
    return usersWithEmail
  }

  const emailFilter = usersWithEmail.map((user) => user.email)
  const attributeOptions = ["email", "id"]

  const usersFromSql = await nom035DTO.getAllUsers(attributeOptions, emailFilter)

  const createdUsers: INom035UserSQL[] = []

  usersWithEmail.forEach((user) => {
    const INom035UserSQL: INom035UserSQL = {
      uid: uuid(),
      fullname: user.fullname,
      nationalId: user.nationalId,
      birthDate: user?.birthDate || null,
      gender: user.gender,
      email: user.email,
      phoneNumber: user.phone,
      maritalStatus: "",
      active: true
    }

    const exists = usersFromSql.some((item) => item.email === user.email)
    if (!exists) {
      createdUsers.push(INom035UserSQL)
    }
  })

  if (!createdUsers.length) {
    return createdUsers
  }

  const newUsers = await nom035DTO.createUserInSQLBulk(createdUsers)

  if (!newUsers.length) {
    return newUsers
  }


  const userPositionToSql = usersWithEmail.reduce((acc, user) => {
    const userId = newUsers.find((item) => item.email === user.email)?.id

    if (!userId) {
      return acc
    }
    acc.push({
      userId: userId,
      corporateId: corporateInfo.value || null,
      branch: user.branch || null,
      area: user.area || null,
      seniority: user.seniority || null,
      isActive: true,
    } satisfies INom035UserPositionSQL)

    return acc
  }, [] satisfies INom035UserPositionSQL[])

  await nom035DTO.createUserPositionInSQLBulk(userPositionToSql)

  return newUsers
}

const createUsersInAirtable = async (users: INom035_csvBody[], corporateInfo: {id: string; value: string}) => {
  const companyId = await getCompanyIdFromAirtable("empresa", corporateInfo.value)

  const saveInAirtable = users.map(async (user) => {
    const payload = {
      "nomina": user.fullname,
      "INE / FM": user.nationalId,
      "fecha nacimiento": moment(user.birthDate, "YYYY-MM-DD").startOf("day").toISOString(),
      "genero": user.gender === "M" ? "Masculino" : user.gender === "F" ? "Femenino" : "Sin especificar",
      "correo": user.email,
      "telefono": user.phone || "",
      "area": user.area || "",
      "c trabajo": user.branch || "",
      "seniority": user.seniority,
      "empresa": [companyId],
      "activo": true,
    }

    await saveUserToAirtable("nomina", payload)
  })

  return await Promise.all(saveInAirtable)
}
