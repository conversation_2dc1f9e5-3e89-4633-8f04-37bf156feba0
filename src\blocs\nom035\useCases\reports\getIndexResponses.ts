import { base } from "src/utils/airtable/airtableConfiguration"

export const getIndexResponses = async (corporateId: string) => {
  const table = base("indice")
  const indexResponses = await table.select({ filterByFormula: `{empresa (from nomina)}="${corporateId}"` }).all()
  const responses = indexResponses.map(record => {
    record.fields.id = record.id
    return record.fields
  })
  return responses
}