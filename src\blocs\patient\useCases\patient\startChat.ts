import { InternalServerErrorException } from "@nestjs/common"
import { appointmentServices, chat, countries, dependantUid, TSpecialties } from "@umahealth/entities"
import { AppointmentRepository, PatientRepository } from "@umahealth/repositories"

export async function startChat(assignation_id: string, country: countries, dependantUid: dependantUid, service: appointmentServices, specialty: TSpecialties | false, uid: string) {
  const assignationPath = AppointmentRepository.getPath(service, country)

  try {
    const callObj: chat = {
      activeUid: uid,
      assignationPath: `${assignationPath}/${assignation_id}`,
      assignation_id: assignation_id,
      chatting: true,
      cuit: service,
      dependant: dependantUid,
      requested: true,
      type: specialty ? specialty : "chat",
      unreadMessagesDoctor: 0,
      dtLastMessageDoctor: null,
      unreadMessagesPatient: 0,
      dtLastMessagePatient: null
    }
    const completeChat = await PatientRepository.updateChatAtt(uid, callObj)
    return completeChat
  } catch (err) {
    throw new InternalServerErrorException(`[ Patient | startChat ] => ${err.message}`)
  }
}
