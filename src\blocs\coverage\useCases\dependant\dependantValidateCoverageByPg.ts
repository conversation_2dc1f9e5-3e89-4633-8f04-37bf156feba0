import { IHealthInsurance } from "@umahealth/entities"
import { DependantRepository,  } from "@umahealth/repositories"
import { InternalServerErrorException, Logger, NotAcceptableException, NotFoundException } from "@nestjs/common"
import { Timestamp } from "@google-cloud/firestore"
import { Padrones } from "@umahealth/sql-models"


export const DependantValidateCoverageByPgUseCase = async (validatedBy:string, healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> => {
  try {
    let exists = false

    switch(validatedBy){
    case "dni": {
      const dependant = await DependantRepository.getByUidFromDependant(healthInsurance.uid)
      if(!dependant) throw new NotFoundException(`[ Coverages | dependant | validateByPg ] Dependant not found - UID: ${dependant.dni}`)

      const data = await Padrones.findOne({ where: { document: dependant.dni, active: true }, raw: true})
      exists = Boolean(data)
      break
    }
    case "affiliate_number":{
      const data = await Padrones.findOne({ where: { affiliateNumber: healthInsurance.affiliate_id, active: true }, raw: true})
      exists = Boolean(data)
      break
    }
    default:
      throw new NotAcceptableException("Invalid coverage validation type")
    }

    return exists

  } catch (err) {
    Logger.error(`[ Coverages | dependant | validateByPg ] => ${err}`)
    throw new InternalServerErrorException(`[ Coverages | dependant | validateByPg ] =>  Error validating coverage by PG with coverageData: ${healthInsurance}`)
  }
}

