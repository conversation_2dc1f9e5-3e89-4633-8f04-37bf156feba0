import { InternalServerErrorException, Logger } from "@nestjs/common"
import { ISpecialtyNotificationEvent, TSpecialties } from "@umahealth/entities"
import { NotificationRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"


export async function getUserSpecialtyNotification(uid: string, specialty?:TSpecialties): Promise<TSpecialties[]> {
  try {
    const specialtiesRequeted: ISpecialtyNotificationEvent<Timestamp>[] = await NotificationRepository.getUserSpecialtyNotifications(uid, specialty)
    return specialtiesRequeted?.map(obj => obj.specialty)
  } catch(error) {
    Logger.error(`[ getUserSpecialtyNotification ] - Error searching notifications uid: ${uid}, error: ${error}`)
    throw new InternalServerErrorException("getUserSpecialtyNotification - Error getting user notification")
  }
}