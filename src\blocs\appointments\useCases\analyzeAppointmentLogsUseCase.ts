import { Logger } from "@nestjs/common"
import { AppointmentRepository } from "@umahealth/repositories"
import { IPatientLog, IProviderLog } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { generateConnectionHistory, generatePrescriptionHistory, generateJoinRoomHistory } from "../utils/analyzeAppointmentLogs/connectionHistory"
import { calculateTotalTimeConnected, calculateOverlappingTime } from "../utils/analyzeAppointmentLogs/timeCalculations"
import { analyzeTechnicalIssues } from "../utils/analyzeAppointmentLogs/technicalIssues"
import { generateAnalysis } from "../utils/analyzeAppointmentLogs/analysisGenerator"
import { AppointmentsBloc } from "../appointments.bloc"
import { IAppointmentLogsAnalysis } from "../appointments.entities"

/**
 * Analiza los logs de una consulta médica y genera un reporte detallado
 * del estado de la conexión, problemas técnicos y tiempos de conexión
 */
export const analyzeAppointmentLogsUseCase = async (
  assignationId: string,
  patientUid?: string,
  providerUid?: string
): Promise<IAppointmentLogsAnalysis> => {
  Logger.log(`[ ${AppointmentsBloc.name} | ${analyzeAppointmentLogsUseCase.name} ] Analizando logs para consulta ${assignationId}`)

  // Obtener logs
  let patientLogs: IPatientLog<Timestamp>[] = []
  let providerLogs: IProviderLog<Timestamp>[] = []

  // Obtener solo los logs de los UIDs proporcionados
  if (patientUid) {
    patientLogs = await AppointmentRepository.getLogsAppointment(assignationId, patientUid) as IPatientLog<Timestamp>[]
    Logger.log(`[ ${AppointmentsBloc.name} | ${analyzeAppointmentLogsUseCase.name} ] Logs obtenidos del paciente con uid: ${patientUid}`)
  }

  if (providerUid) {
    providerLogs = await AppointmentRepository.getLogsAppointment(assignationId, null, providerUid) as IProviderLog<Timestamp>[]
    Logger.log(`[ ${AppointmentsBloc.name} | ${analyzeAppointmentLogsUseCase.name} ] Logs obtenidos del médico con uid: ${providerUid}`)
  }

  // Generar historiales de conexión solo para los participantes con UID
  const patientHistory = patientUid ? generateConnectionHistory(patientLogs) : []
  const providerHistory = providerUid ? generateConnectionHistory(providerLogs) : []

  // Generar historial de intentos de prescripción para el proveedor
  const prescriptionHistory = providerUid ? generatePrescriptionHistory(providerLogs, providerUid) : []

  // Generar historial de eventos joinRoom del paciente
  const joinRoomHistory = patientUid ? generateJoinRoomHistory(patientLogs, patientUid) : []

  // Calcular tiempo de conexion simultanea
  const overlappingTime = calculateOverlappingTime(patientHistory, providerHistory)
  Logger.log(`[ ${AppointmentsBloc.name} | ${analyzeAppointmentLogsUseCase.name} ] Tiempo de conexión simultánea: ${overlappingTime.minutes} minutos`)

  // Analizar problemas técnicos
  const technicalIssues = analyzeTechnicalIssues(patientUid ? patientLogs : [], providerUid ? providerLogs : [])

  // Generar análisis
  const analysis = generateAnalysis(
    patientHistory,
    providerHistory,
    technicalIssues,
    {
      patientUidProvided: !!patientUid,
      providerUidProvided: !!providerUid
    },
    overlappingTime
  )

  const result: IAppointmentLogsAnalysis = {
    analysis,
    assignation_id: assignationId,
    patient: patientUid ? {
      connection_history: patientHistory,
      total_time_connected: calculateTotalTimeConnected(patientHistory),
      current_status: patientHistory.some(p => !p.disconnected_at) ? "connected" : "disconnected",
      join_room_history: joinRoomHistory
    } : {
      connection_history: [],
      total_time_connected: { minutes: 0, seconds: 0 },
      current_status: "unknown",
      info: "No se proporcionó uid del paciente"
    },
    provider: providerUid ? {
      connection_history: providerHistory,
      total_time_connected: calculateTotalTimeConnected(providerHistory),
      current_status: providerHistory.some(p => !p.disconnected_at) ? "connected" : "disconnected",
      prescription_history: prescriptionHistory
    } : {
      connection_history: [],
      total_time_connected: { minutes: 0, seconds: 0 },
      current_status: "unknown",
      info: "No se proporcionó uid del médico"
    },
    simultaneous_connection: overlappingTime,
    technical_issues: technicalIssues,
  }

  Logger.log(`[ ${AppointmentsBloc.name} | ${analyzeAppointmentLogsUseCase.name} ] Análisis de logs completado para consulta con assignation_id: ${assignationId}`)
  return result
}
