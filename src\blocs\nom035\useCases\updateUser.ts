import { NotFoundException } from "@nestjs/common"
import { PatientRepository } from "@umahealth/repositories"
import { IEditUserBody } from "src/portal-app/nom035/user/user.entities"
import { updateUserInAirtable } from "src/utils/airtable"
import nom035DTO from "../models/nom035_userPositionDTO"
import { adaptEditUserToAirtableFormat } from "../utils/functions"
import { sendUpdateCacheMessage } from "./cache/sendUpdateCacheMessage"
import { getUserByUid } from "@umahealth/auth"

const updateUser = async (uid: string, data: Partial<IEditUserBody>, corporate: string) => {

  const patient = await getUserByUid(uid)

  if(!patient){
    throw new NotFoundException(`[ Nom035 | updateUser ] Patient user with uid ${uid} not found`, {
      description: `Patient user with UID ${uid} not found`
    })
  }

  if(Object.keys(data.profile).length > 0){

    // Actualizo el perfil en firestore
    await PatientRepository.update(uid, {ws: data.profile.phone})

    // Actualizo el perfil en SQL
    const sqlData = {
      phoneNumber: data.profile.phone,
      maritalStatus: data.profile.maritalStatus
    }
    await nom035DTO.updateUser(uid, sqlData)

  }

  // Actualizo la position en la SQL
  if(Object.keys(data.position).length > 0){

    const userId = await nom035DTO.getUserByUid(uid)
    await nom035DTO.updateUserPosition(userId.id, data.position)

  }

  const AirtableAdapter = adaptEditUserToAirtableFormat(data)
  await updateUserInAirtable("nomina", patient.email , AirtableAdapter)

  // Update users cache
  await sendUpdateCacheMessage(corporate, "users")

  return { message: "User updated successfully", updated: true }

}


export { updateUser }
