import { FileDataPart, GenerateContentRequest, GenerateContentResult, HarmBlockThreshold, HarmCategory, TextPart, VertexAI } from "@google-cloud/vertexai"
import { BadRequestException, InternalServerErrorException, Logger } from "@nestjs/common"
import { IValidateDniProps, IValidateDniResponse, VertexResponse } from "src/patient-app/patient/patient.entities"
import { removeTildes } from "src/utils/removeTildes"
import { storageBucketName } from "src/utils/variables"
import { retryRequest } from "../../utils/retryRequest"
import { validationPrompts } from "../../utils/vertexValidationsPrompts"

export const validateDniUseCase = async ({ dni, documentType, fullname, sex, imageUrl }: IValidateDniProps): Promise<IValidateDniResponse> => {
  const vertex_ai = new VertexAI({ project: process.env.PROJECT_ID, location: "us-central1" })
  const model = "gemini-1.5-flash"
  const numberRegex = /\d+/

  const generativeModel = vertex_ai.preview.getGenerativeModel({
    model: model,
    generationConfig: {
      "maxOutputTokens": 450,
      "topP": 0.4,
      "topK": 32,
    },
    safetySettings: [
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
    ],
  })

  const filePart: FileDataPart = {
    fileData: {
      mimeType: "image/jpeg",
      fileUri: `gs://${storageBucketName}/${imageUrl}`,
    }
  }

  const textPart: TextPart = {
    text: validationPrompts[documentType] || validationPrompts["DNI"]
  }

  const req: GenerateContentRequest = {
    contents: [
      { role: "user", parts: [textPart, filePart] }
    ],
  }

  try {
    const streamingResp = await retryRequest<GenerateContentResult>(generativeModel, "generateContent", req)
    Logger.debug(streamingResp)
    const textResponse =
      streamingResp.response.candidates[0].content.parts?.length ?
        streamingResp.response.candidates[0].content.parts[0]?.text :
        ""

    if (!textResponse) {
      throw new InternalServerErrorException("Vertex did not provide a text response")
    }
    if (!textResponse.includes("```json\n")) {
      throw new BadRequestException("Mala calidad de imagen")
    }
    const JsonResponse: VertexResponse = JSON.parse(textResponse.replace("```json\n", "").replace("\n```", ""))

    let isValid = false
    if (JsonResponse.dni === "12345678") throw new BadRequestException("Mala calidad de imagen")
    // Si el numero reconocido tiene una letra en la primera posicion, la eliminamos.
    // Esto suele pasar con personas DNI de 7 digitos que tienen el DNI tarjeta viejo, donde te ponian el sexo delante del numero
    // Ejemplo: M1234567
    if (!numberRegex.test(JsonResponse.dni[0])) {
      JsonResponse.dni = JsonResponse.dni.slice(1)
    }
    if (JsonResponse.dni === dni) {
      isValid = true
    } else if (
      removeTildes(JsonResponse.nombre?.toUpperCase().trim()) === removeTildes(fullname.toUpperCase().trim())
      && JsonResponse.sexo?.toUpperCase().trim() === sex.toUpperCase().trim()
    ) {
      isValid = true
    }
    Logger.debug(`[ validateDniUseCase ] => vertexResponse: dni=${JsonResponse.dni}, nombre=${JsonResponse.nombre}, sexo=${JsonResponse.sexo}`)
    Logger.debug(`[ validateDniUseCase ] => inputValues: dni=${dni}, nombre=${fullname}, sexo=${sex}, imageUrl=${imageUrl}}`)
    return {
      isValid,
      vertexResponse: JsonResponse
    }
  } catch (err) {
    if (err instanceof BadRequestException) {
      throw new BadRequestException(`[ validateDniUseCase ] => Err: ${JSON.stringify(err)}`)
    }  else {
      throw new InternalServerErrorException(`[ validateDniUseCase ] => Err: ${JSON.stringify(err)}`)
    }
  }
}