import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getCorporateById } from "../corporate/getCorporateById"

export const updateCorporateCache = async (redis: Redis, corporateId: string) => {
  Logger.log(`[Nom035 | updateCorporateCache] Updating corporate details for corporate: ${corporateId}`)

  const corporateDetails = await getCorporateById(corporateId)

  const redisExpiration = 86400 // 24 hours

  await redis.set(`corporate:${corporateId}:details`, JSON.stringify(corporateDetails), "EX", redisExpiration)
}