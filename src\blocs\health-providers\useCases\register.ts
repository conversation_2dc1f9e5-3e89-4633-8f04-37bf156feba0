import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { IProvider } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"

export async function registerProvider(data: IProvider<Timestamp>) {
  const newProvider = await ProviderRepository.create(data)
  if (!newProvider) throw new InternalServerErrorException("[ Providers | register ] => Could not create provider")
  return newProvider
}
