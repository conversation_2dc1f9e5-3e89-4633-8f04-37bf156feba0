import { IAppointmentsCampaign } from "@umahealth/entities"
import { NotificationType } from "../../notifications.bloc.interface"
import { CorporatesNotificationsEnum, CorporatesNotificationsType } from "./corporateNotificationsMap"

interface IGetNotificationConfig {
  attentionDate: string,
  attentionTime: string,
  corporate: CorporatesNotificationsType,
  dynamicLink: string,
  dynamicLinkQueue: string,
  homeUrl: string,
  patientFullname: string,
  providerFullname: string,
  type: NotificationType,
}

interface INotificationSetup {
  params: string[]
  template: keyof IAppointmentsCampaign
}

interface IDateConfig {
  [dateKey: string]: INotificationSetup
}

type IEntityConfig = {
  [K in CorporatesNotificationsType]?: IDateConfig
}


type INotificationsConfig = {
  [K in NotificationType]?: IEntityConfig
}

export const getNotificationsConfig = ({
  attentionDate,
  attentionTime,
  corporate,
  dynamicLink,
  dynamicLinkQueue,
  homeUrl,
  patientFullname,
  providerFullname,
  type
}: IGetNotificationConfig): IDateConfig => {
  const notificationConfigs: INotificationsConfig = {
    whatsapp: {
      [CorporatesNotificationsEnum.IOMA]: {
        dateOneDayBefore: {
          params: [patientFullname, providerFullname, attentionDate, attentionTime, dynamicLink, homeUrl],
          template: "appointment_reminder_24_ioma"
        },
        dateOneHourBefore: {
          params: [patientFullname, attentionTime, providerFullname, dynamicLink],
          template: "appointment_reminder_1_ioma"
        },
        dateEqualAssignation: {
          params: [dynamicLinkQueue],
          template: "appointment_queue_reminder_ioma"
        },
        dateTenMinutesBefore: {
          params: [patientFullname, providerFullname, attentionTime, dynamicLink],
          template: "appointment_queue_reminder_ioma"
        }
      },
      [CorporatesNotificationsEnum.UMA]: {
        dateOneDayBefore: {
          params: [patientFullname, providerFullname, attentionDate, attentionTime, dynamicLink, homeUrl],
          template: "appointment_reminder_24"
        },
        dateOneHourBefore: {
          params: [patientFullname, attentionTime, providerFullname, dynamicLink],
          template: "appointment_reminder_1"
        },
        dateEqualAssignation: {
          params: [dynamicLinkQueue],
          template: "appointment_queue_reminder"
        },
        dateTenMinutesBefore: {
          params: [patientFullname, providerFullname, attentionTime, dynamicLink],
          template: "appointment_queue_reminder"
        }
      }
    },
    email: {
      [CorporatesNotificationsEnum.IOMA]: {
        dateOneDayBefore: {
          params: [attentionTime],
          template: "ioma_appointment_reminder_1dayBefore"
        },
        dateFourHoursBefore: {
          params: [attentionTime],
          template: "ioma_appointment_reminder_today"
        },
        dateOneHourBefore: {
          params: [attentionTime],
          template: "ioma_appointment_reminder_today"
        }
      },
      [CorporatesNotificationsEnum.UMA]: {
        dateOneDayBefore: {
          params: [patientFullname, providerFullname, attentionDate, dynamicLink],
          template: "appointment_reminder_24"
        },
        dateFourHoursBefore: {
          params: [attentionTime, dynamicLink],
          template: "appointment_reminder_4"
        },
        dateOneHourBefore: {
          params: [dynamicLink, attentionTime],
          template: "appointment_reminder_1"
        }
      },
      [CorporatesNotificationsEnum.FARMATODO]: {
        dateOneDayBefore: {
          params: [attentionTime],
          template: "farmatodo_appointment_reminder_1dayBefore"
        },
        dateFourHoursBefore: {
          params: [attentionTime],
          template: "farmatodo_appointment_reminder_today"
        },
        dateOneHourBefore: {
          params: [attentionTime],
          template: "farmatodo_appointment_reminder_today"
        }
      }
    }
  }
  return notificationConfigs[type]?.[corporate]
}