import { format, subDays, subHours, subMinutes } from "date-fns"
import { Timestamp } from "@google-cloud/firestore"

export const getNotificationsDates = (dt_assignation: Timestamp) => {
  const dateOneDayBefore = format(subDays(dt_assignation.toDate(), 1), "yyyyMMddHHmm")
  const dateFourHoursBefore = format(subHours(dt_assignation.toDate(), 4), "yyyyMMddHHmm")
  const dateOneHourBefore = format(subHours(dt_assignation.toDate(), 1), "yyyyMMddHHmm")
  const dateEqualAssignation = format(dt_assignation.toDate(), "yyyyMMddHHmm")
  const dateTenMinutesBefore = format(subMinutes(dt_assignation.toDate(), 10), "yyyyMMddHHmm")
  const attentionDate = format(dt_assignation.toDate(), "yyyy-MM-dd")
  const attentionTime = format(dt_assignation.toDate(), "HH:mm")

  return {
    dateOneDayBefore,
    dateFourHoursBefore,
    dateOneHourBefore,
    dateEqualAssignation,
    dateTenMinutesBefore,
    attentionDate,
    attentionTime
  }
}