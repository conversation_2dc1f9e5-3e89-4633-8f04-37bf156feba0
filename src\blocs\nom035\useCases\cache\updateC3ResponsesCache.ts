import { Logger } from "@nestjs/common"
import { Redis } from "ioredis"
import { getC3Responses } from "../reports/getC3Responses"

export const updateC3ResponsesCache = async (redis: Redis, corporateId: string) => {
  Logger.log(`[Nom035 | updateC3ResponsesCache] Updating C3 responses for corporate: ${corporateId}`)

  const c3Responses = await getC3Responses(corporateId)

  const redisKeyPrefix = `corporate:${corporateId}`
  const redisExpiration = 86400 // 24 hours

  await redis.set(`${redisKeyPrefix}:form:c3:responses`, JSON.stringify(c3Responses), "EX", redisExpiration)
}