import { InternalServerErrorException } from "@nestjs/common"
import { DengueChatbotMessage } from "@umahealth/sql-models"
import { dengueSendChatbotMessage } from "src/blocs/ai-models/useCases"

export const sendMessage = async (text: string, conversation_id: number) => {
  const aiChatbotRequest = await dengueSendChatbotMessage(text, conversation_id.toString())
  if (!aiChatbotRequest) throw new InternalServerErrorException("Error in AI API call")

  const patientMessage = await DengueChatbotMessage.create({
    conversation_id,
    text_message: text,
    rol: "patient",
  })

  const chatbotMessage = await DengueChatbotMessage.create({
    conversation_id,
    text_message: aiChatbotRequest.response_to_user,
    rol: "ai",
  })

  return { chatbotMessage, patientMessage }
}
