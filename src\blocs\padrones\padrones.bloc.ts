import { updateWithOperations } from "./useCases/updateWithOperations"
import { IPadrones } from "@umahealth/entities"
import { Injectable } from "@nestjs/common"
import { update } from "./useCases/update"

@Injectable()
export class PadronesBloc{
  async updatePadron(corporate: string, rows: IPadrones[]) {
    return await update(corporate, rows)
  }

  async updatePadronWithOperations(corporate: string, rows: IPadrones[]) {
    return await updateWithOperations(corporate, rows)
  }

}
