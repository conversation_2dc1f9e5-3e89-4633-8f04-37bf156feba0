import { Timestamp } from "@google-cloud/firestore"
import { Logger, NotFoundException } from "@nestjs/common"
import { IPatient } from "@umahealth/entities"
import { DependantRepository, PatientRepository } from "@umahealth/repositories"

export const find = async (uid: string): Promise<IPatient<Timestamp>> => {
  const patientExists = await PatientRepository.getByUid(uid)
  if(patientExists && typeof(patientExists) !== "boolean") return patientExists
  throw new NotFoundException(`Patient not found with uid: ${uid}`)
}

/**
 * Find a patient in /user or /dependant
 * @param patientUid patientUid
 * @returns patient or dependant as appropriate
 */
export const findPatientOrDependantUseCase = async (patientUid: string) => {
  const patient = await PatientRepository.getByUid(patientUid)
  if (patient) return patient

  Logger.log(`[ findPatientOrDependantUseCase ] => Patient with ${patientUid} not found in /user path, trying in /dependants`)

  const dependant = await DependantRepository.getByUidFromDependant(patientUid)
  if (dependant) return dependant

  throw new NotFoundException(`[ findPatientOrDependantUseCase ] => Patient with uid ${patientUid} not found`)
}
