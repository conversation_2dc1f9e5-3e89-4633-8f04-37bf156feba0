import { Timestamp } from "@google-cloud/firestore"
import { Logger } from "@nestjs/common"
import { AppointmentRepository } from "@umahealth/repositories"
import { sendSMS } from "src/utils/messaging/messaging.helper"

export const sendUnreadNotificationUseCase = async () => {
  const now = Timestamp.now()
  const fifteenMinutesFromNow = now.toMillis() - 15 * 60 * 1000
  const sixteenMinutesFromNow = now.toMillis() - 16 * 60 * 1000
  const fifteenMinutesFromNowTimestamp = Timestamp.fromMillis(fifteenMinutesFromNow)
  const sixteenMinutesFromNowTimestamp = Timestamp.fromMillis(sixteenMinutesFromNow)
  const unreadChatAttDocuments = await AppointmentRepository.getAllChatAttByLastDoctorMessage(fifteenMinutesFromNowTimestamp, sixteenMinutesFromNowTimestamp)
  for (const unreadChat of unreadChatAttDocuments) {
    const patientWs = unreadChat.patient.ws
    const assignation_id = unreadChat.assignation_id
    Logger.log(`Notifying: ${assignation_id}`)
    const text = "Tienes un mensaje sin leer de tu profesional de la salud. Puedes encontrarlo en https://pacientes.umasalud.com"
    Logger.log("SMS enviado a:", patientWs)
    await sendSMS(patientWs, text, "unread_notification_by_provider")
  }
}
