import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"
import { countries, IOnDemandAppointment} from "@umahealth/entities"
import { AppointmentRepository, IDocumentList } from "@umahealth/repositories"

export const UpdateOnDemandAppointmentUseCase = async (
  assignationId: string,
  country: countries,
  appointment: Partial<IOnDemandAppointment<Timestamp>>
): Promise<IDocumentList<Partial<IOnDemandAppointment<Timestamp>>>> => {
  const updatedOnDemandAppointment = await AppointmentRepository.update("onDemand", country, assignationId, appointment)

  if(!updatedOnDemandAppointment) throw new InternalServerErrorException(`[ Appointments | updateOnDemandAppointment ] => Error updating onDemand attention appointment. Id: ${assignationId}`)

  return updatedOnDemandAppointment
}