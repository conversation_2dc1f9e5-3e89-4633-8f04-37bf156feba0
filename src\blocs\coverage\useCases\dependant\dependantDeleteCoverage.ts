import { HealthInsuranceRepository } from "@umahealth/repositories"
import { IHealthInsurance } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/storage"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException, NotFoundException } from "@nestjs/common"


export const DependantDeleteCoverageUseCase = async (uid: string, coverageId:string): Promise<IDocumentList<IHealthInsurance<Timestamp>>> => {

  const coverage = await HealthInsuranceRepository.dependantGetByName(uid, coverageId)
  if( typeof coverage === "boolean" ) throw new NotFoundException(`[ Coverages | dependant | delete ] Coverage not found - dependantUid: ${uid} coverage: ${coverageId}`)

  const document = await HealthInsuranceRepository.dependantDelete(uid, coverage)
  if(!document) throw new InternalServerErrorException("[ Coverages | dependant | delete ] Error deleting dependant coverage")
  return document


}
