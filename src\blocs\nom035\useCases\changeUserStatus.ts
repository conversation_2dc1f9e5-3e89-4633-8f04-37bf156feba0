import { NotFoundException } from "@nestjs/common"
import { updateUserStatusInAirtable } from "src/utils/airtable"
import nom035DTO from "../models/nom035_userPositionDTO"
import { sendUpdateCacheMessage } from "./cache/sendUpdateCacheMessage"
import { getUserByUid } from "@umahealth/auth"
import { FieldSet}  from "airtable"


export const changeUserStatus = async (uid: string, users: FieldSet[], corporateId: string) => {

  const user = await getUserByUid(uid)
  if(!user){
    throw new NotFoundException(`[ ${changeUserStatus.name} ] User with uid ${uid} not found.`)
  }

  const userRecord = users.find((record) => record.correo === user.email)

  await nom035DTO.updateUserStatus(uid, !userRecord.activo)
  // Luego cambiar esto, no es seguro buscar por emails
  await updateUserStatusInAirtable("nomina", user.email, !userRecord.activo, corporateId)

  // Update users cache
  await sendUpdateCacheMessage(corporateId, "users")

  return { actualStatus: !userRecord.activo, previousStatus: userRecord.activo }
}
