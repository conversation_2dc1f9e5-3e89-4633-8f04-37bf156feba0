import { PatientRepository } from "@umahealth/repositories"

/**
 * Permite desactivar o activar la validación del telefono del usuario, por defecto si no se manda verify,
 *  se entiende que estás queriendo decir que el usuario valido el telefono.
 * @param uid Id del usuario
 * @param verify Si estas activando o no la validación
 */
export async function setPhoneVerification(uid: string, verified?: boolean){
  return await PatientRepository.setVerifiedPhone(uid, verified)
}
