import { InternalServerErrorException, Logger } from "@nestjs/common"
import { ISpecialtyNotificationEvent, TSpecialties } from "@umahealth/entities"
import { IDocumentList, NotificationRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"

export async function updateSpecialtyNotificationEventBatch(documentId: string, data: Partial<ISpecialtyNotificationEvent<Timestamp>>): Promise<IDocumentList<Partial<ISpecialtyNotificationEvent<Timestamp>>>> {
  try {
    return await NotificationRepository.updateSpecialtyNotificationEventBatch(documentId, data)
  } catch(error) {
    Logger.error(`[ updateSpecialtyNotificationEventBatch ] - Error updating notification - id: ${documentId}, error: ${error}`)
    throw new InternalServerErrorException("updateSpecialtyNotificationEventBatch")
  }
}