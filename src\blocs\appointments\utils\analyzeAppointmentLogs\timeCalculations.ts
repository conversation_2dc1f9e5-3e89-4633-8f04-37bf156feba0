import { IConnectionPeriod } from "../../appointments.entities"

/**
 * Calcula la duración entre dos fechas
 */
export function calculateDurationBetweenDates(start: Date, end: Date | null): { minutes: number, seconds: number } {
  const endTime = end ? end.getTime() : Date.now()
  const durationMs = endTime - start.getTime()

  const totalSeconds = Math.floor(durationMs / 1000)
  return {
    minutes: Math.floor(totalSeconds / 60),
    seconds: totalSeconds % 60
  }
}

/**
 * Calcula la duración entre dos fechas y devuelve también los minutos totales
 */
export function calculateDuration(startDate: Date, endDate: Date): { minutes: number, seconds: number, totalMinutes: number } {
  const diffMs = endDate.getTime() - startDate.getTime()
  const totalSeconds = Math.floor(diffMs / 1000)

  return {
    minutes: Math.floor(totalSeconds / 60),
    seconds: totalSeconds % 60,
    totalMinutes: Number((diffMs / (1000 * 60)).toFixed(2))
  }
}

/**
 * Calcula el tiempo total de conexión basado en un historial de conexiones
 */
export function calculateTotalTimeConnected(connectionHistory: IConnectionPeriod[]): { minutes: number, seconds: number } {
  if (connectionHistory.length === 0) {
    return { minutes: 0, seconds: 0 }
  }

  if (connectionHistory.length === 1) {
    return connectionHistory[0].duration
  }

  const totalSeconds = connectionHistory.reduce((total, period) => {
    const periodSeconds = (period.duration.minutes * 60) + period.duration.seconds
    return total + periodSeconds
  }, 0)

  return {
    minutes: Math.floor(totalSeconds / 60),
    seconds: totalSeconds % 60
  }
}

/**
 * Calcula el tiempo de superposición entre las conexiones del paciente y el médico
 */
export function calculateOverlappingTime(
  patientHistory: IConnectionPeriod[],
  providerHistory: IConnectionPeriod[]
): { minutes: number, seconds: number } {
  if (patientHistory.length === 0 || providerHistory.length === 0) {
    return { minutes: 0, seconds: 0 }
  }

  const overlappingPeriods: { start: number, end: number }[] = []

  patientHistory.forEach(patientPeriod => {
    const patientStart = patientPeriod.connected_at.getTime()
    const patientEnd = patientPeriod.disconnected_at ?
      patientPeriod.disconnected_at.getTime() :
      Date.now()

    providerHistory.forEach(providerPeriod => {
      const providerStart = providerPeriod.connected_at.getTime()
      const providerEnd = providerPeriod.disconnected_at ?
        providerPeriod.disconnected_at.getTime() :
        Date.now()

      if (patientStart <= providerEnd && providerStart <= patientEnd) {
        overlappingPeriods.push({
          start: Math.max(patientStart, providerStart),
          end: Math.min(patientEnd, providerEnd)
        })
      }
    })
  })

  overlappingPeriods.sort((a, b) => a.start - b.start)

  let totalOverlapMs = 0
  let currentPeriod = overlappingPeriods[0]

  for (let i = 1; i < overlappingPeriods.length; i++) {
    const nextPeriod = overlappingPeriods[i]

    if (currentPeriod.end >= nextPeriod.start) {
      currentPeriod.end = Math.max(currentPeriod.end, nextPeriod.end)
    } else {
      totalOverlapMs += currentPeriod.end - currentPeriod.start
      currentPeriod = nextPeriod
    }
  }

  if (currentPeriod) {
    totalOverlapMs += currentPeriod.end - currentPeriod.start
  }

  return calculateDurationBetweenDates(new Date(0), new Date(totalOverlapMs))
}
