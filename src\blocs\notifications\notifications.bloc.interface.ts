import { countries, IAppointmentsCampaign } from "@umahealth/entities"
import { CorporatesNotificationsType } from "./utils/corporateNotifications/corporateNotificationsMap"

export type NotificationType = "sms" | "whatsapp" | "email"

export interface IEventData {
  date: string,
  template: keyof IAppointmentsCampaign,
  type: NotificationType,
  params?: string[],
}

export interface IGenerateNotificationParams {
  attentionTime: string,
  country: countries,
  corporate: CorporatesNotificationsType,
  dateEqualAssignation: string,
  dateFourHoursBefore: string,
  dateOneDayBefore: string,
  dateOneHourBefore: string,
  type: NotificationType,
  attentionDate?: string,
  dateTenMinutesBefore?: string,
  dynamicLink?: string,
  dynamicLinkQueue?: string,
  homeUrl?: string,
  patientFullname?: string,
  providerFullname?: string,
}

export interface INotificationConfig {
  enabledTypes: NotificationType[],
  generateNotifications: (
    params: IGenerateNotificationParams
  ) => IEventData[],
}

export interface IGenerateNotificationParamsExtended extends Omit<IGenerateNotificationParams, "type" | "corporate"> {
  country: countries,
  corporate: string,
  types: NotificationType[],
}

export interface IModelData {
  doctorName: string,
  date: Date,
  modality: "online" | "presencial"
  motivosDeConsulta: string,
  patientName: string,
  specialty: string,
  cmData?: {
    address?: string,
    state?: string,
    name?: string
  }
}

export interface ISendDoctorAppointmentConfirmation {
  corporate: string,
  country: countries
  email: string,
  fullname: string,
  modelData: IModelData
}