import { IHealthInsurance, IPatient } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { Timestamp } from "@google-cloud/firestore"
import { DependantRepository, HealthInsuranceRepository } from "@umahealth/repositories"
import { InternalServerErrorException, NotFoundException } from "@nestjs/common"


export const DependantSetPrimaryUseCase = async (uid: string, coverageId:string): Promise<IDocumentList<Partial<IHealthInsurance<Timestamp>> | Partial<IPatient<Timestamp>>>[]> => {

  const updatedCoverages: IDocumentList<Partial<IHealthInsurance<Timestamp>> | Partial<IPatient<Timestamp>>>[] = []
  const coverages = await HealthInsuranceRepository.dependantGetAllByUid(uid)
  if(!coverages) throw new InternalServerErrorException(`[ Coverages | dependant | Set primary ] Failed getting coverages - UID ${uid}`)

  const exists = coverages.filter(item => item.id === coverageId)
  if(exists.length === 0) throw new NotFoundException(`[ Coverages | dependant | Set primary ] => Coverage not found - UID ${uid} Coverage ${coverageId}`)

  coverages.map( async (coverage) => {
    // Si la cobertura es la seleccionada como primary, setea true
    if(coverage.id === coverageId){
      coverage["primary"] = true
      const document = await HealthInsuranceRepository.dependantUpdate(uid, coverage.id, coverage)
      updatedCoverages.push(document)
    } else if(coverage?.primary === undefined || coverage?.primary === true){
      // si la cobertura no tiene param primary o si es la primary anterior, setea false.
      // No se le setea false a todo para evitar escrituras en la db innecesarias.
      coverage["primary"] = false
      const document = await HealthInsuranceRepository.dependantUpdate(uid, coverage.id, coverage)
      updatedCoverages.push(document)
    }
  })

  // Temporal hasta que se deje de usar corporate_norm
  const dependant = await DependantRepository.getByUidFromDependant(uid)
  const dependantDocument = await DependantRepository.newPathUpdate(uid, {...dependant, corporate_norm:coverageId})
  updatedCoverages.push(dependantDocument)


  return updatedCoverages

}
