import { NotFoundException } from "@nestjs/common"
import { countries } from "@umahealth/entities"
import { ProviderIncomesRepository } from "@umahealth/repositories"

export async function getActiveIncome (country: countries, specialty: string, type:string) {
  const income = await ProviderIncomesRepository.getActiveIncome(country, specialty, type)
  if (!income) throw new NotFoundException(`[ Incomes | getActiveIncome ] => Could not find active income for type ${type}`)
  return income
}