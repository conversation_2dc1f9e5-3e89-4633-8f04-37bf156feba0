import { Timestamp } from "@google-cloud/firestore"
import { INotification, countries } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { createReminders } from "src/utils/notifications/createBatchReminders"
import { corporateNotifications, CorporatesNotificationsType } from "../utils/corporateNotifications/corporateNotificationsMap"
import { IEventData, IGenerateNotificationParamsExtended, NotificationType } from "../notifications.bloc.interface"
import { Logger } from "@nestjs/common"
import { getNotificationsDates } from "../utils/getNotificationsDates"
import { generateNotificationLinks } from "../utils/generateNotificationLinks"

// reminder types
const types: NotificationType[] = [ "sms", "email", "whatsapp" ]

export const CreateSpecialistAppointmentNotificationUseCase = async (
  assignationId: string,
  corporate: string,
  country: countries,
  dt_assignation: Timestamp,
  patientFullname: string,
  providerFullname: string,
  uid: string
): Promise<IDocumentList<INotification<Timestamp>>> => {
  // generate dates
  const {
    dateEqualAssignation,
    dateFourHoursBefore,
    dateOneDayBefore,
    dateOneHourBefore,
    dateTenMinutesBefore,
    attentionDate,
    attentionTime
  } = getNotificationsDates(dt_assignation)

  // link generation
  const {
    homeUrl,
    dynamicLink,
    dynamicLinkQueue
  } = await generateNotificationLinks(country, assignationId, uid)

  const eventData = generateNotifications({
    country,
    corporate: corporate as CorporatesNotificationsType,
    types,
    dateEqualAssignation,
    dateOneDayBefore,
    dateFourHoursBefore,
    dateOneHourBefore,
    attentionTime,
    attentionDate,
    dateTenMinutesBefore,
    patientFullname,
    providerFullname,
    dynamicLink,
    dynamicLinkQueue,
    homeUrl,
  })

  return createReminders(assignationId, "appointments", country, true, eventData, uid, uid)
}

/** Main function to generate notifications for any corporate */
function generateNotifications({
  corporate,
  types,
  ...notificationParams
}: IGenerateNotificationParamsExtended): IEventData[] {
  if (corporate === "IOMA-APP") {
    corporate = "IOMA"
  }
  let corporateConfig = corporateNotifications[ corporate as CorporatesNotificationsType ]

  if (!corporateConfig) {
    Logger.log(`[ ${CreateSpecialistAppointmentNotificationUseCase.name} ] => Corporate "${corporate}" not configured for notifications, using default UMA config`)
    corporateConfig = corporateNotifications.UMA
  }

  const eventData: IEventData[] = []

  // Filter notification types based on what's enabled for this corporate
  const enabledTypes = types.filter(type =>
    corporateConfig.enabledTypes.includes(type)
  )
  const disabledTypes = types.filter(type => !enabledTypes.includes(type))
  if (disabledTypes.length > 0) {
    Logger.warn(`Notification types disabled for ${corporate}: ${disabledTypes.join(", ")}`)
  }

  // Generate notifications for each type
  enabledTypes.forEach((type) => {
    Logger.log(`[ ${CreateSpecialistAppointmentNotificationUseCase.name} | ${generateNotifications.name} ] => Generating ${type} notification for ${notificationParams.patientFullname} with corporate ${corporate}`)
    const notifications = corporateConfig.generateNotifications({ ...notificationParams, type, corporate: corporate as CorporatesNotificationsType })

    eventData.push(...notifications)
  })

  return eventData
}
