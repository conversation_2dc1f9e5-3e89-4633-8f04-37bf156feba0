import { Injectable } from "@nestjs/common"
import { getById, getInvitationsByCorporateId, getInvitationsByDates, getInvitationsByDni, updateInvitation } from "./useCases"
import { IUpdateInvitationDto } from "src/doctor-app/invitations/invitations.entities"

@Injectable()
export class InvitationBloc {

  async getById(id: string){
    return await getById(id)
  }

  async updateInvitation(id: string, body: IUpdateInvitationDto){
    return await updateInvitation(id, body)
  }

  async getInvitationsByDates(corporates: string[], dateFrom: Date, dateTo: Date){
    return await getInvitationsByDates(corporates, dateFrom, dateTo)
  }

  async getInvitationsByCorporateId(corporate: string, dateFrom: Date, dateTo: Date) {
    return await getInvitationsByCorporateId(corporate, dateFrom, dateTo)
  }

  async getInvitationsByDni(corporates: string[], dni:string){
    return await getInvitationsByDni(corporates, dni)
  }

}
