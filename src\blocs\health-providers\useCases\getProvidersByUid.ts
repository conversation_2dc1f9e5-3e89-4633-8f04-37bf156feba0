import { Timestamp } from "@google-cloud/firestore"
import { IProvider } from "@umahealth/entities"
import { ProviderRepository } from "@umahealth/repositories"
import { IProviderWithMatricula } from "../entities/IProviderWithMatricula"
import { getCurrentLicense } from "src/blocs/coverage/utils/functions"

export async function getProvidersByUid(uid: string): Promise<{ data: IProviderWithMatricula<Timestamp>[] }> {
  const providers = await ProviderRepository.getAllProviders() as IProvider<Timestamp>[]
  const filteredProviders = providers.filter(
    (provider): provider is IProvider<Timestamp> =>
      typeof provider === "object" &&
      provider !== null &&
      typeof provider.uid === "string" &&
      provider.uid.toLocaleLowerCase().includes(uid.toLocaleLowerCase())
  )
  const data: IProviderWithMatricula<Timestamp>[] = []
  for (const provider of filteredProviders) {
    const licenses = await ProviderRepository.getLicenses(provider.uid)
    const currentLicense = getCurrentLicense(licenses)
    if (currentLicense) {
      data.push({
        ...provider,
        matricula: currentLicense.number.toString()
      })
    }
  }
  return {
    data
  }
}
