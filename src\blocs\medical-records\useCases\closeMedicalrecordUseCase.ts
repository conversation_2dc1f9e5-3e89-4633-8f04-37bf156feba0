import { NotFoundException } from "@nestjs/common"
import { IMedicalRecord, countries, finalDestinations, appointmentServices, orderOrPrescription, action, IOnSiteAppointment } from "@umahealth/entities"
import { IDocumentList } from "@umahealth/firestore"
import { AppointmentRepository, MedicalRecordRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"


export interface IMedicalRecordOnsite extends IMedicalRecord<Timestamp> {
  cancelMotive?: string,
  state?: string,
  adminActions?: action[]
}

export async function closeMedicalRecordOnsiteAppointmentUseCase(
  assignationId: string,
  country: countries,
  dt_cierre: Date,
  final_destination: finalDestinations,
  service: appointmentServices,
  uid: string,
  cancelMotive: string,
  adminActions: action
): Promise<IDocumentList<Partial<IMedicalRecordOnsite>>> {

  const appointmentDocument = await AppointmentRepository.getByAssignationId<IOnSiteAppointment>(service, country, assignationId)
  const medicalRecordDocument: IMedicalRecordOnsite = await MedicalRecordRepository.getByAssignationId(uid, assignationId)

  medicalRecordDocument.mr = {
    destino_final: final_destination,
    diagnostico: "",
    epicrisis: "",
    motivos_de_consulta: appointmentDocument.appointment_data.motivos_de_consulta || "",
    observaciones: "",
    reposo:  "",
    specialist_referral: "",
    tratamiento: "",
    dt_cierre: dt_cierre.toString() || "",
    alertas: "",
    dt: "",
    ordenes: new Array<orderOrPrescription>,
    receta: [""],
    receta_ref: ""
  }
  medicalRecordDocument.timestamps.dt_close = Timestamp.fromDate(dt_cierre)
  medicalRecordDocument.cancelMotive = cancelMotive
  medicalRecordDocument.state = "CANCEL"
  medicalRecordDocument.adminActions = appointmentDocument.adminActions ? [...appointmentDocument.adminActions, adminActions] : [adminActions]

  const mrToUpdate = await MedicalRecordRepository.update(uid, assignationId, medicalRecordDocument)
  if (!mrToUpdate || typeof (mrToUpdate) == "boolean") {
    throw new NotFoundException(`Error closing medical record ${assignationId} from user ${uid}`)
  }
  return mrToUpdate
}
