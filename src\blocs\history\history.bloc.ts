import { Timestamp } from "@google-cloud/firestore"
import { Injectable } from "@nestjs/common"
import { createPatientHistory } from "./useCases/createForPatient"
import { createPatientHistoryInProvider } from "./useCases/createForProvider"
import { updateHistoryInPatient } from "./useCases/updateForPatient"
import { updateHistoryInProvider } from "./useCases/updateForProvider"

@Injectable()
export class HistoryBloc {
  async createInPatientCollection(providerUid: string, cuit: string, providerFullname: string, providerEspecialidad: string, patientUid: string, patientDni: string, text: string, dt_create: Timestamp, historyId: string) {
    return await createPatientHistory(providerUid, cuit, providerFullname, providerEspecialidad, patientUid, patientDni, text, dt_create, historyId)
  }

  async createInProviderCollection(providerUid: string, cuit: string, providerFullname: string, providerEspecialidad: string, patientUid: string, patientDni: string, text: string, dt_create: Timestamp, historyId: string) {
    return await createPatientHistoryInProvider(providerUid, cuit, providerFullname, providerEspecialidad, patientUid, patientDni, text, dt_create, historyId)
  }

  async updateInPatientCollection(text: string, patientUid: string, historyId: string, dt_updated: Timestamp) {
    return await updateHistoryInPatient(text, patientUid, historyId, dt_updated)
  }

  async updateInProviderCollection(text: string, providerUid: string, patientUid: string, historyId: string, dt_updated: Timestamp) {
    return await updateHistoryInProvider(text, providerUid, patientUid, historyId, dt_updated)
  }
}
