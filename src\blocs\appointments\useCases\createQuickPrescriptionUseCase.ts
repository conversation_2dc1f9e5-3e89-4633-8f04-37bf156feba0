import { IDocumentList, AppointmentRepository, DependantRepository, PatientRepository } from "@umahealth/repositories"
import { dependantUid, IPatient, IChatAttAppointment, ChatAttAppointment, countries, paymentData } from "@umahealth/entities"
import { convertDateToTimezoneString } from "@umahealth/time"
import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"

/** Cuando el paciente pide una consulta de chat, se genera la consulta y queda en un pool de donde los medicos van atendiendo */
/**
 * This method is called on the first workflow to take a chat appointment, and it creates an assignation document that will be updated later.
 * @param assignationId - will be the ID of this document
 * @param uid - the id of the patient
 * @param dependantUid - the id of the dependant this appointment is for, or false if no dependant
 * @param motivosDeConsulta - the symptoms the patient logged
 * @param creationTime - when the workflow was called
 * @returns a DocumentList<IAppointment> to be saved in batch
 */
export const CreateQuickPrescriptionUseCase = async (
  assignationId: string,
  country: countries,
  uid: string,
  dependantUid: dependantUid,
  motivosDeConsulta: string,
  creationTime: Date,
  paymentData: paymentData,
  pediatric: boolean,
  specialty: string,
): Promise<IDocumentList<IChatAttAppointment<Timestamp>>> => {
  let patientDocument: IPatient<Timestamp>
  if (dependantUid && typeof (dependantUid) !== "boolean") {
    patientDocument = await DependantRepository.getByUid(uid, dependantUid)
  } else {
    patientDocument = await PatientRepository.getByUid(uid)
  }
  if (!patientDocument) {
    throw new NotFoundException(`Could not find patient ${uid}`)
  }

  const appointmentDocument = new ChatAttAppointment<Timestamp>()

  appointmentDocument.appointment_data = {
    motivos_de_consulta: motivosDeConsulta,
    alertas: ""
  }
  appointmentDocument.assignation_id = assignationId
  appointmentDocument.cm = patientDocument.corporate_norm || "SIN OBRA SOCIAL(UMA)"
  appointmentDocument.country = patientDocument.country || country
  appointmentDocument.cuil = "prescription"
  appointmentDocument.cuit = "prescription"
  appointmentDocument.especialidad = specialty
  appointmentDocument.fullname = ""
  appointmentDocument.max_appointments = 1
  appointmentDocument.patient = {
    corporate: patientDocument.corporate_norm || "SIN OBRA SOCIAL(UMA)",
    country: patientDocument.country || country,
    dni: patientDocument.dni || "",
    dob: patientDocument.dob || "",
    fullname: patientDocument.fullname || "",
    sex: patientDocument.sex || "",
    uid: uid,
    uid_dependant: dependantUid,
    ws: patientDocument.ws || "",
  }
  appointmentDocument.social_work = ""
  appointmentDocument.state = "ASSIGN"
  appointmentDocument.timestamps = {
    dt_create: Timestamp.fromDate(creationTime),
    dt_updated: Timestamp.fromDate(creationTime)
  }

  appointmentDocument.payment_data = paymentData || null
  appointmentDocument.unreadMessagesDoctor = 0
  appointmentDocument.dtLastMessageDoctor = null
  appointmentDocument.unreadMessagesPatient = 0
  appointmentDocument.dtLastMessagePatient = null
  appointmentDocument.pediatric = pediatric || false

  /* This will be soon deprecated */
  appointmentDocument.date = convertDateToTimezoneString(creationTime, "YYYY-MM-DD")
  appointmentDocument.datetime = convertDateToTimezoneString(creationTime, "YYYYMMDDHHmm")
  appointmentDocument.time = convertDateToTimezoneString(creationTime, "HH:mm")

  return await AppointmentRepository.createQuickPrescription(assignationId, appointmentDocument, country)
}
