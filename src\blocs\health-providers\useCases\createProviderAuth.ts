import { ConflictException, Logger } from "@nestjs/common"
import { authClient } from "@umahealth/auth"
import { ICreateProviderAuth } from "src/portal-app/healthProvider/healthProvider.entities"

export const createProviderAuth = async (data: ICreateProviderAuth) => {
  try {
    Logger.log(`[ createProviderAuth ] => Creating provider auth for email: ${data.email}`)
    // Check if user already exists
    try {
      const existingUser = await authClient.getUserByEmail(data.email)
      if (existingUser) {
        throw new ConflictException(`Provider already exists with email: ${data.email}`)
      }
    } catch (error) {
      // If error is user not found, continue with creation
      if (error.code !== "auth/user-not-found") {
        throw error
      }
    }

    // Create new user in Firebase Auth
    const userAuth = await authClient.createUser({
      email: data.email,
      password: data.password,
      disabled: true
    })

    Logger.log(`[ createProviderAuth ] => Provider auth created for email: ${data.email} with uid: ${userAuth.uid}`)

    return userAuth
  } catch (error) {
    if (error instanceof ConflictException) {
      throw error
    }
    throw new Error(`Error creating provider auth: ${error}`)
  }
}
