import { NotFoundException } from "@nestjs/common"
import * as moment from "moment"
import { INom035UserWithFormStatus } from "../interfaces"
import nom035DTO from "../models/nom035_userPositionDTO"


export const getUserInformation = async (uid: string) => {
  const user = await nom035DTO.getInformUserByUid(uid) as unknown as INom035UserWithFormStatus
  if(!user){
    throw new NotFoundException(`[Nom035 | Informs User Information ] User with uid ${uid} not found`)
  }

  const month_diff = Date.now() - new Date(user.birthDate).getTime()
  const year = new Date(month_diff).getUTCFullYear()
  user.dataValues.age = Math.abs(year - 1970)

  const positions = user.dataValues.positions[0]
  user.dataValues = {
    ...user.dataValues,
    ...positions.dataValues
  }
  delete user.dataValues.positions
  return user

}



