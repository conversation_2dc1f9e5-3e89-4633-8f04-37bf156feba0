import { IDocumentList, PatientRepository } from "@umahealth/repositories"
import { IPatient } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { InternalServerErrorException } from "@nestjs/common"

export const SetInactiveAppointmentForPatientUseCase = async (uid: string): Promise<IDocumentList<Partial<IPatient<Timestamp>>>> => {
  try {
    const updatedPatient: Partial<IPatient<Timestamp>> = {
      _start_date: "",
      active_appointment: false
    }
    const newPatient = await PatientRepository.update(uid, updatedPatient)
    return newPatient

  } catch (err) {
    throw new InternalServerErrorException(`[ Patient | setInactiveAppointment ] => ${err.message}`)
  }
}
