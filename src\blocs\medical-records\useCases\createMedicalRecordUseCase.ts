import {
  DependantRepository,
  HealthInsuranceRepository,
  IDocumentList,
  MedicalRecordRepository,
  PatientRepository,
  ProviderRepository,
} from "@umahealth/repositories"
import {
  attCategories,
  dependantUid,
  IMedicalRecord,
  IPatient,
  IProvider,
  MedicalRecord,
  medicalRecordPatient,
  paymentData,
  providerSpecialties
} from "@umahealth/entities"
import { InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common"
import { convertDateToTimezoneString } from "@umahealth/time"
import { Timestamp } from "@google-cloud/firestore"
import AiModelsBloc from "../../ai-models/ai-models.bloc"

export const CreateMedicalRecordUseCase = async (
  assignationId: string,
  incidente_id: string,
  attCategory: attCategories,
  attentionDate: string,
  attentionTime: string,
  dependantUid: dependantUid,
  dt_create: Date,
  motivosDeConsulta: string,
  uid: string,
  especialidad?: providerSpecialties,
  payment_data?: paymentData,
  providerUid?: string
): Promise<IDocumentList<IMedicalRecord<Timestamp>>> => {
  let patientDocument: IPatient<Timestamp>
  let coverage

  if (dependantUid && typeof (dependantUid) !== "boolean") {
    patientDocument = await DependantRepository.getByUid(uid, dependantUid)
    coverage = await HealthInsuranceRepository.getDepentantPrimary(dependantUid)
  } else {
    patientDocument = await PatientRepository.getByUid(uid)
    coverage = await HealthInsuranceRepository.getPrimary(uid)
  }
  if (!patientDocument) throw new NotFoundException(`[ MedicalRecords | create ] => could not find patient with uid ${uid} dependant ${dependantUid}`)
  if (!coverage) throw new NotFoundException(`[ MedicalRecords | create ] => could not get primary coverage for ${uid} dependant ${dependantUid}`)

  const patient: medicalRecordPatient = {
    address: patientDocument.address || "",
    antecedentes: "",
    corporate_norm: coverage.id,
    country: patientDocument.country || "AR",
    dependant_uid: dependantUid,
    dni: patientDocument.dni,
    dob: patientDocument.dob,
    fullname: patientDocument.fullname,
    n_afiliado: coverage.affiliate_id || "",
    obra_social: coverage.id,
    sex: patientDocument.sex,
    uid: uid,
    ws: patientDocument.ws
  }
  let provider = {
    cuit: "bag",
    especialidad: "online_clinica_medica",
    fullname: "",
    uid: "",
    ws: ""
  }
  if (providerUid) {
    const providerDocument: IProvider<Timestamp> = await ProviderRepository.getByProviderUid(providerUid)
    provider = {
      cuit: providerDocument.cuit,
      ws: providerDocument.ws,
      fullname: providerDocument.fullname,
      uid: providerUid,
      especialidad: especialidad || "online_clinica_medica"
    }
  }
  /* Get diagnostic prediction if there are motivos de consulta */
  let diagnostico = ""
  if (motivosDeConsulta !== "") {
    try {
      const diag = await AiModelsBloc.diagnosticPredict(motivosDeConsulta)
      if (typeof diag !== "boolean") {
        diagnostico = diag
      }
    } catch (err) {
      if (err.isAxiosError) {
        Logger.warn("Falló diagnostic_predict", "[ MedicalRecords | guardia | create ]")
        // Logger.warn(`${err.response.status}: ${err.response.config.url} ${err.response.statusText}`)
      } else {
        throw new InternalServerErrorException(err)
      }
    }
  }

  const medicalRecord = new MedicalRecord<Timestamp>()
  medicalRecord.assignation_id = assignationId
  medicalRecord.attentionDate = attentionDate
  medicalRecord.attentionTime = attentionTime
  medicalRecord.att_category = attCategory
  medicalRecord.dt_cierre = null
  medicalRecord.especialidad = especialidad || "online_clinica_medica"
  medicalRecord.event_type = attCategory !== "CONSULTORIO" ? "online" : "onsite"
  medicalRecord.incidente_id = assignationId
  medicalRecord.mr_preds.diagnostico = diagnostico || ""
  medicalRecord.mr_preds.motivos_de_consulta = motivosDeConsulta
  medicalRecord.mr.motivos_de_consulta = motivosDeConsulta
  medicalRecord.patient = patient
  medicalRecord.payment_data = payment_data
  medicalRecord.provider = provider
  medicalRecord.timestamps = {
    dt_create: Timestamp.fromDate(dt_create)
  }

  /* This will be soon deprecated */
  medicalRecord.created_dt = convertDateToTimezoneString(dt_create, "YYYY-MM-DD HH:mm:ss")

  return await MedicalRecordRepository.create(uid, assignationId, medicalRecord)
}

