import { IRequest } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"

export interface IUniqueUsers {
  corporate: string,
  uid: string,
  ws: string,
}

/** Obtiene, de todas las requests, los usuarios únicos implicados en las mismas */
export const getUniqueUsers = (requests: IRequest<Timestamp>[]): IUniqueUsers[] => {
  const users: IUniqueUsers[] = []

  requests.forEach((request) => {
    const { uid, corporate, ws } = request

    const userExists = users.findIndex((user) => {
      return user.uid === uid
    })

    if (userExists === -1) {
      users.push({
        corporate,
        uid,
        ws,
      })
    }
  })

  return users
}