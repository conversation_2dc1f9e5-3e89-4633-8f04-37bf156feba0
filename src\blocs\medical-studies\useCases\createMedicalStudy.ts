import { Timestamp } from "@google-cloud/firestore"
import { MedicalStudyRepository, saveBatch } from "@umahealth/repositories"
import { MedicalStudyData, MedicalStudyType } from "src/patient-app/medicalStudy/medicalStudy.interface"

export const createMedicalStudyUseCase = async (uid: string, newData: MedicalStudyData, path: string, type: MedicalStudyType) => {
  const medicalStudyData = {
    uid: uid,
    type: type,
    path: path,
    data: newData,
    active: true,
    timestamps: {
      dt_create: Timestamp.now()
    },
  }
  const newMedicalStudy = await MedicalStudyRepository.create(uid, medicalStudyData)
  await saveBatch([newMedicalStudy], true)
  return true
}
