import { Timestamp } from "@google-cloud/firestore"
import { Injectable, InternalServerErrorException, NotFoundException } from "@nestjs/common"
import { IAutonomous } from "@umahealth/entities"
import { AutonomousRepository, IDocumentList } from "@umahealth/repositories"


@Injectable()
export class AutonomousBloc {

  async saveFeedback(uid: string, dt: Date, notes: string, umaEval: number): Promise<IDocumentList<IAutonomous<Timestamp>>> {
    const doc = await AutonomousRepository.getById(uid)

    if (!doc) throw new NotFoundException("[ Autonomous | saveFeedback ] => Lab document not found")

    doc.feedback = {
      dt: Timestamp.fromDate(dt),
      notes: notes,
      umaEval: umaEval
    }
    const updateDoc = await AutonomousRepository.update(uid, doc)
    if(!updateDoc) throw new InternalServerErrorException(`[ Autonomous | saveFeedback ] => Error updating lab with uid: ${uid}`)

    return updateDoc
  }
}
