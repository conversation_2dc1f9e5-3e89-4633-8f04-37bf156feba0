import { IHealthInsurance } from "@umahealth/entities"
import { InternalServerErrorException } from "@nestjs/common"
import { Timestamp } from "@google-cloud/firestore"
import { Externals } from "@umahealth/sql-models"


export const ValidateCoverageByExternalPgUseCase =  async (healthInsurance: IHealthInsurance<Timestamp>): Promise<boolean> => {
  try{

    const data = await Externals.findOne({ where: { affiliateNumber: healthInsurance.affiliate_id, active: true}, raw: true})
    if(data){
      return true
    }else{
      return false
    }
  }catch(err){
    throw new InternalServerErrorException(`[ Coverages | patient | validateByExternalPG ] Error validating patient with healthInsurance data: ${JSON.stringify(healthInsurance)} and error: ${err} `)
  }

}
