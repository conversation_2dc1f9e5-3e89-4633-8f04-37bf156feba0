import { InboxRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"

export async function toggleInboxRead(providerUid: string, inboxId: string) {
  const inbox = await InboxRepository.get(providerUid, inboxId)
  if (!inbox) {
    throw new NotFoundException(`[ Inbox | toggle ] => Inbox ${inboxId} from provider ${providerUid} not found`)
  }

  inbox.read = !inbox.read
  inbox.timestamps = {
    ...inbox.timestamps,
    dt_updated: Timestamp.now()
  }

  return await InboxRepository.update(providerUid, inboxId, inbox)
}
