import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { appointmentServices, countries, dependantUid, finalDestinations, IAppointment, IMedicalRecord } from "@umahealth/entities"
import { AppointmentRepository, DependantRepository, IDocumentList, MedicalRecordRepository, PatientRepository, ProviderRepository } from "@umahealth/repositories"

export async function updateMedicalRecord(
  assignationId: string,
  country: countries,
  dependant_uid: dependantUid,
  dt_updated: Date,
  providerUid: string,
  service: appointmentServices,
  uid: string,
  diagnostic?: string,
  epicrisis?: string,
  final_destination?: finalDestinations,
  motivosDeConsulta?: string,
  notes?: string,
  rest?: string,
  treatment?: string
): Promise<IDocumentList<Partial<IMedicalRecord<Timestamp>>>> {

  const appointmentDocument = await AppointmentRepository.getByAssignationId<IAppointment>(service, country, assignationId)
  const medicalRecordDocument = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  let providerDocument
  let patientDocument
  if(!appointmentDocument || !medicalRecordDocument) throw new NotFoundException(`[ MedicalRecords | update ] => Appointment or medical record not found. Service: ${service} Assignation Id: ${assignationId} Uid: ${uid}`)

  if (diagnostic) {
    if (medicalRecordDocument.mr.diagnostico === null || medicalRecordDocument.mr.diagnostico.trim() !== diagnostic.trim()) {
      medicalRecordDocument.mr.diagnostico = diagnostic
    }
  }
  if (epicrisis) {
    if (medicalRecordDocument.mr.epicrisis === null || medicalRecordDocument.mr.epicrisis.trim() !== epicrisis.trim()) {
      medicalRecordDocument.mr.epicrisis = epicrisis
    }
  }
  if (final_destination) {
    if (medicalRecordDocument.mr.destino_final === null || medicalRecordDocument.mr.destino_final.trim() !== final_destination.trim()) {
      medicalRecordDocument.mr.destino_final = final_destination
    }
  }
  if (motivosDeConsulta) {
    if (medicalRecordDocument.mr.motivos_de_consulta === null || medicalRecordDocument.mr.motivos_de_consulta.trim() !== motivosDeConsulta.trim()) {
      medicalRecordDocument.mr.motivos_de_consulta = motivosDeConsulta
    }
  }
  if (notes) {
    if (medicalRecordDocument.mr.observaciones === null || medicalRecordDocument.mr.observaciones.trim() !== notes.trim()) {
      medicalRecordDocument.mr.observaciones = notes
    }
  }
  if (rest) {
    if (medicalRecordDocument.mr.reposo === null || medicalRecordDocument.mr.reposo.trim() !== rest.trim()) {
      medicalRecordDocument.mr.reposo = rest
    }
  }
  if (treatment) {
    if (medicalRecordDocument.mr.tratamiento === null || medicalRecordDocument.mr.tratamiento.trim() !== treatment.trim()) {
      medicalRecordDocument.mr.tratamiento = treatment
    }
  }

  medicalRecordDocument.timestamps.dt_updated = Timestamp.fromDate(dt_updated)

  if (service === "bag" || service === "chatAtt") {
    providerDocument = await ProviderRepository.getByProviderUid(providerUid)
    medicalRecordDocument.provider.cuit = providerDocument.cuit || ""
    medicalRecordDocument.provider.especialidad = providerDocument.matricula_especialidad || ""
    medicalRecordDocument.provider.fullname = providerDocument.fullname
    medicalRecordDocument.provider.uid = providerUid
    medicalRecordDocument.provider.ws = providerDocument.ws || ""
  }
  if (service === "online") {
    if (dependant_uid && dependant_uid !== "") {
      patientDocument = await DependantRepository.getByUidFromDependant(dependant_uid)
      if (!patientDocument) {
        patientDocument = await DependantRepository.getByUid(uid, dependant_uid)
      }
    } else {
      patientDocument = await PatientRepository.getByUid(uid)
    }
    medicalRecordDocument.patient.address = patientDocument.address || ""
    medicalRecordDocument.patient.country = patientDocument.country
    medicalRecordDocument.patient.dependant_uid = dependant_uid
    medicalRecordDocument.patient.dni = patientDocument.dni
    medicalRecordDocument.patient.dob = patientDocument.dob
    medicalRecordDocument.patient.fullname = patientDocument.fullname
    medicalRecordDocument.patient.sex = patientDocument.sex
    medicalRecordDocument.patient.uid = dependant_uid || uid
    medicalRecordDocument.patient.ws = patientDocument.ws
  }

  const mrToUpdate = await MedicalRecordRepository.update(uid, assignationId, medicalRecordDocument)
  if (!mrToUpdate || typeof (mrToUpdate) == "boolean") {
    throw new NotFoundException(`[ MedicalRecords | update ] => Error updating medical record ${assignationId} from user ${uid}`)
  }
  return mrToUpdate
}

