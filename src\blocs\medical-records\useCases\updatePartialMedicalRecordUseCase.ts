import { NotFoundException } from "@nestjs/common"
import { IMedicalRecord } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { MedicalRecordRepository, IDocumentList } from "@umahealth/repositories"

/**
 * Actualiza parcialmente un registro médico en la base de datos.
 *
 * @param {string} assignationId - El identificador de la consulta.
 * @param {Partial<IMedicalRecord<Timestamp>>} data - Los datos parciales del registro médico que se desean actualizar.
 * @param {string} uid - El identificador único del usuario (paciente).
 * @returns {Promise<IDocumentList<Partial<IMedicalRecord<Timestamp>>>>} - Los datos del registro médico actualizado.
 * @throws {NotFoundException} - Si no se encuentra el registro médico con el `assignationId` proporcionado.
 *
 * @description
 * Esta función busca un registro médico por el `assignationId` y actualiza los campos proporcionados.
 * Maneja específicamente el campo `mr_auditoria` para añadir nuevos elementos sin reemplazar los existentes.
 * Actualiza las marcas de tiempo (`timestamps`) con la nueva información proporcionada.
 */

export const updatePartialMedicalRecordUseCase = async (
  assignationId: string,
  data: Partial<IMedicalRecord<Timestamp>>,
  uid: string
): Promise<IDocumentList<Partial<IMedicalRecord<Timestamp>>>> => {
  const patientMedicalRecord = await MedicalRecordRepository.getByAssignationId(uid, assignationId)
  if (!patientMedicalRecord) {
    throw new NotFoundException(
      `[ MedicalRecords | Update ] => Could not find medical record ${assignationId}`
    )
  }

  // Preparar los datos para la actualización
  const updateData: Partial<IMedicalRecord<Timestamp>> = { ...data }

  if (data.fitnessCertificateDocuments) {
    updateData.fitnessCertificateDocuments = {
      ...patientMedicalRecord.fitnessCertificateDocuments,
      ...data.fitnessCertificateDocuments
    }
  }

  // Manejar mr_auditoria
  if (data.mr_auditoria && data.mr_auditoria.length > 0) {
    updateData.mr_auditoria = [
      ...(patientMedicalRecord.mr_auditoria || []),
      ...data.mr_auditoria
    ]
  }

  // Manejar la actualización de reposo
  if (data.mr && data.mr.reposo) {
    updateData.mr = {
      ...patientMedicalRecord.mr,
      reposo: data.mr.reposo
    }
  }

  // Manejar la actualización de timestamps
  updateData.timestamps = {
    ...patientMedicalRecord.timestamps,
    ...data.timestamps,
    dt_updated: Timestamp.now()
  }

  // Realizar la actualización
  return MedicalRecordRepository.update(uid, assignationId, updateData)
}
