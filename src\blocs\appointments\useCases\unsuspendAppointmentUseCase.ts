import { IAppointment, appointmentStates, countries } from "@umahealth/entities"
import { AppointmentRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"

export const unsuspendAppointmentUseCase = async (assignationId: string, country: countries, appointment: IAppointment) => {
  const appointmentData: Partial<IAppointment<Timestamp>> = {
    state: "ASSIGN" as appointmentStates,
    confirmed: true,
    timestamps: {
      ...appointment.timestamps,
      dt_confirmed: Timestamp.now(),
      dt_unsuspend: Timestamp.now()
    }
  }
  return AppointmentRepository.update("bag", country, assignationId, appointmentData)
}
