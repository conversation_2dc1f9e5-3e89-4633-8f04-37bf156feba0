import { NotFoundException } from "@nestjs/common"
import { getUserByUid } from "@umahealth/auth"
import { getCoachC3Responses } from "src/utils/airtable"


export const getC3ResponsesToCoach = async (uid: string) => {
  const user = await getUserByUid(uid)
  if(!user){
    throw new NotFoundException(`[ Nom035 | Responses To Coach ] User with uid: ${uid} not found`)
  }
  return await getCoachC3Responses(user.email)
}
