import { NotFoundException } from "@nestjs/common"
import { getUserByUid } from "@umahealth/auth"
import { FieldSet } from "airtable"
import { QuestionItem } from "src/utils/airtable/coach"
import { c3CategoriesRecommendations } from "src/utils/airtable/statistics"
import { organizeC3RiskyFactorsData } from "src/utils/airtable/utils/functions"

export const employeeC3SpeedometerAndRiskyFactors = async (
  users: FieldSet[],
  c3Questions: FieldSet[],
  c3Responses: FieldSet[],
  uid: string,
  email?: string
) => {

  if (!email) {
    const userInDB = await getUserByUid(uid)
    if (!userInDB) {
      throw new NotFoundException(`[ Nom035 | employeeC3SpeedometerAndRiskyFactors ] User with uid: ${uid} not found`)
    }
    email = userInDB.email
  }

  const user = users.find(user => user.correo === email)
  if (!user) {
    throw new NotFoundException(`[ Nom035 | employeeC3SpeedometerAndRiskyFactors ] User with email: ${email} not found in users array`)
  }

  const c3FormCompleted = user["c3"] as string[] || []
  if (c3FormCompleted.length === 0) {
    return { formCompleted: false, riskyFactors: {}, date: "", generalPercentage: 0, categoryResult: "", recommendation: "" }
  }

  const { maxScore, totalScore, lastC3Form } = processC3Forms(c3FormCompleted, c3Responses)
  const riskyFactors = organizeC3RiskyFactorsData(c3Questions as unknown as QuestionItem[], lastC3Form)

  const resultadoFinal = calculateFinalResult(totalScore, maxScore, c3FormCompleted.length)
  const categoryResult = getCategoryResult(resultadoFinal)

  return {
    formCompleted: true,
    riskyFactors,
    date: lastC3Form?.["año mes"] as string || "",
    generalPercentage: +resultadoFinal,
    categoryResult,
    recommendation: c3CategoriesRecommendations[categoryResult as keyof typeof c3CategoriesRecommendations]
  }
}

function processC3Forms(c3FormCompleted: string[], c3Responses: FieldSet[]) {
  let maxScore = 0
  let totalScore = 0
  let lastC3Form = null

  const categoryRegex = /\d{1,2}\/(\d{2,3})/
  const categories = ["cat1 / max", "cat2 / max", "cat3 / max", "cat4 / max", "cat5 / max"]

  c3FormCompleted.forEach(id => {
    const record = c3Responses.find(record => record.id === id)
    if (!record) return

    categories.forEach(cat => {
      const match = record[cat].toString().match(categoryRegex)
      if (match) maxScore += +match[1]
    })

    totalScore += +record["c dim total"]
  })

  lastC3Form = c3Responses.find(record => record.id === c3FormCompleted[c3FormCompleted.length - 1])

  return { maxScore, totalScore, lastC3Form }
}

function calculateFinalResult(totalScore: number, maxScore: number, formCount: number): string {
  return ((totalScore / (maxScore * formCount)) * 100).toFixed(2)
}

function getCategoryResult(resultadoFinal: string): string {
  const categoriesLimits: { [key: number]: string } = {
    17: "Nulo",
    26: "Bajo",
    34: "Medio",
    49: "Alto",
    100: "Muy alto"
  }

  for (const [limit, category] of Object.entries(categoriesLimits)) {
    if (+resultadoFinal <= +limit) {
      return category
    }
  }

  return "Muy alto" // Default case if no category is found
}
