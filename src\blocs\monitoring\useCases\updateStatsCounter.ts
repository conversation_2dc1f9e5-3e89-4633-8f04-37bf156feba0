import { MonitoringRepository } from "@umahealth/repositories"
import { Timestamp } from "@google-cloud/firestore"

export async function updateStatsCounter(uid: string, dt_create: Timestamp) {
  // get current count
  let updatedCount
  const count = await MonitoringRepository.getStatsCounter(uid)

  if (!count || isNaN(count.counter)) {
    updatedCount = await MonitoringRepository.createStatsCounter(uid, dt_create)
  } else {
    updatedCount = await MonitoringRepository.updateStatsCounter(uid, count.counter, dt_create)
  }

  return updatedCount
}
