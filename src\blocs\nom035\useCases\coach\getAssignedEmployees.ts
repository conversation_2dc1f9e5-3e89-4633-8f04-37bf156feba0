import { getCoachAssignedEmployees } from "src/utils/airtable"
import { INom035UsersToCoach } from "../../interfaces"
import nom035DTO from "../../models/nom035_userPositionDTO"


export const getAssignedEmployees = async (email: string, company: string) => {
  const [nominaEmails, nominaInfo] = await getCoachAssignedEmployees(email, company)

  if(!nominaEmails.length){
    return []
  }

  const users = await nom035DTO.getUsersByCoach(company, nominaEmails as string[]) as unknown as INom035UsersToCoach[]
  users.map(((user, index) => {
    const userFromAT = (nominaInfo as  { email: string; last_interaction: string | null; }[]).find((item) => item.email === user.email )

    const user_temp = user
    user_temp.dataValues.lastInteraction = userFromAT?.last_interaction
    user_temp.dataValues.corporateId = user.dataValues.positions[0].corporateId
    delete user_temp.dataValues.positions
    users[index] = user_temp
  }))

  return users
}


