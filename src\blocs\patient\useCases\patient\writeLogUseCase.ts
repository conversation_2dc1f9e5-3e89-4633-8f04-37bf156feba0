import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import { EventTypes, IPatientLog, dependantUid } from "@umahealth/entities"
import { PatientRepository } from "@umahealth/repositories"

export const writeLogUseCase = async (uid: string, event: EventTypes, assignation_id: string, uid_dependant: dependantUid) => {
  const patient = await PatientRepository.getByUid(uid)

  if (!patient) throw new NotFoundException(`[ Patient | writeLogUseCase ] => Patient not found | uid ${uid}`)

  const newLog: IPatientLog<Timestamp> = {
    assignation_id,
    event,
    timestamps: {
      dt_create: Timestamp.now(),
    },
    uid,
    uid_dependant,
  }

  const createdLog = await PatientRepository.createPatientLog(newLog)

  return createdLog
}
