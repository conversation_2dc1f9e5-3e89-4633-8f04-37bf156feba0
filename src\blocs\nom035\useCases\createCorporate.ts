import { Logger } from "@nestjs/common"
import { ICreateNomCorporate } from "@umahealth/entities"
import { CorporateRepository } from "@umahealth/repositories"

export const createCorporate = async (nomCorporateData: ICreateNomCorporate) => {
  Logger.log(`[${createCorporate.name}] -> Creating nom corporate, ${nomCorporateData.corporateName}`)

  await CorporateRepository.setNomCorporate(nomCorporateData)
}
