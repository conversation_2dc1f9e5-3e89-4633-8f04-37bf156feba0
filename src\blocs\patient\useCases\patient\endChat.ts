import { IDocumentList, PatientRepository } from "@umahealth/repositories"
import { chat } from "@umahealth/entities"
import { InternalServerErrorException } from "@nestjs/common"
import { Timestamp } from "@google-cloud/firestore"

export async function endChat(uid: string): Promise<IDocumentList<Partial<chat<Timestamp>>>> {
  try {
    const callObj: chat<Timestamp> = {
      activeUid: "",
      assignation_id: "",
      assignationPath: "",
      chatting: false,
      cuit: "",
      dependant: false,
      requested: false,
      type: "",
      unreadMessagesDoctor: 0,
      dtLastMessageDoctor: null,
      unreadMessagesPatient: 0,
      dtLastMessagePatient: null
    }
    const cleanChat = await PatientRepository.updateChatAtt(uid, callObj)
    return cleanChat
  } catch (err) {
    throw new InternalServerErrorException(`[ Patient | endChat ] => ${err.message}`)
  }
}
