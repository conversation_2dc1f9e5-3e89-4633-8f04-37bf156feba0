import { Injectable } from "@nestjs/common"
import { getAllCorporateNamesUseCase, processLogoImageUseCase } from "./useCase"


@Injectable()
export class CorporateBloc {

  async getAllCorporateNames(): Promise<string[]> {
    return await getAllCorporateNamesUseCase()
  }

  async updateLogoImage(file: Express.Multer.File, corporateName: string) {
    const { tempFilePath, storagePath } = await processLogoImageUseCase(file, corporateName)
    return { tempFilePath, storagePath }
  }

}
