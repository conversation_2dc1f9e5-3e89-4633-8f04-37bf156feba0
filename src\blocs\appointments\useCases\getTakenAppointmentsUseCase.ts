import { InternalServerErrorException, Logger } from "@nestjs/common"
import { IRequest } from "@umahealth/entities"
import { RequestRepository } from "@umahealth/repositories"

/**
 * Elimina duplicados de una lista de citas basándose en `incidente_id`.
 * Registra un error si se encuentran duplicados.
 *
 * @param {IRequest[]} appointments - Lista de citas a procesar.
 * @returns {IRequest[]} - Lista de citas sin duplicados.
 */
const removeDuplicateAppointments = (appointments: IRequest[]): IRequest[] => {
  const uniqueAppointmentsMap = new Map()
  const duplicates: string[] = []

  for (const appointment of appointments) {
    if (appointment.incidente_id) {
      if (uniqueAppointmentsMap.has(appointment.incidente_id)) {
        duplicates.push(appointment.incidente_id)
      } else {
        uniqueAppointmentsMap.set(appointment.incidente_id, appointment)
      }
    }
  }

  if (duplicates.length > 0) {
    Logger.error(`[${removeDuplicateAppointments.name}] - - Se encontraron duplicados en los incidentes: ${duplicates.join(", ")}`)
  }

  return Array.from(uniqueAppointmentsMap.values())
}

/**
 * Retorna el número de citas tomadas en el último mes por un usuario.
 * @param {string} dni - DNI del paciente
 * @param {string} corporate - Obra social primaria del paciente
 * @returns {{total: number, appointments: IRequest[]}}
 */
export const getTakenAppointmentsLastMonthUseCase = async (dni: string, corporate: string) => {
  try {

    const appointments = await RequestRepository.getRequests(
      { corporate, dni, type: "otro" },
      { onlyEffectiveAppointments: true }
    )

    const specialistAppointments = await RequestRepository.getRequests(
      { corporate, dni, type: "especialista" },
      { onlyEffectiveAppointments: true, }
    )

    // Unir listas y eliminar duplicados
    const allAppointments = removeDuplicateAppointments([...appointments, ...specialistAppointments])

    Logger.log(`[getTakenAppointmentsLastMonthUseCase] - dni: ${dni} - corporate: ${corporate} has ${allAppointments.length} registered this month`)

    return { total: allAppointments.length, appointments: allAppointments }
  } catch (err) {
    Logger.error(`[${getTakenAppointmentsLastMonthUseCase.name}] - Something went wrong. Error: ${JSON.stringify(err)}`)
    throw new InternalServerErrorException(`[${getTakenAppointmentsLastMonthUseCase.name}] - Something went wrong. Error: ${JSON.stringify(err)}`)
  }
}
