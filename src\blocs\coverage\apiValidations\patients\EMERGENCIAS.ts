import axios from "axios"
import * as moment from "moment"
import { BadRequestException, InternalServerErrorException, Logger } from "@nestjs/common"
import { IEmergenciasSalesforceCredentials } from "../../coverage.bloc.interfaces"
import { IEmerResponse, IBeneficiaries } from "../interfaces"
import { IHealthInsurance } from "@umahealth/entities"
import { Timestamp } from "@google-cloud/firestore"
import { selectCoveragePlan } from "../utils/selectCoveragePlan"


const getToken = async (emerCredentials: IEmergenciasSalesforceCredentials) => {
  const result = await axios.post(`${emerCredentials.tokenUrl}?username=${emerCredentials.username}&password=${emerCredentials.password}&grant_type=password&client_id=${emerCredentials.client_id}&client_secret=${emerCredentials.client_secret}`)
  return `Bearer ${result["data"]["access_token"]}`
}

export const GetBeneficiaries = async (dni: string) => {
  const emerCredentials: IEmergenciasSalesforceCredentials = JSON.parse(process.env.EMERGENCIAS_SALESFORCE_API_CREDENTIALS)
  Logger.log(`[GetBeneficiaries] Obteniendo beneficiarios para DNI: ${dni}`)
  try {
    if(!dni || dni === "") {
      Logger.error("[GetBeneficiaries] DNI faltante o vacío")
      throw new BadRequestException("Missing field: dni on Emergencias API")
    }
    const token = await getToken(emerCredentials)
    const url = `${emerCredentials.coverageUrl}?documentNumber=${dni}&documentType=96`
    const result = await axios.get(url, { headers: { "Authorization": token }, timeout: 5000 }).catch(error => Logger.error(`[ Coverages | patient | EmergenciasAPI | GetBeneficiaries ] => ${error}`))
    const resp: IBeneficiaries[] = []

    if(!result) {
      Logger.log(`[GetBeneficiaries] No se obtuvo resultado para DNI: ${dni}`)
      return resp
    }
    if(result?.data?.length) {
      Logger.log(`[GetBeneficiaries] Procesando ${result.data.length} beneficiarios para DNI: ${dni}`)

      for (const element of result.data) {
        Logger.log(`[GetBeneficiaries] Validando element para DNI ${dni}: ${JSON.stringify(element)}`)
        // Algunos afiliado tienen el campo `status` y otros `coverageStatus`
        const status = element["status"] || element["coverageStatus"]
        if(status !== "Activo") {
          Logger.log(`[GetBeneficiaries] Beneficiario no activo para DNI ${dni}: ${element["beneficiaryNumber"]}`)
          continue
        }
        resp.push({
          affiliateNumber: element?.beneficiaryNumber,
          corporate: "EMERGENCIAS",
          type: "padron",
          taxTreatment: "",
          plan: element["relatedProduct"] || ""
        })
      }
    }
    Logger.log(`[GetBeneficiaries] Se encontraron ${resp.length} beneficiarios activos para DNI: ${dni}`)
    return resp
  } catch(err) {
    throw new InternalServerErrorException(`[ Coverages | patient | EmergenciasAPI ] => DNI: ${dni} Error: ${err.message}`)
  }
}

export const emergenciasSalesforceApi = async (dni: string, patientCoverage?: IHealthInsurance<Timestamp>) => {
  try {
    Logger.log(`[EMERGENCIAS] Iniciando EMERGENCIAS para DNI: ${dni}`)

    const response: Record<string, IEmerResponse> = {}
    const emerRes: IBeneficiaries[] = await GetBeneficiaries(dni)
    if(!emerRes.length) {
      Logger.log(`[EMERGENCIAS] No se encontraron beneficiarios para DNI: ${dni}`)
      return {}
    }
    Logger.log(`[EMERGENCIAS] Se encontraron ${emerRes.length} beneficiarios para DNI: ${dni}`)

    // Si recibe patientCoverage, seteamos el plan que coincida con esa coverage ya cargada
    if (patientCoverage) {
      Logger.log(`[EMERGENCIAS] Seleccionando plan para cobertura existente: ${patientCoverage.id}`)

      response[patientCoverage.id] = selectCoveragePlan(emerRes, patientCoverage)
      return response
    }
    Logger.log(`[EMERGENCIAS] Respuesta EMERGENCIAS generada exitosamente para DNI: ${dni}`)

    // De lo contrario, seteamos el primer plan encontrado en la API
    response[emerRes[0]?.corporate?.toUpperCase().trim()] = {
      affiliateNumber: emerRes[0]?.affiliateNumber,
      plan: emerRes[0]?.plan,
      lastUpdate: moment().utc().toDate(),
      taxTreatment: emerRes[0]?.taxTreatment,
    }
    return response
  } catch (err) {
    throw new InternalServerErrorException(`[ Coverages | patient | EmergenciasAPI ] => DNI: ${dni} - err: ${err.message || err}}`)
  }
}
