import { countries } from "@umahealth/entities"
import { IStartCallRequest } from "src/doctor-app/guardia/appointments.interfaces"

export interface IPatientBloc {
  saveDniImage(uid: string, service: string, assignationId: string, filename: string, country: countries): Promise<boolean>
}
export interface IUpdateOldActiveServices extends IStartCallRequest {
    room: string,
    token: string,
    startDate: Date
}

export interface IUpdateNewActiveServices {
  activeUid: string,
  appointmentPath: string,
  assignationId: string,
  cuit: string,
  isDependant: boolean,
  room: string,
  token: string,
  uid: string,
}