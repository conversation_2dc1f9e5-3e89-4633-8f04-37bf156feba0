import { Timestamp } from "@google-cloud/firestore"
import { NotFoundException } from "@nestjs/common"
import {getUserByUid} from "@umahealth/auth"
import { IPortalUser } from "@umahealth/entities"
import { getMedicalRecord } from "src/utils/airtable"

export const getUserMedicalRecord = async (uid: string, coach: IPortalUser<Timestamp>) => {
  const user = await getUserByUid(uid)
  if(!user){
    throw new NotFoundException(`[ Nom035 | Responses To Coach ] User with uid: ${uid} not found`)
  }
  return await getMedicalRecord(user.email, coach)

}
