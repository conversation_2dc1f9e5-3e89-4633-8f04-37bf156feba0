import { FieldSet } from "airtable"
import { DomainsC3GeneralWithScores, domainIds, domainMaxScores } from "src/utils/airtable/statistics"
import { Logger } from "@nestjs/common"
import { IFormFilters, INom035Form } from "src/portal-app/nom035/statistics/statistics.entities"
import { getUsersWithFormByDate } from "../../utils/functions"

interface QuestionsByDimensions {
  [dimension: string]: number[]
}

interface ResultadosDimensiones {
  [dimension: string]: number[]
}

export const getC3GeneralDomainsStatistics = (
  usersArray: FieldSet[],
  c3Responses: FieldSet[],
  c3Questions: FieldSet[],
  filters?: IFormFilters,
  category?: string
) => {
  Logger.log("[ Nom035 | Download Report] Getting C3 general domains statistics")

  const normalizeCategory =  category === "Organizacion del tiempo de trabajo" ? "Organización del tiempo de trabajo" : category

  const users = filters?.date ? getUsersWithFormByDate(usersArray, c3Responses, INom035Form.c3, filters.date) : usersArray.filter(user => user.c3 && (user.c3 as string[]).length !== 0)
  const totalResponses = users.length
  const treshold_amount = Math.floor(usersArray.length * 0.1)

  if ((totalResponses < treshold_amount && !filters?.area && !filters?.branch) || totalResponses === 0) {
    return { enoughResponses: false, domains: [] }
  }

  const questionsByDimensions: QuestionsByDimensions = {}
  const domains: DomainsC3GeneralWithScores[] = []
  const filteredQuestions = normalizeCategory ? c3Questions.filter(question => (question.categoria as string).includes(normalizeCategory)) : c3Questions

  filteredQuestions.forEach(item => {
    const domainMatch = (item["dominio"] as string).match(/do10|do[1-9]/)
    if (!domainMatch) return

    const domainId = domainMatch[0] as domainIds
    const domainName = (item["dominio"] as string).slice(domainMatch.index + domainId.length).trim()
    const dimension = (item["dimensiones"] as string).split(" ").slice(1).join(" ")

    let domain = domains.find(d => d.fullString === item["dominio"])
    if (!domain) {
      domain = {
        id: domainId,
        domain: domainName,
        fullString: item["dominio"] as string,
        maxScorePercollaborator: domainMaxScores[domainId].maxScore,
        maxScore: domainMaxScores[domainId].maxScore * totalResponses,
        score: 0,
        totalScore: 0,
        totalPercentage: 0,
        highRisk: 0,
        labelResult: "",
        definition: domainMaxScores[domainId].definition,
        dimensions: []
      }
      domains.push(domain)
    }

    if (!domain.dimensions.some(d => d.dimension === dimension)) {
      domain.dimensions.push({ dimension, result: 0 })
    }

    questionsByDimensions[dimension] = questionsByDimensions[dimension] || []
    questionsByDimensions[dimension].push(item["num preg"] as number)
  })

  const resultadosDimensiones: ResultadosDimensiones = {}

  users.forEach(user => {
    const c3FormCompleted = user.c3 as string[] || []
    if (!c3FormCompleted.length) { return }

    const lastId = c3FormCompleted[c3FormCompleted.length - 1]
    const record = c3Responses.find(record => record.id === lastId)

    domains.forEach(item => {
      const domainScore = getDomainScore(record, item.fullString)
      item.totalScore = item.totalScore + domainScore
      item.score = Number((item.totalScore / totalResponses).toFixed(2))
      item.highRisk = item.highRisk + (record[`resultado ${item.id}`] === "Muy alto" ? 1 : 0)
    })

    Object.entries(questionsByDimensions).forEach(([dimension, questionArray]) => {
      const totalPercentage = questionArray.reduce((sum, num) => {
        const puntaje = record[`puntaje p${num}`] as number
        return sum + (puntaje / 4 * 100)
      }, 0)

      resultadosDimensiones[dimension] = resultadosDimensiones[dimension] || []
      resultadosDimensiones[dimension].push(totalPercentage / questionArray.length)
    })
  })

  domains.forEach(dom => {
    dom.totalPercentage = Number(((dom.totalScore / dom.maxScore) * 100).toFixed(2))
    const prom = dom.totalScore / totalResponses

    const limits = domainMaxScores[dom.id as domainIds]["limits"]
    dom.labelResult = Object.entries(limits).find(([num]) => prom < +num)?.[1] || ""

    dom.labelLimits = Object.fromEntries(
      Object.entries(limits).map(([key, value]) =>
        [((+key - 1) * 100 / +dom.maxScorePercollaborator).toFixed(0), value]
      )
    )

    dom.dimensions.forEach(dimension => {
      const dimensionResults = resultadosDimensiones[dimension.dimension]
      dimension.result = Number((dimensionResults?.reduce((prev, c) => prev + c, 0) / dimensionResults?.length).toFixed(2))
    })
  })

  return { enoughResponses: true, domains }
}

function getDomainScore(record: FieldSet, fullString: string): number {
  switch(fullString) {
  case "do1 Condiciones en el ambiente de trabajo":
    return Number(record["do1  Condiciones en el ambiente de trabajo"])
  case "do2 Carga de trabajo":
    return Number(record["do2 Carga de Trabajo"])
  case "do10 Insuficiente sentido de pertenencia e inestabilidad":
    return Number(record["do10 Insuficiente sentido de pertenencia e, inestabilidad"])
  default:
    return Number(record[fullString])
  }
}
