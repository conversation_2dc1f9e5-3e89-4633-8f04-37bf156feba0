import { NotFoundException } from "@nestjs/common"
import { appointmentServices, countries} from "@umahealth/entities"
import { AppointmentRepository } from "@umahealth/repositories"

export async function getAppointmentById<T>(
  service: appointmentServices,
  country: countries,
  assignationId: string
): Promise<T> {
  const appointment = await AppointmentRepository.getByAssignationId<T>(service, country, assignationId)
  if (!appointment) throw new NotFoundException(`[ Appointment | getAppointmentById ] => Could not get appointment doc with id ${assignationId}`)
  return appointment
}