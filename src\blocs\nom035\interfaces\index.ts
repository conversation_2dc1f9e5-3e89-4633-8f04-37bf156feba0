import { INom035User } from "@umahealth/entities"
import { INom035UserPosition, INom035UserProfile } from "src/portal-app/nom035/user/user.entities"

/* (Carpeta temporal) Despues hay que mover a package entities */
export interface ICreateNom035User {
  profile: INom035Person,
  position: INom035CorporatePosition
}

export interface ICreateNomForm {
  corporate: string,
  email: string,
  form: string,
  questionsAndAnswers: {[key: string]: unknown}
}

// nom035Person incluye datos de uid | id que son de firestore y la sql.
// nom035Profile sólo tiene datos del usuario que vienen desde el front para la creación
export interface INom035Person extends INom035UserProfile{
  id?: number
  uid?: string
  phoneNumber?: string
}

export interface INom035CorporatePosition extends INom035UserPosition {
  userId?: number
  corporateId?: string
}

export interface INom035_csvBodyRowData {
  "Nombre Y Apellidos": string;
  "Número de identificación (CURP)": string;
  "Fecha de nacimiento": string;
  "Sexo que figura en su identificación": "M" | "F" | "X";
  "Correo electrónico": string;
  "Teléfono": string;
  "Sucursales": string;
  "Área": string;
  "Seniority": string;
}

export class INom035_csvBodyRow {
  id?: string
  uid?: string
  fullname: string
  nationalId: string
  birthDate: string
  gender: "M" | "F" | "X"
  email: string
  phone: string
  branch?: string
  area?: string
  seniority?: string

  constructor(row: INom035_csvBodyRowData) {
    this.fullname = row["Nombre Y Apellidos"] || ""
    this.nationalId = row["Número de identificación (CURP)"] || ""
    this.birthDate = row["Fecha de nacimiento"] || ""
    this.gender = row["Sexo que figura en su identificación"] as "M" | "F" | "X"
    this.email = row["Correo electrónico"] || `${row["Número de identificación (CURP)"]}@no-mail.com`
    this.phone = row["Teléfono"] || ""
    this.area = row["Área"] || ""
    this.seniority = row["Seniority"] || "N/a"
    this.branch = row["Sucursales"] || "Central"
  }
}
export interface INom035_csvBody {
  id?:string | number
  uid?: string
  fullname: string
  nationalId: string
  birthDate: string
  gender: "M" | "F" | "X"
  email: string
  phone?: string
  branch?: string
  area?: string
  seniority?: string
}

export interface INom035UserFilters {
  page?: number
  pageSize?: number
  offset?: number
  searchText?: string
  date?: string
}

export interface INom035UserSQL {
  id?: number
  uid: string
  fullname: string
  nationalId: string
  birthDate: string
  gender: string
  email: string
  phoneNumber?: string
  position?: string
  maritalStatus?: string
  active: boolean
}


export interface INom035UserPositionSQL {
  id?: number
  userId?: number
  corporateId?: string
  educationLevel?: string
  workingSchedule?: string
  contractType?: string
  branch?: string
  rotationalShifts?: boolean
  hiringDate?: string
  positionDate?: string
  area?: string
  seniority?: string
  photoUrl?: string
  isActive?: boolean
  contractEndDate?: string
  position?: string
  division?: string
}


export interface IUpdateCorporate {
  "address"?: string;
  "category"?: string;
  "contactEmail"?: string;
  "contactFullname"?: string;
  "contactPhone"?: string;
  "employees"?: number;
  "mainActivity"?: string;
  "rfc"?: string
}


export interface IUpdateCorporateAirtable {
  "actividad principal"?: string;
  "domicilio"?: string;
  "contacto empresa"?: string;
  "correo contacto empresa"?: string;
  "nomina empleados"?: number;
  "telefono contacto empresa"?: string;
  "rfc"?: string;
  "rubro"?: string;
}

export interface INom035UserWithFormStatus extends INom035User {
  dataValues: {
    nationalId: string
    corporateId: string
    contractType: string
    workingSchedule: string
    rotationalShifts: string
    gender: string
    fullname: string
    positions: {
      area: string,
      dataValues?: object
    }[],
    age?:number,
    birthDate?: string,
    formStatus: {
      c1: boolean,
      c2: boolean,
      c3: boolean
    },
    hasInforms?: boolean,
    area?:string,
    branch?: string,
    position?: string,
    division?: string,
  }
}

export interface INom035UserInformWithFormStatus extends INom035User {
  dataValues: {
    positions: {
      area: string,
      branch?: string
      position?: string
    }[],
    age?:number,
    birthDate?: string,
    formStatus: {
      name: string,
      completed: boolean
    }[],
    hasInforms?: boolean,
    area?:string,
    branch?: string,
    position?: string,
    division?: string,
    forms?: {
      form: string,
    }[],
  }
}

export interface INom035UserInform {
  fullname: string,
  gender: string,
  area: string,
  branch: string,
  position: string,
  nationalId: string,
  division: string,
  birthdate: Date,
  corporateId: string,
  contractType: string,
  workingSchedule: string,
  rotationalShifts: string
}

export interface INom035UsersToCoach extends INom035User {
  dataValues: {
    positions: {
      corporateId: string
    }[],
    lastInteraction?:string
    corporateId?: string
  }
}


export interface AirtableUserProps {
  "nomina": string;
  "INE / FM": string;
  "fecha nacimiento": string;
  "genero": "Masculino" | "Femenino" | "Sin especificar";
  "c trabajo": string;
  "correo": string;
  "telefono": string;
  "estado civil"?: string;
  "nivel de estudios (completos)"?: string;
  "tipo de contratacion"?: string;
  "tipo de jornada"?: string;
  "rotacion turno": "Si" | "No";
  "fecha ingreso empresa"?: string;
  "fecha inicio posicion actual"?: string;
  "area"?: string;
  "seniority"?: string;
  "empresa": string[]
  "activo": boolean;
  "puesto"?: string;
  "división"?: string;
}

export interface CategoriesC3WithScores {
  [category: string]: {
    percentage: number
    average: number,
    maxValue: number
  }
}

export interface IExtraFilters {
  filterByName?: string,
  filterByArea?: string,
  filterByState?: boolean,
  filterByCuestionarios?: string,
  filterByDate?: string,
  page: number,
}

export type ICacheType = "corporate" | "users" | "indexQuestions" | "indexResponses" | "c1Questions" | "c1Responses" | "c3Questions" | "c3Responses"

export const Gender = {
  M: "M",
  F: "F",
  X: "X",
} as const

export const GenderTypes = {
  [Gender.M]: "Masculino",
  [Gender.F]: "Femenino",
  [Gender.X]: "Sin especificar"
} as const

export interface INom035PersonWithActive extends INom035Person {
  active: boolean
}

export interface IAuthUserResult {
  uid: string;
  success: boolean;
}

export interface UserWithPosition {
  gender: string;
  birthDate: Date;
  "positions.area": string;
  "positions.branch": string;
}
