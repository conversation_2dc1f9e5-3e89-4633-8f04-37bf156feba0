import { Test } from "@nestjs/testing"
import { NotificationsBloc } from "../notifications.bloc"
import { NotificationsBlocModule } from "../notifications.module"


describe("SubscriptionsModule", () => {
    let notificationsBloc: NotificationsBloc

    beforeEach(async () => {
        const moduleRef = await Test.createTestingModule({
            imports: [NotificationsBlocModule],
        }).compile()

        notificationsBloc = moduleRef.get<NotificationsBloc>(NotificationsBloc)
    })

    it("should be defined", () => {
        expect(notificationsBloc).toBeDefined()
    })
})