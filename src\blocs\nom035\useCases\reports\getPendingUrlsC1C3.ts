import { Logger } from "@nestjs/common"
import { FieldSet } from "airtable"
import { getMultipleAirtableFormLinks } from "src/utils/airtable"

const isWithinLast13Months = (dateString: string): boolean => {
  const [year, month] = dateString.split("_").map(Number)
  const today = new Date()
  const thirteenMonthsAgo = new Date(today.setMonth(today.getMonth() - 13))
  const responseDate = new Date(year, month - 1)
  return responseDate >= thirteenMonthsAgo
}

export const getPendingFormUrls = async (users: FieldSet[], c1Responses: FieldSet[], c3Responses: FieldSet[]) => {
  Logger.log(`[ Nom035 | ${getPendingFormUrls.name}] Getting pending C1 and C3 URLs"`)

  const userEmailMap = new Map(
    users.map(user => [user["correo"].toString(), user["nomina"]])
  )

  const c1ResponseList = new Set(
    c1Responses
      .filter(response => isWithinLast13Months(response["año mes"].toString()))
      .map(response => (response["nombre y apellido (from nomina)"] as string[])?.[0])
  )

  const c3ResponseList = new Set(
    c3Responses
      .filter(response => isWithinLast13Months(response["año mes"].toString()))
      .map(response => (response["nombre y apellido (from nomina)"] as string[])?.[0])
  )

  const usersWithPendingForms = Array.from(userEmailMap.entries())
    .map(([email, nomina]) => ({
      email,
      setC1: !c1ResponseList.has(nomina as string),
      setC3: !c3ResponseList.has(nomina as string)
    }))
    .filter(user => user.setC1 || user.setC3)

  const data = await getMultipleAirtableFormLinks(usersWithPendingForms.map(user => user.email))

  return {
    urls: data.map((user) => {
      const userForm = usersWithPendingForms.find(item => item.email === user.email)
      return ({
        email: user.email,
        c1Url: userForm.setC1 ? user.c1Url : "",
        c3Url: userForm.setC3 ? user.c3Url : "",
      })})
  }
}
